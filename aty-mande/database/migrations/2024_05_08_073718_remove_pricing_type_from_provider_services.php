<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Supprimer la colonne pricing_type seulement si elle existe
        if (Schema::hasColumn('provider_services', 'pricing_type')) {
            Schema::table('provider_services', function (Blueprint $table) {
                $table->dropColumn('pricing_type');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Ajouter la colonne pricing_type
        Schema::table('provider_services', function (Blueprint $table) {
            $table->enum('pricing_type', ['fixed', 'hourly', 'per_person', 'custom', 'quote'])->default('quote')->after('description');
        });
    }
};
