<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Cette migration effectue les opérations suivantes :
     * 1. Ajoute une colonne 'details' si elle n'existe pas déjà
     * 2. Copie les données de 'options' vers 'details' en supprimant les informations de prix
     * 3. Supprime la colonne 'options'
     */
    public function up(): void
    {
        // Vérifier si la colonne 'details' existe déjà
        if (!Schema::hasColumn('provider_services', 'details')) {
            // Si la colonne 'details' n'existe pas, la créer
            Schema::table('provider_services', function (Blueprint $table) {
                // Déterminer après quelle colonne ajouter 'details'
                if (Schema::hasColumn('provider_services', 'options')) {
                    $table->json('details')->nullable()->after('options');
                } else {
                    $table->json('details')->nullable()->after('price');
                }
            });
        }

        // Vérifier si la colonne 'options' existe
        if (Schema::hasColumn('provider_services', 'options')) {
            // Étape 2: Copier les données de 'options' vers 'details' en supprimant les informations de prix
            $services = DB::table('provider_services')->get();
            foreach ($services as $service) {
                $options = $service->options;
                $details = [];

                if (is_string($options) && !empty($options)) {
                    try {
                        $optionsArray = json_decode($options, true);
                        if (json_last_error() === JSON_ERROR_NONE && is_array($optionsArray)) {
                            // Supprimer toutes les clés liées aux prix
                            $priceKeys = [
                                'base_price', 'price', 'min_price', 'max_price', 'pricing_type',
                                'is_negotiable', 'hourly_rate', 'per_person_price'
                            ];

                            foreach ($optionsArray as $key => $value) {
                                // Ne pas copier les clés liées aux prix
                                if (!in_array($key, $priceKeys)) {
                                    $details[$key] = $value;
                                }
                            }

                            // Si le service a des éléments de menu (pour les traiteurs), supprimer les prix des éléments
                            if (isset($details['menu_items']) && is_array($details['menu_items'])) {
                                foreach ($details['menu_items'] as &$item) {
                                    if (is_array($item) && isset($item['price'])) {
                                        // Conserver le prix dans la colonne price si c'est le premier élément et que le prix principal n'est pas défini
                                        if (!$service->price && isset($details['menu_items'][0]) && $item === $details['menu_items'][0]) {
                                            DB::table('provider_services')
                                                ->where('id', $service->id)
                                                ->update(['price' => $item['price']]);
                                        }

                                        // Supprimer le prix de l'élément de menu
                                        unset($item['price']);
                                    }
                                }
                            }

                            // Mettre à jour la ligne avec les détails sans les prix
                            DB::table('provider_services')
                                ->where('id', $service->id)
                                ->update(['details' => json_encode($details)]);
                        }
                    } catch (\Exception $e) {
                        // En cas d'erreur, initialiser avec un tableau vide
                        DB::table('provider_services')
                            ->where('id', $service->id)
                            ->update(['details' => json_encode([])]);
                    }
                }
            }

            // Étape 3: Supprimer la colonne 'options'
            Schema::table('provider_services', function (Blueprint $table) {
                $table->dropColumn('options');
            });
        } else {
            // Si la colonne 'options' n'existe pas, vérifier si 'details' existe déjà
            if (Schema::hasColumn('provider_services', 'details')) {
                // Nettoyer les informations de prix dans la colonne 'details'
                $services = DB::table('provider_services')->get();
                foreach ($services as $service) {
                    if ($service->details) {
                        try {
                            $detailsArray = json_decode($service->details, true);
                            if (json_last_error() === JSON_ERROR_NONE && is_array($detailsArray)) {
                                // Supprimer toutes les clés liées aux prix
                                $priceKeys = [
                                    'base_price', 'price', 'min_price', 'max_price', 'pricing_type',
                                    'is_negotiable', 'hourly_rate', 'per_person_price'
                                ];

                                $modified = false;
                                foreach ($priceKeys as $key) {
                                    if (isset($detailsArray[$key])) {
                                        unset($detailsArray[$key]);
                                        $modified = true;
                                    }
                                }

                                // Si le service a des éléments de menu (pour les traiteurs), supprimer les prix des éléments
                                if (isset($detailsArray['menu_items']) && is_array($detailsArray['menu_items'])) {
                                    foreach ($detailsArray['menu_items'] as &$item) {
                                        if (is_array($item) && isset($item['price'])) {
                                            // Conserver le prix dans la colonne price si c'est le premier élément et que le prix principal n'est pas défini
                                            if (!$service->price && isset($detailsArray['menu_items'][0]) && $item === $detailsArray['menu_items'][0]) {
                                                DB::table('provider_services')
                                                    ->where('id', $service->id)
                                                    ->update(['price' => $item['price']]);
                                            }

                                            // Supprimer le prix de l'élément de menu
                                            unset($item['price']);
                                            $modified = true;
                                        }
                                    }
                                }

                                // Mettre à jour la ligne avec les détails sans les prix
                                if ($modified) {
                                    DB::table('provider_services')
                                        ->where('id', $service->id)
                                        ->update(['details' => json_encode($detailsArray)]);
                                }
                            }
                        } catch (\Exception $e) {
                            // En cas d'erreur, ne rien faire
                        }
                    }
                }
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Cette migration est irréversible car elle supprime des données (les informations de prix)
        // Nous ne pouvons pas restaurer les informations de prix qui ont été supprimées

        // Cependant, nous pouvons restaurer la structure de la base de données

        // Étape 1: Ajouter une colonne 'options' si elle n'existe pas
        if (!Schema::hasColumn('provider_services', 'options')) {
            Schema::table('provider_services', function (Blueprint $table) {
                $table->json('options')->nullable()->after('price');
            });
        }

        // Étape 2: Copier les données de 'details' vers 'options' (sans restaurer les prix)
        if (Schema::hasColumn('provider_services', 'details')) {
            $services = DB::table('provider_services')->get();
            foreach ($services as $service) {
                if ($service->details) {
                    // Mettre à jour la ligne
                    DB::table('provider_services')
                        ->where('id', $service->id)
                        ->update(['options' => $service->details]);
                }
            }

            // Étape 3: Supprimer la colonne 'details'
            Schema::table('provider_services', function (Blueprint $table) {
                $table->dropColumn('details');
            });
        }
    }
};
