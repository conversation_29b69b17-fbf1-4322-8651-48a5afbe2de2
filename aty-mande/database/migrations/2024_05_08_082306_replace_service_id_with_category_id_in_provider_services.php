<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Vérifier si la colonne category_id existe déjà
        if (!Schema::hasColumn('provider_services', 'category_id')) {
            // Ajouter la colonne category_id
            Schema::table('provider_services', function (Blueprint $table) {
                $table->unsignedBigInteger('category_id')->nullable()->after('provider_id');
                $table->foreign('category_id')->references('id')->on('categories')->onDelete('cascade');
            });
        }

        // Vérifier si la colonne service_id existe encore
        if (Schema::hasColumn('provider_services', 'service_id')) {
            // Migrer les données : récupérer la catégorie de chaque service et la stocker dans category_id
            $providerServices = DB::table('provider_services')->get();
            foreach ($providerServices as $providerService) {
                if (isset($providerService->service_id)) {
                    $service = DB::table('services')->find($providerService->service_id);
                    if ($service) {
                        DB::table('provider_services')
                            ->where('id', $providerService->id)
                            ->update(['category_id' => $service->category_id]);
                    }
                }
            }

            // Supprimer la colonne service_id et sa contrainte de clé étrangère
            Schema::table('provider_services', function (Blueprint $table) {
                // Vérifier si la contrainte de clé étrangère existe
                $foreignKeys = $this->listTableForeignKeys('provider_services');
                if (in_array('provider_services_service_id_foreign', $foreignKeys)) {
                    $table->dropForeign(['service_id']);
                }
                $table->dropColumn('service_id');
            });
        }
    }

    /**
     * Get a list of foreign keys for a table
     */
    protected function listTableForeignKeys($table)
    {
        $conn = Schema::getConnection()->getDoctrineSchemaManager();
        return array_map(function($key) {
            return $key->getName();
        }, $conn->listTableForeignKeys($table));
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Ajouter la colonne service_id
        Schema::table('provider_services', function (Blueprint $table) {
            $table->unsignedBigInteger('service_id')->nullable()->after('provider_id');
            $table->foreign('service_id')->references('id')->on('services')->onDelete('cascade');
        });

        // Supprimer la colonne category_id et sa contrainte de clé étrangère
        Schema::table('provider_services', function (Blueprint $table) {
            $table->dropForeign(['category_id']);
            $table->dropColumn('category_id');
        });
    }
};
