<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Étape 1: Ajouter une nouvelle colonne 'details'
        Schema::table('provider_services', function (Blueprint $table) {
            $table->json('details')->nullable()->after('options');
        });

        // Étape 2: Copier les données de 'options' vers 'details'
        $services = DB::table('provider_services')->get();
        foreach ($services as $service) {
            $options = $service->options;
            
            // Extraire le prix de base des options
            $basePrice = 0;
            $details = $options;
            
            if (is_string($options) && !empty($options)) {
                try {
                    $optionsArray = json_decode($options, true);
                    if (json_last_error() === JSON_ERROR_NONE && is_array($optionsArray)) {
                        // Si base_price existe, l'extraire
                        if (isset($optionsArray['base_price'])) {
                            $basePrice = $optionsArray['base_price'];
                            unset($optionsArray['base_price']);
                        }
                        
                        // Mettre à jour les détails sans le prix
                        $details = json_encode($optionsArray);
                    }
                } catch (\Exception $e) {
                    // En cas d'erreur, garder les options telles quelles
                }
            }
            
            // Mettre à jour la ligne
            DB::table('provider_services')
                ->where('id', $service->id)
                ->update(['details' => $details]);
        }

        // Étape 3: Supprimer l'ancienne colonne 'options'
        Schema::table('provider_services', function (Blueprint $table) {
            $table->dropColumn('options');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Étape 1: Ajouter une nouvelle colonne 'options'
        Schema::table('provider_services', function (Blueprint $table) {
            $table->json('options')->nullable()->after('description');
        });

        // Étape 2: Copier les données de 'details' vers 'options'
        $services = DB::table('provider_services')->get();
        foreach ($services as $service) {
            $details = $service->details;
            
            // Mettre à jour la ligne
            DB::table('provider_services')
                ->where('id', $service->id)
                ->update(['options' => $details]);
        }

        // Étape 3: Supprimer la nouvelle colonne 'details'
        Schema::table('provider_services', function (Blueprint $table) {
            $table->dropColumn('details');
        });
    }
};
