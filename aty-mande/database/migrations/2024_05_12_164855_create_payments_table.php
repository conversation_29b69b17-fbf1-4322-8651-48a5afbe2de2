<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Skip if the table already exists
        if (Schema::hasTable('payments')) {
            // Update the status and payment_type fields to have default values if needed
            Schema::table('payments', function (Blueprint $table) {
                // Check if the columns exist and modify them
                if (Schema::hasColumn('payments', 'status')) {
                    $table->string('status')->default('completed')->change();
                }
                if (Schema::hasColumn('payments', 'payment_type')) {
                    $table->string('payment_type')->default('full')->change();
                }
            });
            return;
        }

        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->foreignId('booking_id')->constrained()->onDelete('cascade');
            $table->foreignId('invoice_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('payment_method_id')->constrained()->onDelete('restrict');
            $table->string('transaction_id')->nullable();
            $table->string('reference_number')->unique();
            $table->decimal('amount', 10, 2);
            $table->string('currency')->default('MGA');
            $table->string('status')->default('completed'); // pending, completed, failed, refunded
            $table->string('payment_type')->default('full'); // deposit, balance, full
            $table->json('payment_details')->nullable();
            $table->text('notes')->nullable();
            $table->timestamp('paid_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Don't drop the table in down() since it might have been created by another migration
    }
};
