<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Skip if the table already exists
        if (Schema::hasTable('event_type_service_categories')) {
            return;
        }

        Schema::create('event_type_service_categories', function (Blueprint $table) {
            $table->id();
            $table->foreignId('event_type_id')->constrained()->onDelete('cascade');
            $table->foreignId('category_id')->constrained()->onDelete('cascade');
            $table->boolean('is_required')->default(false);
            $table->integer('priority')->default(0);
            $table->text('description')->nullable();
            $table->timestamps();

            // Unique constraint to prevent duplicate entries
            $table->unique(['event_type_id', 'category_id']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('event_type_service_categories');
    }
};
