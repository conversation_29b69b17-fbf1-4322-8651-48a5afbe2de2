<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

// Changed timestamp to avoid conflict with rename_options_to_details_in_provider_services migration
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Skip if the table already exists
        if (Schema::hasTable('service_prices')) {
            return;
        }

        Schema::create('service_prices', function (Blueprint $table) {
            $table->id();
            $table->foreignId('provider_service_id')->constrained()->onDelete('cascade');
            $table->decimal('price', 10, 2);
            $table->enum('price_type', ['fixed', 'hourly', 'per_person', 'per_unit'])->default('fixed');
            $table->integer('min_quantity')->default(1);
            $table->integer('max_quantity')->nullable();
            $table->boolean('is_negotiable')->default(false);
            $table->boolean('is_primary')->default(true);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('service_prices');
    }
};
