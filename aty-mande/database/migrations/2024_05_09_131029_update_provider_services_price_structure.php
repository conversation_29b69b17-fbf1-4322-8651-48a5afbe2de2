<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Mettre à jour la structure de prix dans le champ options JSON
        $services = DB::table('provider_services')->get();

        foreach ($services as $service) {
            try {
                // Vérifier si options est une chaîne JSON valide
                if (is_string($service->options) && !empty($service->options)) {
                    $options = json_decode($service->options, true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        // Si ce n'est pas un JSON valide, initialiser un tableau vide
                        $options = [];
                    }
                } else {
                    $options = [];
                }

                // Créer un nouveau tableau d'options
                $newOptions = [];

                // Conserver les éléments de menu existants
                if (isset($options['menu_items']) && is_array($options['menu_items'])) {
                    $newOptions['menu_items'] = $options['menu_items'];
                }

                // Extraire le prix de base
                $basePrice = 0;
                if (isset($options['price']) && is_numeric($options['price'])) {
                    $basePrice = $options['price'];
                }

                // Ajouter le nouveau champ de prix de base
                $newOptions['base_price'] = $basePrice;

                // Conserver les autres options qui ne sont pas liées au prix
                foreach ($options as $key => $value) {
                    if (!in_array($key, ['price', 'pricing_type', 'min_price', 'max_price', 'menu_items'])) {
                        $newOptions[$key] = $value;
                    }
                }

                // Mettre à jour les options
                DB::table('provider_services')
                    ->where('id', $service->id)
                    ->update(['options' => json_encode($newOptions)]);
            } catch (\Exception $e) {
                // En cas d'erreur, initialiser avec un tableau vide contenant uniquement base_price
                DB::table('provider_services')
                    ->where('id', $service->id)
                    ->update(['options' => json_encode(['base_price' => 0])]);
            }
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Restaurer l'ancienne structure de prix
        $services = DB::table('provider_services')->get();

        foreach ($services as $service) {
            try {
                // Vérifier si options est une chaîne JSON valide
                if (is_string($service->options) && !empty($service->options)) {
                    $options = json_decode($service->options, true);
                    if (json_last_error() !== JSON_ERROR_NONE) {
                        // Si ce n'est pas un JSON valide, initialiser un tableau vide
                        $options = [];
                    }
                } else {
                    $options = [];
                }

                // Créer un nouveau tableau d'options
                $oldOptions = [];

                // Conserver les éléments de menu existants
                if (isset($options['menu_items']) && is_array($options['menu_items'])) {
                    $oldOptions['menu_items'] = $options['menu_items'];
                }

                // Extraire le prix de base
                $basePrice = 0;
                if (isset($options['base_price']) && is_numeric($options['base_price'])) {
                    $basePrice = $options['base_price'];
                }

                // Restaurer les anciens champs de prix
                $oldOptions['price'] = $basePrice;
                $oldOptions['pricing_type'] = 'fixed';

                // Conserver les autres options qui ne sont pas liées au prix
                foreach ($options as $key => $value) {
                    if ($key !== 'base_price' && $key !== 'menu_items') {
                        $oldOptions[$key] = $value;
                    }
                }

                // Mettre à jour les options
                DB::table('provider_services')
                    ->where('id', $service->id)
                    ->update(['options' => json_encode($oldOptions)]);
            } catch (\Exception $e) {
                // En cas d'erreur, initialiser avec un tableau vide contenant uniquement price et pricing_type
                DB::table('provider_services')
                    ->where('id', $service->id)
                    ->update(['options' => json_encode(['price' => 0, 'pricing_type' => 'fixed'])]);
            }
        }
    }
};
