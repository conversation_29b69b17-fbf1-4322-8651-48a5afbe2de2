<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('quotes', function (Blueprint $table) {
            // Ajouter les nouveaux champs pour le calcul détaillé du devis
            $table->decimal('base_amount', 10, 2)->after('description')->default(0);
            $table->json('options')->after('base_amount')->nullable();
            $table->decimal('discount_amount', 10, 2)->after('options')->default(0);
            $table->decimal('subtotal_amount', 10, 2)->after('discount_amount')->default(0);
            $table->decimal('tax_rate', 5, 2)->after('subtotal_amount')->default(0);
            $table->decimal('tax_amount', 10, 2)->after('tax_rate')->default(0);
            $table->text('notes')->after('terms_and_conditions')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('quotes', function (Blueprint $table) {
            // Supprimer les champs ajoutés
            $table->dropColumn([
                'base_amount',
                'options',
                'discount_amount',
                'subtotal_amount',
                'tax_rate',
                'tax_amount',
                'notes'
            ]);
        });
    }
};
