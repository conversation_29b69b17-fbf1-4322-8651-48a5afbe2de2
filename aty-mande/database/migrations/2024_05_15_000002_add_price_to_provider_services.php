<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Étape 1: Ajouter les colonnes de prix
        Schema::table('provider_services', function (Blueprint $table) {
            $table->decimal('price', 10, 2)->nullable()->after('description');
            $table->boolean('is_negotiable')->default(false)->after('price');
        });

        // Étape 2: Migrer les données de prix depuis les options
        $services = DB::table('provider_services')->get();
        foreach ($services as $service) {
            $options = $service->options;
            $basePrice = 0;
            $isNegotiable = false;
            
            if (is_string($options) && !empty($options)) {
                try {
                    $optionsArray = json_decode($options, true);
                    if (json_last_error() === JSON_ERROR_NONE && is_array($optionsArray)) {
                        // Extraire le prix de base
                        if (isset($optionsArray['base_price'])) {
                            $basePrice = $optionsArray['base_price'];
                        }
                        
                        // Vérifier si le prix est négociable
                        if (isset($optionsArray['is_negotiable'])) {
                            $isNegotiable = (bool)$optionsArray['is_negotiable'];
                        }
                    }
                } catch (\Exception $e) {
                    // En cas d'erreur, utiliser les valeurs par défaut
                }
            }
            
            // Mettre à jour la ligne avec le prix
            DB::table('provider_services')
                ->where('id', $service->id)
                ->update([
                    'price' => $basePrice,
                    'is_negotiable' => $isNegotiable
                ]);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Supprimer les colonnes de prix
        Schema::table('provider_services', function (Blueprint $table) {
            $table->dropColumn(['price', 'is_negotiable']);
        });
    }
};
