<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     *
     * Supprime les tables liées aux devis et les références aux devis dans d'autres tables
     */
    public function up(): void
    {
        // Supprimer les références aux devis dans la table bookings
        if (Schema::hasColumn('bookings', 'quote_id')) {
            Schema::table('bookings', function (Blueprint $table) {
                $table->dropForeign(['quote_id']);
                $table->dropColumn('quote_id');
            });
        }

        // Supprimer les références aux devis dans la table messages
        if (Schema::hasColumn('messages', 'quote_id') || Schema::hasColumn('messages', 'quote_request_id')) {
            Schema::table('messages', function (Blueprint $table) {
                if (Schema::hasColumn('messages', 'quote_id')) {
                    $table->dropForeign(['quote_id']);
                    $table->dropColumn('quote_id');
                }

                if (Schema::hasColumn('messages', 'quote_request_id')) {
                    $table->dropForeign(['quote_request_id']);
                    $table->dropColumn('quote_request_id');
                }
            });
        }

        // Supprimer les tables liées aux devis
        Schema::dropIfExists('quotes');
        Schema::dropIfExists('quote_requests');
    }

    /**
     * Reverse the migrations.
     *
     * Cette migration est irréversible car elle supprime des données
     */
    public function down(): void
    {
        // Cette migration est irréversible
    }
};
