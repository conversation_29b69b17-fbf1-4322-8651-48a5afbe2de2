<?php

namespace Database\Seeders;

use App\Models\ClientsModel\EventType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class EventTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $eventTypes = [
            [
                'name' => 'Wedding',
                'description' => 'Wedding ceremonies and receptions',
                'icon' => 'fa-solid fa-rings-wedding',
                'is_active' => true,
            ],
            [
                'name' => 'Birthday Party',
                'description' => 'Birthday celebrations for all ages',
                'icon' => 'fa-cake-candles',
                'is_active' => true,
            ],
            [
                'name' => 'Corporate Event',
                'description' => 'Business meetings, conferences, and corporate gatherings',
                'icon' => 'fa-briefcase',
                'is_active' => true,
            ],
            [
                'name' => 'Graduation',
                'description' => 'Graduation ceremonies and celebrations',
                'icon' => 'fa-graduation-cap',
                'is_active' => true,
            ],
            [
                'name' => 'Anniversary',
                'description' => 'Anniversary celebrations',
                'icon' => 'fa-calendar-heart',
                'is_active' => true,
            ],
            [
                'name' => 'Baby Shower',
                'description' => 'Baby shower celebrations',
                'icon' => 'fa-baby',
                'is_active' => true,
            ],
            [
                'name' => 'Engagement Party',
                'description' => 'Engagement celebrations',
                'icon' => 'fa-ring',
                'is_active' => true,
            ],
            [
                'name' => 'Religious Ceremony',
                'description' => 'Religious ceremonies and celebrations',
                'icon' => 'fa-place-of-worship',
                'is_active' => true,
            ],
            [
                'name' => 'Funeral',
                'description' => 'Funeral services and memorials',
                'icon' => 'fa-cross',
                'is_active' => true,
            ],
            [
                'name' => 'Other',
                'description' => 'Other types of events',
                'icon' => 'fa-calendar',
                'is_active' => true,
            ],
        ];

        foreach ($eventTypes as $eventType) {
            // Check if event type already exists
            $slug = Str::slug($eventType['name']);
            $existingEventType = EventType::where('slug', $slug)->first();

            if (!$existingEventType) {
                EventType::create([
                    'name' => $eventType['name'],
                    'slug' => $slug,
                    'description' => $eventType['description'],
                    'icon' => $eventType['icon'],
                    'is_active' => $eventType['is_active'],
                ]);
            }
        }
    }
}
