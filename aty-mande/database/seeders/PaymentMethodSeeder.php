<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PaymentMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Vérifier si les méthodes de paiement existent déjà
        if (\App\Models\PaymentMethod::count() > 0) {
            $this->command->info('Les méthodes de paiement existent déjà. Seeder ignoré.');
            return;
        }

        $paymentMethods = [
            // Mobile Money
            [
                'name' => 'MVola',
                'code' => 'mvola',
                'type' => 'mobile_money',
                'provider' => 'mvola',
                'logo' => 'payment_methods/mvola.png',
                'description' => 'Paiement via MVola',
                'config' => json_encode([
                    'merchant_number' => '034 00 000 00',
                    'instructions' => 'Envoyez le montant au numéro marchand et entrez le code de transaction.',
                ]),
                'is_active' => true,
                'display_order' => 1,
            ],
            [
                'name' => 'Orange Money',
                'code' => 'orange_money',
                'type' => 'mobile_money',
                'provider' => 'orange_money',
                'logo' => 'payment_methods/orange_money.png',
                'description' => 'Paiement via Orange Money',
                'config' => json_encode([
                    'merchant_number' => '032 00 000 00',
                    'instructions' => 'Envoyez le montant au numéro marchand et entrez le code de transaction.',
                ]),
                'is_active' => true,
                'display_order' => 2,
            ],
            [
                'name' => 'Airtel Money',
                'code' => 'airtel_money',
                'type' => 'mobile_money',
                'provider' => 'airtel_money',
                'logo' => 'payment_methods/airtel_money.png',
                'description' => 'Paiement via Airtel Money',
                'config' => json_encode([
                    'merchant_number' => '033 00 000 00',
                    'instructions' => 'Envoyez le montant au numéro marchand et entrez le code de transaction.',
                ]),
                'is_active' => true,
                'display_order' => 3,
            ],

            // Bank Cards
            [
                'name' => 'Carte Visa',
                'code' => 'visa',
                'type' => 'bank_card',
                'provider' => 'visa',
                'logo' => 'payment_methods/visa.png',
                'description' => 'Paiement par carte Visa',
                'config' => json_encode([
                    'api_key' => 'visa_test_key',
                    'secret' => 'visa_test_secret',
                ]),
                'is_active' => true,
                'display_order' => 4,
            ],
            [
                'name' => 'Mastercard',
                'code' => 'mastercard',
                'type' => 'bank_card',
                'provider' => 'mastercard',
                'logo' => 'payment_methods/mastercard.png',
                'description' => 'Paiement par carte Mastercard',
                'config' => json_encode([
                    'api_key' => 'mastercard_test_key',
                    'secret' => 'mastercard_test_secret',
                ]),
                'is_active' => true,
                'display_order' => 5,
            ],
        ];

        foreach ($paymentMethods as $method) {
            \App\Models\PaymentMethod::create($method);
        }
    }
}
