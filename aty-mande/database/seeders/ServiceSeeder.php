<?php

namespace Database\Seeders;

use App\Models\Service;
use App\Models\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class ServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all categories
        $categories = Category::all();

        if ($categories->isEmpty()) {
            $this->command->info('No categories found. Please run the CategorySeeder first.');
            return;
        }

        $services = [
            // Services de traiteur
            [
                'name' => 'Service Traiteur Complet',
                'description' => 'Service de traiteur complet incluant nourriture, boissons, personnel et équipement',
                'category_name' => 'Traiteur',
                'is_active' => true,
            ],
            [
                'name' => 'Nourriture Uniquement',
                'description' => 'Préparation et livraison de nourriture sans personnel de service',
                'category_name' => 'Traiteur',
                'is_active' => true,
            ],
            [
                'name' => 'Service de Boissons',
                'description' => 'Service de boissons incluant options alcoolisées et non alcoolisées',
                'category_name' => 'Traiteur',
                'is_active' => true,
            ],

            // Services de photographie
            [
                'name' => 'Photographie de Mariage',
                'description' => 'Services de photographie professionnelle pour mariages',
                'category_name' => 'Photographie',
                'is_active' => true,
            ],
            [
                'name' => 'Photographie d\'Événements',
                'description' => 'Photographie professionnelle pour événements d\'entreprise, fêtes et autres occasions',
                'category_name' => 'Photographie',
                'is_active' => true,
            ],
            [
                'name' => 'Photographie de Portrait',
                'description' => 'Services de photographie de portrait professionnelle',
                'category_name' => 'Photographie',
                'is_active' => true,
            ],

            // Services de vidéographie
            [
                'name' => 'Vidéographie de Mariage',
                'description' => 'Services vidéo professionnels pour mariages',
                'category_name' => 'Vidéographie',
                'is_active' => true,
            ],
            [
                'name' => 'Vidéographie d\'Événements',
                'description' => 'Services vidéo professionnels pour événements',
                'category_name' => 'Vidéographie',
                'is_active' => true,
            ],
            [
                'name' => 'Vidéographie par Drone',
                'description' => 'Vidéographie aérienne utilisant des drones',
                'category_name' => 'Vidéographie',
                'is_active' => true,
            ],

            // Services de musique et animation
            [
                'name' => 'Services de DJ',
                'description' => 'Services de DJ professionnels pour événements',
                'category_name' => 'Musique & Animation',
                'is_active' => true,
            ],
            [
                'name' => 'Groupe en Direct',
                'description' => 'Performance musicale en direct par des groupes professionnels',
                'category_name' => 'Musique & Animation',
                'is_active' => true,
            ],
            [
                'name' => 'Services de Maître de Cérémonie',
                'description' => 'Maître de cérémonie professionnel pour événements',
                'category_name' => 'Musique & Animation',
                'is_active' => true,
            ],

            // Services d'espaces de réception
            [
                'name' => 'Espace Intérieur',
                'description' => 'Espaces intérieurs pour événements',
                'category_name' => 'Espaces de Réception',
                'is_active' => true,
            ],
            [
                'name' => 'Espace Extérieur',
                'description' => 'Espaces extérieurs pour événements',
                'category_name' => 'Espaces de Réception',
                'is_active' => true,
            ],
            [
                'name' => 'Espace sur la Plage',
                'description' => 'Espaces sur la plage pour événements',
                'category_name' => 'Espaces de Réception',
                'is_active' => true,
            ],

            // Services de décoration
            [
                'name' => 'Décoration de Mariage',
                'description' => 'Services de décoration pour mariages',
                'category_name' => 'Décoration',
                'is_active' => true,
            ],
            [
                'name' => 'Décoration d\'Événements',
                'description' => 'Services de décoration pour événements',
                'category_name' => 'Décoration',
                'is_active' => true,
            ],
            [
                'name' => 'Arrangements Floraux',
                'description' => 'Services de décoration florale',
                'category_name' => 'Décoration',
                'is_active' => true,
            ],
        ];

        foreach ($services as $serviceData) {
            // Find the category
            $category = $categories->firstWhere('name', $serviceData['category_name']);

            if ($category) {
                Service::create([
                    'name' => $serviceData['name'],
                    'slug' => Str::slug($serviceData['name']),
                    'description' => $serviceData['description'],
                    'category_id' => $category->id,
                    'is_active' => $serviceData['is_active'],
                ]);
            }
        }
    }
}
