<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\ClientsModel\EventType;
use App\Models\EventTypeServiceCategory;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class EventTypeServiceCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing data
        DB::table('event_type_service_categories')->truncate();

        // Get all event types and categories
        $eventTypes = EventType::all();
        $categories = Category::all();

        if ($eventTypes->isEmpty()) {
            $this->command->info('No event types found. Please run the EventTypeSeeder first.');
            return;
        }

        if ($categories->isEmpty()) {
            $this->command->info('No categories found. Please run the CategorySeeder first.');
            return;
        }

        // Define mappings between event types and service categories
        $mappings = [
            'Wedding' => [
                'Traiteur' => ['priority' => 10, 'is_required' => true, 'description' => 'Service de restauration pour votre mariage'],
                'Photographie' => ['priority' => 9, 'is_required' => true, 'description' => 'Photographe professionnel pour capturer vos moments spéciaux'],
                'Vidéographie' => ['priority' => 8, 'is_required' => false, 'description' => 'Service vidéo pour immortaliser votre journée'],
                'Musique & Animation' => ['priority' => 7, 'is_required' => true, 'description' => 'DJ ou groupe pour animer votre soirée'],
                'Espaces de Réception' => ['priority' => 10, 'is_required' => true, 'description' => 'Lieu pour accueillir votre cérémonie et réception'],
                'Décoration' => ['priority' => 8, 'is_required' => true, 'description' => 'Décoration florale et thématique pour votre mariage'],
                'Transport' => ['priority' => 6, 'is_required' => false, 'description' => 'Service de transport pour les mariés et invités'],
            ],
            'Birthday Party' => [
                'Traiteur' => ['priority' => 9, 'is_required' => true, 'description' => 'Service de restauration pour votre anniversaire'],
                'Photographie' => ['priority' => 7, 'is_required' => false, 'description' => 'Photographe pour capturer les moments de la fête'],
                'Musique & Animation' => ['priority' => 8, 'is_required' => true, 'description' => 'DJ ou animateur pour votre fête d\'anniversaire'],
                'Décoration' => ['priority' => 7, 'is_required' => false, 'description' => 'Décoration thématique pour votre anniversaire'],
            ],
            'Corporate Event' => [
                'Traiteur' => ['priority' => 9, 'is_required' => true, 'description' => 'Service de restauration pour votre événement d\'entreprise'],
                'Photographie' => ['priority' => 8, 'is_required' => true, 'description' => 'Photographe professionnel pour votre événement corporate'],
                'Vidéographie' => ['priority' => 7, 'is_required' => false, 'description' => 'Service vidéo pour documenter votre événement'],
                'Espaces de Réception' => ['priority' => 10, 'is_required' => true, 'description' => 'Lieu pour accueillir votre événement d\'entreprise'],
            ],
            'Graduation' => [
                'Traiteur' => ['priority' => 8, 'is_required' => true, 'description' => 'Service de restauration pour votre cérémonie de remise de diplôme'],
                'Photographie' => ['priority' => 9, 'is_required' => true, 'description' => 'Photographe pour immortaliser ce moment important'],
                'Vidéographie' => ['priority' => 7, 'is_required' => false, 'description' => 'Service vidéo pour votre cérémonie de remise de diplôme'],
            ],
            'Anniversary' => [
                'Traiteur' => ['priority' => 9, 'is_required' => true, 'description' => 'Service de restauration pour votre anniversaire de mariage'],
                'Photographie' => ['priority' => 8, 'is_required' => false, 'description' => 'Photographe pour capturer vos moments spéciaux'],
                'Musique & Animation' => ['priority' => 7, 'is_required' => false, 'description' => 'Ambiance musicale pour votre célébration'],
                'Décoration' => ['priority' => 8, 'is_required' => false, 'description' => 'Décoration romantique pour votre anniversaire de mariage'],
            ],
            'Baby Shower' => [
                'Traiteur' => ['priority' => 8, 'is_required' => true, 'description' => 'Service de restauration pour votre baby shower'],
                'Photographie' => ['priority' => 7, 'is_required' => false, 'description' => 'Photographe pour capturer les moments de joie'],
                'Décoration' => ['priority' => 9, 'is_required' => true, 'description' => 'Décoration thématique pour votre baby shower'],
            ],
            'Engagement Party' => [
                'Traiteur' => ['priority' => 9, 'is_required' => true, 'description' => 'Service de restauration pour votre fête de fiançailles'],
                'Photographie' => ['priority' => 8, 'is_required' => true, 'description' => 'Photographe pour immortaliser votre engagement'],
                'Musique & Animation' => ['priority' => 7, 'is_required' => false, 'description' => 'Ambiance musicale pour votre célébration'],
                'Décoration' => ['priority' => 8, 'is_required' => false, 'description' => 'Décoration élégante pour votre fête de fiançailles'],
            ],
            'Religious Ceremony' => [
                'Traiteur' => ['priority' => 8, 'is_required' => false, 'description' => 'Service de restauration pour votre cérémonie religieuse'],
                'Photographie' => ['priority' => 9, 'is_required' => true, 'description' => 'Photographe pour capturer les moments sacrés'],
                'Vidéographie' => ['priority' => 8, 'is_required' => false, 'description' => 'Service vidéo pour votre cérémonie religieuse'],
                'Musique & Animation' => ['priority' => 7, 'is_required' => false, 'description' => 'Musique appropriée pour votre cérémonie'],
                'Décoration' => ['priority' => 8, 'is_required' => true, 'description' => 'Décoration respectueuse pour votre cérémonie religieuse'],
            ],
            'Funeral' => [
                'Traiteur' => ['priority' => 8, 'is_required' => false, 'description' => 'Service de restauration pour la réception après les obsèques'],
                'Photographie' => ['priority' => 6, 'is_required' => false, 'description' => 'Photographe discret pour la cérémonie'],
                'Transport' => ['priority' => 9, 'is_required' => true, 'description' => 'Service de transport funéraire'],
            ],
        ];

        // Create the mappings
        foreach ($mappings as $eventTypeName => $categoryMappings) {
            $eventType = $eventTypes->where('name', $eventTypeName)->first();

            if (!$eventType) {
                $this->command->info("Event type '{$eventTypeName}' not found. Skipping.");
                continue;
            }

            foreach ($categoryMappings as $categoryName => $attributes) {
                $category = $categories->where('name', $categoryName)->first();

                if (!$category) {
                    $this->command->info("Category '{$categoryName}' not found. Skipping.");
                    continue;
                }

                EventTypeServiceCategory::create([
                    'event_type_id' => $eventType->id,
                    'category_id' => $category->id,
                    'is_required' => $attributes['is_required'],
                    'priority' => $attributes['priority'],
                    'description' => $attributes['description'],
                ]);
            }
        }

        $this->command->info('Event type service categories seeded successfully.');
    }
}
