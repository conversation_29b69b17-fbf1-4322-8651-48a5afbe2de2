<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\ClientsModel\Client;
use App\Models\ClientsModel\EventType;
use App\Models\ProvidersModel\Provider;
use App\Models\ProvidersModel\ProviderService;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Faker\Factory as Faker;

class BasicTestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = Faker::create();

        // Créer un utilisateur administrateur
        $admin = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Admin User',
                'password' => Hash::make('password'),
                'role' => 'admin',
            ]
        );

        // Créer un utilisateur prestataire
        $provider1 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Provider User',
                'password' => Hash::make('password'),
                'role' => 'provider',
            ]
        );

        // Créer un utilisateur client
        $client1 = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Client User',
                'password' => Hash::make('password'),
                'role' => 'client',
            ]
        );

        // Créer les catégories de base
        $categories = [
            ['name' => 'Traiteur', 'slug' => 'traiteur', 'description' => 'Services de traiteur pour vos événements'],
            ['name' => 'Photographie', 'slug' => 'photographie', 'description' => 'Services de photographie professionnelle'],
            ['name' => 'Vidéographie', 'slug' => 'videographie', 'description' => 'Services de vidéographie professionnelle'],
            ['name' => 'Lieu de Réception', 'slug' => 'lieu-de-reception', 'description' => 'Lieux pour vos événements'],
            ['name' => 'Musique et Animation', 'slug' => 'musique-et-animation', 'description' => 'Services de musique et animation'],
            ['name' => 'Transport', 'slug' => 'transport', 'description' => 'Services de transport pour vos événements'],
        ];

        foreach ($categories as $categoryData) {
            Category::firstOrCreate(
                ['slug' => $categoryData['slug']],
                $categoryData
            );
        }

        // Créer les types d'événements
        $eventTypes = [
            ['name' => 'Mariage', 'slug' => 'mariage', 'description' => 'Célébration de mariage'],
            ['name' => 'Anniversaire', 'slug' => 'anniversaire', 'description' => 'Fête d\'anniversaire'],
            ['name' => 'Conférence', 'slug' => 'conference', 'description' => 'Événement professionnel'],
            ['name' => 'Séminaire', 'slug' => 'seminaire', 'description' => 'Formation ou séminaire'],
            ['name' => 'Gala', 'slug' => 'gala', 'description' => 'Soirée de gala'],
        ];

        foreach ($eventTypes as $eventTypeData) {
            EventType::firstOrCreate(
                ['slug' => $eventTypeData['slug']],
                $eventTypeData
            );
        }

        // Créer un profil de prestataire
        $providerProfile = Provider::firstOrCreate(
            ['user_id' => $provider1->id],
            [
                'business_name' => 'Service Pro',
                'business_email' => '<EMAIL>',
                'business_phone' => '+261 34 12 345 67',
                'description' => 'Nous offrons des services professionnels pour tous vos événements',
                'service_areas' => json_encode(['Antananarivo', 'Antsirabe', 'Toamasina']),
                'is_verified' => true,
                'is_active' => true,
                'subscription_type' => 'premium',
                'subscription_expires_at' => now()->addYear(),
            ]
        );

        // Associer le prestataire à toutes les catégories
        $categories = Category::all();
        foreach ($categories as $category) {
            $providerProfile->categories()->syncWithoutDetaching([
                $category->id => ['is_primary' => $category->name === 'Traiteur']
            ]);
        }

        // Créer un profil client
        Client::firstOrCreate(
            ['user_id' => $client1->id],
            [
                'client_type' => 'individual',
                'company_name' => null,
                'company_position' => null,
            ]
        );

        // Créer des services pour le prestataire
        $this->createServices($providerProfile, $categories);
    }

    /**
     * Créer des services pour un prestataire
     */
    private function createServices(Provider $provider, $categories)
    {
        $faker = Faker::create();

        // Service de traiteur
        $cateringCategory = $categories->where('slug', 'traiteur')->first();
        if ($cateringCategory) {
            $cateringService = ProviderService::create([
                'provider_id' => $provider->id,
                'category_id' => $cateringCategory->id,
                'title' => 'Service de traiteur premium',
                'description' => 'Notre service de traiteur offre une cuisine raffinée pour tous vos événements',
                'price' => 50000,
                'is_negotiable' => true,
                'details' => json_encode([
                    'min_guests' => 10,
                    'max_guests' => 500,
                    'menu_items' => [
                        [
                            'name' => 'Salade César',
                            'type' => 'entree',
                            'price' => 15000,
                            'description' => 'Salade romaine, croûtons, parmesan, sauce César'
                        ],
                        [
                            'name' => 'Poulet rôti aux herbes',
                            'type' => 'plat_principal',
                            'price' => 25000,
                            'description' => 'Poulet rôti avec des herbes fraîches et légumes de saison'
                        ],
                        [
                            'name' => 'Tiramisu',
                            'type' => 'dessert',
                            'price' => 10000,
                            'description' => 'Dessert italien à base de café et mascarpone'
                        ]
                    ],
                    'dietary_options' => ['vegetarian', 'gluten_free']
                ]),
                'is_featured' => true,
                'is_active' => true,
            ]);
        }

        // Service de photographie
        $photoCategory = $categories->where('slug', 'photographie')->first();
        if ($photoCategory) {
            $photoService = ProviderService::create([
                'provider_id' => $provider->id,
                'category_id' => $photoCategory->id,
                'title' => 'Service de photographie professionnelle',
                'description' => 'Capturez vos moments précieux avec notre service de photographie',
                'price' => 350000,
                'is_negotiable' => false,
                'details' => json_encode([
                    'duration' => 4,
                    'num_photos' => 200,
                    'deliverables' => ['digital_files', 'photo_album'],
                    'equipment' => 'Canon EOS R5, objectifs professionnels',
                    'photo_styles' => ['traditional', 'photojournalistic']
                ]),
                'is_featured' => true,
                'is_active' => true,
            ]);
        }

        // Service de transport
        $transportCategory = $categories->where('slug', 'transport')->first();
        if ($transportCategory) {
            $transportService = ProviderService::create([
                'provider_id' => $provider->id,
                'category_id' => $transportCategory->id,
                'title' => 'Service de transport VIP',
                'description' => 'Transport de luxe pour vos événements spéciaux',
                'price' => 200000,
                'is_negotiable' => true,
                'details' => json_encode([
                    'vehicle_type' => 'limousine',
                    'seats' => 8,
                    'transport_options' => ['cortege_lead', 'decoration'],
                    'transport_pricing_model' => 'hourly',
                    'vehicle_details' => 'Limousine Mercedes-Benz Classe S avec intérieur cuir'
                ]),
                'is_featured' => false,
                'is_active' => true,
            ]);
        }
    }
}
