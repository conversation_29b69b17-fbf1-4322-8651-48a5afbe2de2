<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;
use Illuminate\Support\Str;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Traiteur',
                'description' => 'Services de restauration pour événements',
                'icon' => 'fa-utensils',
                'is_active' => true,
                'order' => 1,
            ],
            [
                'name' => 'Photographie',
                'description' => 'Services de photographie professionnelle pour événements',
                'icon' => 'fa-camera',
                'is_active' => true,
                'order' => 2,
            ],
            [
                'name' => 'Vidéographie',
                'description' => 'Services vidéo professionnels pour événements',
                'icon' => 'fa-video',
                'is_active' => true,
                'order' => 3,
            ],
            [
                'name' => 'Musique & Animation',
                'description' => 'DJs, groupes et services d\'animation',
                'icon' => 'fa-music',
                'is_active' => true,
                'order' => 4,
            ],
            [
                'name' => 'Espaces de Réception',
                'description' => 'Lieux et espaces pour événements',
                'icon' => 'fa-building',
                'is_active' => true,
                'order' => 5,
            ],
            [
                'name' => 'Décoration',
                'description' => 'Services de décoration et de stylisme pour événements',
                'icon' => 'fa-paint-brush',
                'is_active' => true,
                'order' => 6,
            ],
            [
                'name' => 'Transport',
                'description' => 'Services de transport pour événements',
                'icon' => 'fa-car',
                'is_active' => true,
                'order' => 7,
            ],
        ];

        foreach ($categories as $category) {
            // Check if category already exists
            $slug = Str::slug($category['name']);
            $existingCategory = Category::where('slug', $slug)->first();

            if (!$existingCategory) {
                Category::create([
                    'name' => $category['name'],
                    'slug' => $slug,
                    'description' => $category['description'],
                    'icon' => $category['icon'],
                    'is_active' => $category['is_active'],
                    'order' => $category['order'],
                ]);
            }
        }
    }
}
