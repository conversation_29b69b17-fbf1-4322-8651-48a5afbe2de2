<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\ProvidersModel\Provider;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Faker\Factory as Faker;

class ProvidersPerCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = Faker::create();

        // Get all categories
        $categories = Category::all();

        if ($categories->isEmpty()) {
            $this->command->info('No categories found. Please run the CategorySeeder first.');
            return;
        }

        // Business types for more realistic business names
        $businessTypes = [
            'Traiteur' => ['Catering', 'Food', 'Cuisine', 'Delights', 'Gastronomy', 'Kitchen', 'Buffet', 'Meals'],
            'Photographie' => ['Photography', 'Photos', 'Captures', 'Moments', 'Memories', 'Lens', 'Shots', 'Images'],
            'Vidéographie' => ['Films', 'Video', 'Productions', 'Cinema', 'Motion', 'Frames', 'Reels', 'Studios'],
            'Musique & Animation' => ['Entertainment', 'Music', 'DJ', 'Band', 'Sound', 'Performers', 'Artists', 'Shows'],
            'Espaces de Réception' => ['Venues', 'Halls', 'Gardens', 'Spaces', 'Locations', 'Places', 'Mansions', 'Palaces'],
            'Décoration' => ['Decor', 'Design', 'Styling', 'Decorations', 'Ambiance', 'Arrangements', 'Aesthetics', 'Ornaments'],
            'Transport' => ['Transport', 'Cars', 'Limo', 'Rides', 'Transfers', 'Fleet', 'Vehicles', 'Shuttles'],
        ];

        // Business name prefixes
        $prefixes = [
            'Royal', 'Elite', 'Premium', 'Luxury', 'Elegant', 'Exclusive', 'Perfect', 'Dream',
            'Golden', 'Silver', 'Diamond', 'Platinum', 'Crystal', 'Malagasy', 'Tana', 'Exotic',
            'Unique', 'Creative', 'Innovative', 'Professional', 'Expert', 'Master', 'Artisan', 'Signature'
        ];

        // Business name suffixes
        $suffixes = [
            'Services', 'Solutions', 'Group', 'Company', 'Experts', 'Professionals', 'Team', 'Crew',
            'Madagascar', 'Antananarivo', 'Enterprise', 'Agency', 'Boutique', 'Specialists', 'Masters', 'Artists'
        ];

        // Service areas in Madagascar
        $serviceAreas = [
            'Antananarivo', 'Toamasina', 'Antsirabe', 'Fianarantsoa', 'Mahajanga',
            'Toliara', 'Antsiranana', 'Ambovombe', 'Morondava', 'Ambatondrazaka'
        ];

        // Subscription types with probabilities
        $subscriptionTypes = [
            'free' => 50,
            'basic' => 30,
            'premium' => 20,
        ];

        // Create 10 providers for each category
        foreach ($categories as $category) {
            $this->command->info("Creating providers for category: {$category->name}");

            // Get business types for this category
            $categoryBusinessTypes = $businessTypes[$category->name] ?? ['Services', 'Solutions', 'Experts'];

            for ($i = 1; $i <= 10; $i++) {
                // Create a user for the provider
                $user = User::create([
                    'name' => $faker->name,
                    'email' => $faker->unique()->safeEmail,
                    'password' => Hash::make('password'),
                    'role' => 'provider',
                    'phone' => '+261 ' . rand(30, 39) . ' ' . rand(10, 99) . ' ' . rand(100, 999) . ' ' . rand(10, 99),
                    'is_active' => true,
                    'email_verified_at' => now(),
                ]);

                // Generate a business name based on category
                $prefix = $faker->randomElement($prefixes);
                $businessType = $faker->randomElement($categoryBusinessTypes);
                $suffix = $faker->optional(0.5)->randomElement($suffixes);

                $businessName = $prefix . ' ' . $businessType;
                if ($suffix) {
                    $businessName .= ' ' . $suffix;
                }

                // Random service areas (2-4)
                $randomServiceAreas = $faker->randomElements($serviceAreas, rand(2, 4));

                // Random subscription type based on probabilities
                $subscriptionType = $this->getRandomWeighted($subscriptionTypes);

                // Subscription expiry date based on type
                $subscriptionExpiresAt = null;
                if ($subscriptionType !== 'free') {
                    $subscriptionExpiresAt = $subscriptionType === 'premium'
                        ? now()->addMonths(rand(6, 12))
                        : now()->addMonths(rand(1, 6));
                }

                // Create the provider
                $provider = Provider::create([
                    'user_id' => $user->id,
                    'business_name' => $businessName,
                    'business_email' => $faker->companyEmail,
                    'business_phone' => '+261 ' . rand(20, 29) . ' ' . rand(10, 99) . ' ' . rand(100, 999) . ' ' . rand(10, 99),
                    'business_address' => $faker->address,
                    'business_registration_number' => 'REG' . rand(1000, 9999) . '/' . date('Y'),
                    'description' => $faker->paragraph(rand(3, 5)),
                    'service_areas' => json_encode($randomServiceAreas),
                    'is_verified' => $faker->boolean(80), // 80% chance of being verified
                    'is_featured' => $faker->boolean(20), // 20% chance of being featured
                    'subscription_type' => $subscriptionType,
                    'subscription_expires_at' => $subscriptionExpiresAt,
                    'is_active' => true,
                ]);

                // Assign the category to the provider
                DB::table('provider_categories')->insert([
                    'provider_id' => $provider->id,
                    'category_id' => $category->id,
                    'is_primary' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                // Randomly assign 0-2 additional categories
                $otherCategories = $categories->where('id', '!=', $category->id)->random(rand(0, 2));
                foreach ($otherCategories as $otherCategory) {
                    DB::table('provider_categories')->insert([
                        'provider_id' => $provider->id,
                        'category_id' => $otherCategory->id,
                        'is_primary' => false,
                        'created_at' => now(),
                        'updated_at' => now(),
                    ]);
                }
            }
        }
    }

    /**
     * Get a random value based on weighted probabilities
     *
     * @param array $weighted Array of values with their weights
     * @return mixed A randomly selected value
     */
    private function getRandomWeighted(array $weighted)
    {
        $rand = mt_rand(1, array_sum($weighted));

        foreach ($weighted as $key => $value) {
            $rand -= $value;
            if ($rand <= 0) {
                return $key;
            }
        }
    }
}
