<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\ProvidersModel\Provider;
use App\Models\ProvidersModel\ProviderService;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Faker\Factory as Faker;

class DetailedProviderServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = Faker::create();

        // Get all categories
        $categories = Category::all();

        if ($categories->isEmpty()) {
            $this->command->info('No categories found. Please run the CategorySeeder first.');
            return;
        }

        // Get all providers
        $providers = Provider::all();

        if ($providers->isEmpty()) {
            $this->command->info('No providers found. Please run the ProviderSeeder first.');
            return;
        }

        // Service titles by category
        $serviceTitles = [
            'Traiteur' => [
                'Buffet Complet',
                'Menu Gastronomique',
                'Cocktail Dînatoire',
                'Cuisine Traditionnelle',
                'Menu International'
            ],
            'Photographie' => [
                'Reportage Photo Complet',
                'Séance Portrait',
                'Photographie Artistique',
                'Album Souvenir Premium',
                'Photographie Événementielle'
            ],
            'Vidéographie' => [
                'Reportage Vidéo HD',
                'Film Événementiel',
                'Montage Professionnel',
                'Drone Aérien 4K',
                'Clip Souvenir'
            ],
            'Musique & Animation' => [
                'DJ & Sonorisation Complète',
                'Groupe Live',
                'Animation & Maître de Cérémonie',
                'Spectacle Musical',
                'Performance Artistique'
            ],
            'Espaces de Réception' => [
                'Salle de Réception Élégante',
                'Jardin Événementiel',
                'Espace VIP',
                'Terrasse Panoramique',
                'Domaine Privé'
            ],
            'Décoration' => [
                'Décoration Complète',
                'Arrangements Floraux',
                'Mise en Scène Thématique',
                'Décoration Lumineuse',
                'Design d\'Ambiance'
            ],
            'Transport' => [
                'Voiture de Luxe avec Chauffeur',
                'Service Limousine',
                'Transport des Invités',
                'Navette VIP',
                'Flotte Événementielle'
            ],
        ];

        // Supprimer d'abord les entrées de la table provider_galleries qui référencent provider_services
        DB::table('provider_galleries')->delete();

        // Supprimer ensuite les entrées de la table provider_services
        DB::table('provider_services')->delete();

        // Pour chaque prestataire, créer des services détaillés
        foreach ($providers as $provider) {
            // Récupérer les catégories du prestataire
            $providerCategories = $provider->categories;

            if ($providerCategories->isEmpty()) {
                continue;
            }

            // Pour chaque catégorie du prestataire, créer 1-3 services
            foreach ($providerCategories as $category) {
                $serviceCount = rand(1, 3);

                for ($i = 0; $i < $serviceCount; $i++) {
                    // Sélectionner un titre de service aléatoire pour cette catégorie
                    $categoryServiceTitles = $serviceTitles[$category->name] ?? ['Service Standard', 'Service Premium', 'Service Deluxe'];
                    $serviceTitle = $categoryServiceTitles[array_rand($categoryServiceTitles)];

                    // Ajouter un identifiant unique pour éviter les doublons
                    $serviceTitle .= ' ' . uniqid();

                    // Créer les options spécifiques à la catégorie
                    $options = $this->generateOptionsForCategory($category->name, $faker);

                    // Créer le service
                    ProviderService::create([
                        'provider_id' => $provider->id,
                        'category_id' => $category->id,
                        'title' => $serviceTitle,
                        'description' => $faker->paragraph(rand(3, 5)),
                        'options' => $options,
                        'is_featured' => $faker->boolean(30), // 30% chance of being featured
                        'is_active' => true,
                    ]);
                }
            }
        }
    }

    /**
     * Génère des options spécifiques pour chaque catégorie
     */
    private function generateOptionsForCategory($categoryName, $faker)
    {
        $options = [];

        // Prix de base (70% des services auront un prix fixe, 30% seront sur devis)
        $options['base_price'] = $faker->boolean(70) ? rand(50000, 2000000) : 0;

        switch ($categoryName) {
            case 'Traiteur':
                return $this->generateCateringOptions($options, $faker);

            case 'Photographie':
                return $this->generatePhotographyOptions($options, $faker);

            case 'Vidéographie':
                return $this->generateVideographyOptions($options, $faker);

            case 'Musique & Animation':
                return $this->generateMusicOptions($options, $faker);

            case 'Espaces de Réception':
                return $this->generateVenueOptions($options, $faker);

            case 'Décoration':
                return $this->generateDecorationOptions($options, $faker);

            case 'Transport':
                return $this->generateTransportOptions($options, $faker);

            default:
                return $options;
        }
    }

    /**
     * Génère des options pour les services de traiteur
     */
    private function generateCateringOptions($options, $faker)
    {
        // Types de plats
        $dishTypes = ['entree', 'plat_principal', 'dessert', 'boisson', 'accompagnement'];

        // Noms de plats par type
        $dishNames = [
            'entree' => [
                'Salade César', 'Carpaccio de Bœuf', 'Foie Gras Maison', 'Crevettes Flambées',
                'Velouté de Potiron', 'Tartare de Saumon', 'Assiette de Charcuterie'
            ],
            'plat_principal' => [
                'Filet Mignon Sauce Poivre', 'Poulet Rôti aux Herbes', 'Saumon Grillé',
                'Risotto aux Champignons', 'Magret de Canard', 'Bœuf Bourguignon', 'Paella Royale'
            ],
            'dessert' => [
                'Crème Brûlée', 'Mousse au Chocolat', 'Tarte Tatin', 'Tiramisu',
                'Salade de Fruits Frais', 'Profiteroles', 'Fondant au Chocolat'
            ],
            'boisson' => [
                'Vin Rouge Premium', 'Champagne', 'Cocktail Signature', 'Jus de Fruits Frais',
                'Eau Minérale', 'Café/Thé', 'Punch Tropical'
            ],
            'accompagnement' => [
                'Gratin Dauphinois', 'Légumes de Saison', 'Riz Basmati', 'Purée Maison',
                'Pommes de Terre Rôties', 'Ratatouille', 'Salade Verte'
            ]
        ];

        // Générer des plats pour le menu
        $options['menu_items'] = [];
        $menuItemCount = rand(5, 15);

        for ($i = 0; $i < $menuItemCount; $i++) {
            $type = $dishTypes[array_rand($dishTypes)];
            $name = $dishNames[$type][array_rand($dishNames[$type])];

            $options['menu_items'][] = [
                'name' => $name,
                'type' => $type,
                'description' => $faker->sentence(),
                'price' => rand(5000, 50000)
            ];
        }

        // Options supplémentaires
        $options['includes_service'] = $faker->boolean(70);
        $options['includes_equipment'] = $faker->boolean(60);
        $options['min_guests'] = rand(10, 50);
        $options['max_guests'] = rand(100, 500);
        $options['price_per_person'] = rand(15000, 100000);

        return $options;
    }

    /**
     * Génère des options pour les services de photographie
     */
    private function generatePhotographyOptions($options, $faker)
    {
        $options['duration_hours'] = rand(2, 12);
        $options['includes_editing'] = $faker->boolean(80);
        $options['includes_prints'] = $faker->boolean(60);
        $options['includes_digital_files'] = $faker->boolean(90);
        $options['includes_album'] = $faker->boolean(70);
        $options['number_of_photographers'] = rand(1, 3);
        $options['delivery_time_days'] = rand(7, 30);
        $options['equipment_description'] = $faker->sentence();

        return $options;
    }

    /**
     * Génère des options pour les services de vidéographie
     */
    private function generateVideographyOptions($options, $faker)
    {
        $options['duration_hours'] = rand(2, 12);
        $options['includes_editing'] = $faker->boolean(90);
        $options['includes_drone'] = $faker->boolean(60);
        $options['includes_highlight_reel'] = $faker->boolean(80);
        $options['resolution'] = $faker->randomElement(['HD', '4K', '8K']);
        $options['number_of_videographers'] = rand(1, 3);
        $options['delivery_time_days'] = rand(14, 60);
        $options['equipment_description'] = $faker->sentence();

        return $options;
    }

    /**
     * Génère des options pour les services de musique et animation
     */
    private function generateMusicOptions($options, $faker)
    {
        $options['artist_type'] = $faker->randomElement(['DJ', 'Groupe Live', 'Orchestre', 'Soliste', 'Animateur']);
        $options['duration_hours'] = rand(2, 8);
        $options['includes_equipment'] = $faker->boolean(80);
        $options['includes_lights'] = $faker->boolean(70);
        $options['includes_special_effects'] = $faker->boolean(40);
        $options['music_genres'] = $faker->randomElements(['Pop', 'Rock', 'Jazz', 'Classique', 'R&B', 'Électronique', 'Traditionnelle'], rand(2, 5));
        $options['number_of_performers'] = rand(1, 10);

        return $options;
    }

    /**
     * Génère des options pour les espaces de réception
     */
    private function generateVenueOptions($options, $faker)
    {
        $options['capacity'] = rand(50, 500);
        $options['indoor'] = $faker->boolean(80);
        $options['outdoor'] = $faker->boolean(60);
        $options['includes_tables_chairs'] = $faker->boolean(70);
        $options['includes_parking'] = $faker->boolean(80);
        $options['includes_security'] = $faker->boolean(60);
        $options['includes_cleaning'] = $faker->boolean(70);
        $options['includes_sound_system'] = $faker->boolean(50);
        $options['includes_lighting'] = $faker->boolean(50);
        $options['area_sqm'] = rand(100, 1000);
        $options['location_description'] = $faker->sentence();

        return $options;
    }

    /**
     * Génère des options pour les services de décoration
     */
    private function generateDecorationOptions($options, $faker)
    {
        $options['includes_flowers'] = $faker->boolean(80);
        $options['includes_furniture'] = $faker->boolean(60);
        $options['includes_lighting'] = $faker->boolean(70);
        $options['includes_table_settings'] = $faker->boolean(80);
        $options['includes_installation'] = $faker->boolean(90);
        $options['includes_removal'] = $faker->boolean(70);
        $options['theme_options'] = $faker->randomElements(['Classique', 'Moderne', 'Rustique', 'Bohème', 'Élégant', 'Tropical', 'Vintage'], rand(2, 5));
        $options['color_schemes'] = $faker->randomElements(['Blanc & Or', 'Bleu & Argent', 'Rouge & Noir', 'Pastel', 'Multicolore', 'Monochrome'], rand(1, 3));

        return $options;
    }

    /**
     * Génère des options pour les services de transport
     */
    private function generateTransportOptions($options, $faker)
    {
        $options['vehicle_type'] = $faker->randomElement(['Berline de Luxe', 'SUV', 'Limousine', 'Van', 'Bus', 'Voiture Classique']);
        $options['includes_driver'] = $faker->boolean(90);
        $options['includes_decoration'] = $faker->boolean(60);
        $options['includes_refreshments'] = $faker->boolean(50);
        $options['seats'] = rand(2, 50);
        $options['pricing_model'] = $faker->randomElement(['hourly', 'per_trip', 'per_day']);
        $options['includes_fuel'] = $faker->boolean(70);
        $options['includes_waiting_time'] = $faker->boolean(60);
        $options['vehicle_description'] = $faker->sentence();

        return $options;
    }
}
