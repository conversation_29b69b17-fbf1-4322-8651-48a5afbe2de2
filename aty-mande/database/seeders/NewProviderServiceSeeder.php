<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\ProvidersModel\Provider;
use App\Models\ProvidersModel\ProviderService;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Faker\Factory as Faker;

class NewProviderServiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $faker = Faker::create();

        // Get all categories
        $categories = Category::all();

        if ($categories->isEmpty()) {
            $this->command->info('No categories found. Please run the CategorySeeder first.');
            return;
        }

        // Business types for more realistic business names
        $businessTypes = [
            'Traiteur' => ['Catering', 'Food', 'Cuisine', 'Delights', 'Gastronomy', 'Kitchen', 'Buffet', 'Meals'],
            'Photographie' => ['Photography', 'Photos', 'Captures', 'Moments', 'Memories', 'Lens', 'Shots', 'Images'],
            'Vidéographie' => ['Films', 'Video', 'Productions', 'Cinema', 'Motion', 'Frames', 'Reels', 'Studios'],
            'Musique & Animation' => ['Entertainment', 'Music', 'DJ', 'Band', 'Sound', 'Performers', 'Artists', 'Shows'],
            'Espaces de Réception' => ['Venues', 'Halls', 'Gardens', 'Spaces', 'Locations', 'Places', 'Mansions', 'Palaces'],
            'Décoration' => ['Decor', 'Design', 'Styling', 'Decorations', 'Ambiance', 'Arrangements', 'Aesthetics', 'Ornaments'],
            'Transport' => ['Transport', 'Cars', 'Limo', 'Rides', 'Transfers', 'Fleet', 'Vehicles', 'Shuttles'],
        ];

        // Business name prefixes
        $prefixes = [
            'Royal', 'Elite', 'Premium', 'Luxury', 'Elegant', 'Exclusive', 'Perfect', 'Dream',
            'Golden', 'Silver', 'Diamond', 'Platinum', 'Crystal', 'Malagasy', 'Tana', 'Exotic',
            'Unique', 'Creative', 'Innovative', 'Professional', 'Expert', 'Master', 'Artisan', 'Signature'
        ];

        // Business name suffixes
        $suffixes = [
            'Services', 'Solutions', 'Group', 'Company', 'Experts', 'Professionals', 'Team', 'Crew',
            'Madagascar', 'Antananarivo', 'Enterprise', 'Agency', 'Boutique', 'Specialists', 'Masters', 'Artists'
        ];

        // Service areas in Madagascar
        $serviceAreas = [
            'Antananarivo', 'Toamasina', 'Antsirabe', 'Fianarantsoa', 'Mahajanga',
            'Toliara', 'Antsiranana', 'Ambovombe', 'Morondava', 'Ambatondrazaka'
        ];

        // Service titles by category
        $serviceTitles = [
            'Traiteur' => ['Buffet Complet', 'Menu Gastronomique', 'Cocktail Dînatoire', 'Cuisine Traditionnelle', 'Menu International'],
            'Photographie' => ['Reportage Photo', 'Séance Portrait', 'Photographie Artistique', 'Album Souvenir', 'Photographie Événementielle'],
            'Vidéographie' => ['Reportage Vidéo', 'Film Événementiel', 'Montage Professionnel', 'Drone Aérien', 'Clip Souvenir'],
            'Musique & Animation' => ['DJ & Sonorisation', 'Groupe Live', 'Animation Complète', 'Spectacle Musical', 'Performance Artistique'],
            'Espaces de Réception' => ['Salle de Réception', 'Jardin Événementiel', 'Espace VIP', 'Terrasse Panoramique', 'Domaine Privé'],
            'Décoration' => ['Décoration Complète', 'Arrangements Floraux', 'Mise en Scène', 'Décoration Thématique', 'Design d\'Ambiance'],
            'Transport' => ['Voiture de Luxe', 'Service Limousine', 'Transport des Invités', 'Navette VIP', 'Flotte Événementielle'],
        ];

        // Create 5 providers for each category
        foreach ($categories as $category) {
            $this->command->info("Creating providers for category: {$category->name}");

            // Get business types for this category
            $categoryBusinessTypes = $businessTypes[$category->name] ?? ['Services', 'Solutions', 'Experts'];
            $categoryServiceTitles = $serviceTitles[$category->name] ?? ['Service Standard', 'Service Premium', 'Service Deluxe', 'Service VIP', 'Service Exclusif'];

            for ($i = 0; $i < 5; $i++) {
                // Create a user for the provider
                $user = User::create([
                    'name' => $faker->name,
                    'email' => $faker->unique()->safeEmail,
                    'password' => Hash::make('password'),
                    'role' => 'provider',
                    'phone' => '+261 ' . rand(30, 39) . ' ' . rand(10, 99) . ' ' . rand(100, 999) . ' ' . rand(10, 99),
                    'is_active' => true,
                    'email_verified_at' => now(),
                ]);

                // Generate a business name based on category
                $prefix = $faker->randomElement($prefixes);
                $businessType = $faker->randomElement($categoryBusinessTypes);
                $suffix = $faker->optional(0.5)->randomElement($suffixes);

                $businessName = $prefix . ' ' . $businessType;
                if ($suffix) {
                    $businessName .= ' ' . $suffix;
                }

                // Random service areas (2-4)
                $randomServiceAreas = $faker->randomElements($serviceAreas, rand(2, 4));

                // Create the provider
                $provider = Provider::create([
                    'user_id' => $user->id,
                    'business_name' => $businessName,
                    'business_email' => $faker->companyEmail,
                    'business_phone' => '+261 ' . rand(20, 29) . ' ' . rand(10, 99) . ' ' . rand(100, 999) . ' ' . rand(10, 99),
                    'business_address' => $faker->address,
                    'business_registration_number' => 'REG' . rand(1000, 9999) . '/' . date('Y'),
                    'description' => $faker->paragraph(rand(3, 5)),
                    'service_areas' => json_encode($randomServiceAreas),
                    'is_verified' => $faker->boolean(80), // 80% chance of being verified
                    'is_featured' => $faker->boolean(20), // 20% chance of being featured
                    'subscription_type' => 'basic',
                    'subscription_expires_at' => now()->addMonths(6),
                    'is_active' => true,
                ]);

                // Assign the category to the provider
                DB::table('provider_categories')->insert([
                    'provider_id' => $provider->id,
                    'category_id' => $category->id,
                    'is_primary' => true,
                    'created_at' => now(),
                    'updated_at' => now(),
                ]);

                // Create a service for this provider with the new price structure
                $serviceTitle = $categoryServiceTitles[$i];

                // 30% des services seront "Sur devis" (prix = 0)
                $basePrice = $faker->boolean(30) ? 0 : rand(10000, 100000);
                $options = ['base_price' => $basePrice];

                // For catering services, add menu items
                if ($category->name === 'Traiteur') {
                    $menuItems = [];
                    $menuTypes = ['entree', 'plat_principal', 'dessert', 'boisson', 'accompagnement'];
                    $dishNames = [
                        'entree' => ['Salade César', 'Carpaccio de Bœuf', 'Soupe à l\'Oignon', 'Bruschetta', 'Crevettes Grillées'],
                        'plat_principal' => ['Filet Mignon', 'Poulet Rôti', 'Saumon Grillé', 'Risotto aux Champignons', 'Bœuf Bourguignon'],
                        'dessert' => ['Crème Brûlée', 'Tarte aux Fruits', 'Mousse au Chocolat', 'Tiramisu', 'Cheesecake'],
                        'boisson' => ['Vin Rouge', 'Vin Blanc', 'Champagne', 'Jus de Fruits', 'Eau Minérale'],
                        'accompagnement' => ['Pommes de Terre', 'Légumes Grillés', 'Riz Parfumé', 'Purée de Carottes', 'Salade Verte']
                    ];

                    // Add 2-3 items of each type
                    foreach ($menuTypes as $type) {
                        $typeItems = $faker->randomElements($dishNames[$type], rand(2, 3));
                        foreach ($typeItems as $dish) {
                            // 20% des plats seront sans prix (pour tester l'affichage "Sur devis")
                            $price = $faker->boolean(20) ? 0 : rand(5000, 25000);
                            $menuItems[] = [
                                'name' => $dish,
                                'type' => $type,
                                'description' => $faker->sentence(),
                                'price' => $price
                            ];
                        }
                    }

                    $options['menu_items'] = $menuItems;
                    $options['min_guests'] = rand(10, 30);
                    $options['max_guests'] = rand(50, 200);
                }

                ProviderService::create([
                    'provider_id' => $provider->id,
                    'category_id' => $category->id,
                    'title' => $serviceTitle,
                    'description' => $faker->paragraph(),
                    'options' => json_encode($options),
                    'is_featured' => $faker->boolean(30), // 30% chance of being featured
                    'is_active' => true,
                ]);
            }
        }
    }
}
