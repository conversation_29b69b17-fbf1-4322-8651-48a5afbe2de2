<?php

namespace App\Models;

use App\Models\ProvidersModel\Provider;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Category extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
        'icon',
        'image',
        'is_active',
        'order',
        'parent_id',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'order' => 'integer',
    ];

    /**
     * Get the parent category.
     */
    public function parent()
    {
        return $this->belongsTo(Category::class, 'parent_id');
    }

    /**
     * Get the child categories.
     */
    public function children()
    {
        return $this->hasMany(Category::class, 'parent_id');
    }

    /**
     * Get all active categories.
     */
    public static function getActive()
    {
        return self::where('is_active', true)->orderBy('order')->get();
    }

    /**
     * Get all parent categories (categories without a parent).
     */
    public static function getParentCategories()
    {
        return self::whereNull('parent_id')->orderBy('order')->get();
    }

    /**
     * Get the services in this category.
     */
    public function services()
    {
        return $this->hasMany(Service::class);
    }

    /**
     * Get the providers in this category.
     */
    public function providers()
    {
        return $this->belongsToMany(Provider::class, 'provider_categories')
            ->withPivot('is_primary')
            ->withTimestamps();
    }

    /**
     * Get the URL path for the category.
     */
    public function getPath()
    {
        return '/categories/' . $this->slug;
    }

    /**
     * Get the event types associated with this category through the pivot table.
     */
    public function eventTypes()
    {
        return $this->belongsToMany(\App\Models\ClientsModel\EventType::class, 'event_type_service_categories')
            ->withPivot('is_required', 'priority', 'description')
            ->withTimestamps();
    }
}
