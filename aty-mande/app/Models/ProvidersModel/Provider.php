<?php

namespace App\Models\ProvidersModel;

use App\Models\Booking;
use App\Models\Category;
use App\Models\ClientsModel\Client;
use App\Models\Message;
use App\Models\Quote;
use App\Models\QuoteRequest;
use App\Models\Review;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Provider extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'user_id',
        'business_name',
        'business_email',
        'business_phone',
        'business_address',
        'business_registration_number',
        'description',
        'logo',
        'service_areas',
        'is_verified',
        'is_featured',
        'subscription_type',
        'subscription_expires_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'service_areas' => 'json',
        'is_verified' => 'boolean',
        'is_featured' => 'boolean',
        'subscription_expires_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * Get the user that owns the provider.
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the categories for the provider.
     */
    public function categories()
    {
        return $this->belongsToMany(Category::class, 'provider_categories')
            ->withPivot('is_primary')
            ->withTimestamps();
    }

    /**
     * Get the services for the provider.
     */
    public function services()
    {
        return $this->hasMany(ProviderService::class);
    }

    /**
     * Get the availability entries for the provider.
     */
    public function availabilities()
    {
        return $this->hasMany(\App\Models\ProviderAvailability::class);
    }

    /**
     * Get the gallery items for the provider.
     */
    public function gallery()
    {
        return $this->hasMany(ProviderGallery::class);
    }

    /**
     * Get the quote requests for the provider.
     */
    public function quoteRequests()
    {
        return $this->hasMany(QuoteRequest::class);
    }

    /**
     * Get the quotes for the provider.
     */
    public function quotes()
    {
        return $this->hasMany(Quote::class);
    }

    /**
     * Get the bookings for the provider.
     */
    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Get the reviews for the provider.
     */
    public function reviews()
    {
        return $this->hasMany(Review::class);
    }

    /**
     * Get the messages for the provider.
     */
    public function messages()
    {
        return $this->hasManyThrough(
            Message::class,
            User::class,
            'id', // Foreign key on users table...
            'sender_id', // Foreign key on messages table...
            'user_id', // Local key on providers table...
            'id' // Local key on users table...
        );
    }

    /**
     * Get the service packages for the provider.
     */
    public function servicePackages()
    {
        return $this->hasMany(\App\Models\ServicePackage::class);
    }

    /**
     * Check if the provider subscription is active
     */
    public function hasActiveSubscription(): bool
    {
        if ($this->subscription_type === 'free') {
            return true;
        }

        return $this->subscription_expires_at === null || $this->subscription_expires_at->isFuture();
    }

    /**
     * Check if the provider is available on a specific date
     */
    public function isAvailableOn($date): bool
    {
        $availability = $this->availabilities()
            ->where('date', $date)
            ->first();

        if (!$availability) {
            return true; // If no specific entry, assume available
        }

        return $availability->is_available;
    }

    /**
     * Get the service areas as an array of strings
     *
     * @return array
     */
    public function getServiceAreasArray(): array
    {
        $serviceAreas = [];

        try {
            if (empty($this->service_areas)) {
                return [];
            }

            $areas = [];

            if (is_string($this->service_areas)) {
                $decoded = json_decode($this->service_areas, true);
                if (is_array($decoded)) {
                    $areas = $decoded;
                }
            } elseif (is_array($this->service_areas)) {
                $areas = $this->service_areas;
            } elseif (is_object($this->service_areas)) {
                $areas = (array) $this->service_areas;
            }

            // S'assurer que tous les éléments sont des chaînes
            foreach ($areas as $area) {
                if (is_array($area)) {
                    // Si c'est un tableau imbriqué, on le convertit en chaîne JSON
                    $serviceAreas[] = json_encode($area);
                } elseif (is_object($area)) {
                    // Si c'est un objet, on le convertit en chaîne JSON
                    $serviceAreas[] = json_encode($area);
                } else {
                    // Sinon, on le convertit en chaîne
                    $serviceAreas[] = (string) $area;
                }
            }
        } catch (\Exception $e) {
            // En cas d'erreur, retourner un tableau vide
            return [];
        }

        return $serviceAreas;
    }
}
