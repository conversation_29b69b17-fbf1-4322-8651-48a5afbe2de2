<?php

namespace App\Models\ProvidersModel;

use App\Models\Booking;
use App\Models\Category;
use App\Models\QuoteRequest;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProviderService extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'provider_id',
        'category_id',
        'title',
        'description',
        'price',
        'is_negotiable',
        'details',
        'is_featured',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'details' => 'json',
        'price' => 'decimal:2',
        'is_negotiable' => 'boolean',
        'is_featured' => 'boolean',
        'is_active' => 'boolean',
    ];

    /**
     * Get the provider that owns the service.
     */
    public function provider()
    {
        return $this->belongsTo(Provider::class);
    }

    /**
     * Get the category that this provider service belongs to.
     */
    public function category()
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the gallery items for this provider service.
     */
    public function gallery()
    {
        return $this->hasMany(ProviderGallery::class);
    }

    /**
     * Get the quote requests for this provider service.
     */
    public function quoteRequests()
    {
        return $this->hasMany(QuoteRequest::class);
    }

    /**
     * Get the bookings for this provider service.
     */
    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Get the display price for this service.
     */
    public function getDisplayPrice()
    {
        // Si le prix est défini, l'utiliser
        if ($this->price > 0) {
            return number_format($this->price, 0, ',', ' ') . ' Ar';
        }

        // Si le prix n'est pas défini, afficher "Sur devis"
        return 'Sur devis';
    }

    /**
     * Get the real price for this service.
     */
    public function getRealPrice()
    {
        return $this->price > 0 ? $this->price : 0;
    }

    /**
     * Get all active services for a provider.
     */
    public static function getActiveForProvider($providerId)
    {
        return self::where('provider_id', $providerId)
            ->where('is_active', true)
            ->get();
    }

    /**
     * Get all featured services.
     */
    public static function getFeatured()
    {
        return self::where('is_featured', true)
            ->where('is_active', true)
            ->get();
    }

    /**
     * Get services with fixed prices.
     */
    public static function getWithFixedPrices()
    {
        return self::where('price', '>', 0)
            ->where('is_active', true)
            ->get();
    }

    /**
     * Get services within a price range.
     */
    public static function getInPriceRange($minPrice, $maxPrice)
    {
        $query = self::where('is_active', true);

        if ($minPrice) {
            $query->where('price', '>=', $minPrice);
        }

        if ($maxPrice) {
            $query->where('price', '<=', $maxPrice);
        }

        return $query->get();
    }
}
