<?php

namespace App\Models;

use App\Models\ClientsModel\EventType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class EventTypeServiceCategory extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'event_type_id',
        'category_id',
        'is_required',
        'priority',
        'description',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_required' => 'boolean',
        'priority' => 'integer',
    ];

    /**
     * Get the event type that owns the service category.
     */
    public function eventType(): BelongsTo
    {
        return $this->belongsTo(EventType::class);
    }

    /**
     * Get the category that owns the service category.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }
}
