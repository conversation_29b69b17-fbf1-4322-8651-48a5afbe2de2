<?php

namespace App\Models\ClientsModel;

use App\Models\Booking;
use App\Models\QuoteRequest;
use App\Models\ClientsModel\EventTask;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Event extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'client_id',
        'event_type_id',
        'title',
        'description',
        'date',
        'start_time',
        'end_time',
        'location',
        'address',
        'city',
        'state',
        'postal_code',
        'country',
        'guest_count',
        'budget',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'date' => 'date',
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'guest_count' => 'integer',
        'budget' => 'decimal:2',
    ];

    /**
     * Get the client that owns the event.
     */
    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Get the event type for this event.
     */
    public function eventType()
    {
        return $this->belongsTo(EventType::class);
    }

    /**
     * Get the quote requests for this event.
     */
    public function quoteRequests()
    {
        return $this->hasMany(QuoteRequest::class);
    }

    /**
     * Get the bookings for this event.
     */
    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    /**
     * Get the tasks for this event.
     */
    public function tasks()
    {
        return $this->hasMany(EventTask::class);
    }

    /**
     * Get the full address as a string.
     */
    public function getFullAddress()
    {
        $parts = [];

        if ($this->address) {
            $parts[] = $this->address;
        }

        $cityState = [];
        if ($this->city) {
            $cityState[] = $this->city;
        }
        if ($this->state) {
            $cityState[] = $this->state;
        }

        if (!empty($cityState)) {
            $parts[] = implode(', ', $cityState);
        }

        if ($this->postal_code) {
            $parts[] = $this->postal_code;
        }

        if ($this->country) {
            $parts[] = $this->country;
        }

        return implode(', ', $parts);
    }

    /**
     * Get the formatted date and time.
     */
    public function getFormattedDateTime()
    {
        $result = $this->date->format('F j, Y');

        if ($this->start_time) {
            $result .= ' at ' . $this->start_time->format('g:i A');

            if ($this->end_time) {
                $result .= ' - ' . $this->end_time->format('g:i A');
            }
        }

        return $result;
    }

    /**
     * Get upcoming events for a client.
     */
    public static function getUpcomingForClient($clientId)
    {
        return self::where('client_id', $clientId)
            ->where('date', '>=', now()->format('Y-m-d'))
            ->orderBy('date')
            ->get();
    }

    /**
     * Get past events for a client.
     */
    public static function getPastForClient($clientId)
    {
        return self::where('client_id', $clientId)
            ->where('date', '<', now()->format('Y-m-d'))
            ->orderBy('date', 'desc')
            ->get();
    }

    /**
     * Calculate the total budget used for this event.
     *
     * @return float
     */
    public function getBudgetUsed()
    {
        // Somme des montants des réservations confirmées
        $bookingsAmount = $this->bookings()
            ->whereIn('status', ['confirmed', 'completed'])
            ->sum('total_amount');

        // Somme des coûts réels des tâches complétées qui ne sont pas liées à des réservations
        $tasksAmount = $this->tasks()
            ->where('status', 'completed')
            ->whereNotNull('actual_cost')
            ->sum('actual_cost');

        return $bookingsAmount + $tasksAmount;
    }

    /**
     * Calculate the remaining budget for this event.
     *
     * @return float
     */
    public function getRemainingBudget()
    {
        if (!$this->budget) {
            return 0;
        }

        return max(0, $this->budget - $this->getBudgetUsed());
    }

    /**
     * Calculate the budget usage percentage.
     *
     * @return int
     */
    public function getBudgetPercentage()
    {
        if (!$this->budget || $this->budget <= 0) {
            return 0;
        }

        return min(100, round(($this->getBudgetUsed() / $this->budget) * 100));
    }

    /**
     * Calculate the tasks completion percentage.
     *
     * @return int
     */
    public function getTasksCompletionPercentage()
    {
        $totalTasks = $this->tasks()->count();

        if ($totalTasks <= 0) {
            return 0;
        }

        $completedTasks = $this->tasks()
            ->where('status', 'completed')
            ->count();

        return round(($completedTasks / $totalTasks) * 100);
    }

    /**
     * Calculate the overall event progress percentage.
     *
     * @return int
     */
    public function getOverallProgressPercentage()
    {
        // Pondération : 40% pour les tâches, 30% pour le budget, 30% pour la proximité de la date
        $tasksWeight = 0.4;
        $budgetWeight = 0.3;
        $dateWeight = 0.3;

        // Progression des tâches
        $tasksProgress = $this->getTasksCompletionPercentage();

        // Progression du budget (considéré comme 100% si aucun budget n'est défini)
        $budgetProgress = $this->budget ? $this->getBudgetPercentage() : 100;

        // Progression de la date (pourcentage du temps écoulé depuis la création jusqu'à la date de l'événement)
        $dateProgress = 0;
        $today = now();
        $eventDate = $this->date;
        $creationDate = $this->created_at;

        if ($eventDate && $creationDate) {
            if ($today >= $eventDate) {
                $dateProgress = 100;
            } else {
                $totalDays = $eventDate->diffInDays($creationDate);
                if ($totalDays > 0) {
                    $daysElapsed = $today->diffInDays($creationDate);
                    $dateProgress = min(100, round(($daysElapsed / $totalDays) * 100));
                }
            }
        }

        // Calcul de la progression globale pondérée
        return round(
            ($tasksProgress * $tasksWeight) +
            ($budgetProgress * $budgetWeight) +
            ($dateProgress * $dateWeight)
        );
    }

    /**
     * Get the tasks related to a specific service category.
     *
     * @param string $category
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getTasksByCategory($category)
    {
        return $this->tasks()
            ->where('category', $category)
            ->get();
    }

    /**
     * Find a task related to a specific service.
     *
     * @param int $serviceId
     * @return \App\Models\ClientsModel\EventTask|null
     */
    public function findTaskForService($serviceId)
    {
        return $this->tasks()
            ->where('description', 'like', '%Service: %')
            ->where('description', 'like', "%provider_service_id: {$serviceId}%")
            ->first();
    }
}
