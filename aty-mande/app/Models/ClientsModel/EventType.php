<?php

namespace App\Models\ClientsModel;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EventType extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'slug',
        'description',
        'icon',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Get the events of this type.
     */
    public function events()
    {
        return $this->hasMany(Event::class);
    }

    /**
     * Get all active event types.
     */
    public static function getActive()
    {
        return self::where('is_active', true)->get();
    }

    /**
     * Get the URL path for the event type.
     */
    public function getPath()
    {
        return '/event-types/' . $this->slug;
    }

    /**
     * Get the service categories associated with this event type.
     */
    public function serviceCategories()
    {
        return $this->hasMany(\App\Models\EventTypeServiceCategory::class);
    }

    /**
     * Get the categories associated with this event type through the pivot table.
     */
    public function categories()
    {
        return $this->belongsToMany(\App\Models\Category::class, 'event_type_service_categories')
            ->withPivot('is_required', 'priority', 'description')
            ->orderBy('priority', 'desc')
            ->withTimestamps();
    }
}
