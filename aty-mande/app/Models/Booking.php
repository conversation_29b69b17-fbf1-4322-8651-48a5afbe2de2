<?php

namespace App\Models;

use App\Models\ClientsModel\Client;
use App\Models\ClientsModel\Event;
use App\Models\ProvidersModel\Provider;
use App\Models\ProvidersModel\ProviderAvailability;
use App\Models\ProvidersModel\ProviderService;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Booking extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'client_id',
        'provider_id',
        'provider_service_id',
        'event_id',
        'quote_id',
        'booking_number',
        'title',
        'description',
        'event_date',
        'event_start_time',
        'event_end_time',
        'event_location',
        'total_amount',
        'deposit_amount',
        'paid_amount',
        'balance_amount',
        'payment_status',
        'status',
        'cancellation_reason',
        'confirmed_at',
        'completed_at',
        'cancelled_at',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'event_date' => 'date',
        'event_start_time' => 'datetime',
        'event_end_time' => 'datetime',
        'total_amount' => 'decimal:2',
        'deposit_amount' => 'decimal:2',
        'paid_amount' => 'decimal:2',
        'balance_amount' => 'decimal:2',
        'confirmed_at' => 'datetime',
        'completed_at' => 'datetime',
        'cancelled_at' => 'datetime',
    ];

    /**
     * The "booting" method of the model.
     */
    protected static function boot()
    {
        parent::boot();

        // Generate a unique booking number when creating a new booking
        static::creating(function ($booking) {
            $booking->booking_number = 'BK-' . strtoupper(Str::random(8));
        });
    }

    /**
     * Get the client that made the booking.
     */
    public function client()
    {
        return $this->belongsTo(Client::class);
    }

    /**
     * Get the provider that received the booking.
     */
    public function provider()
    {
        return $this->belongsTo(Provider::class);
    }

    /**
     * Get the provider service that the booking is for.
     */
    public function providerService()
    {
        return $this->belongsTo(ProviderService::class);
    }

    /**
     * Get the event that the booking is for.
     */
    public function event()
    {
        return $this->belongsTo(Event::class);
    }

    /**
     * Get the quote that led to this booking.
     */
    public function quote()
    {
        return $this->belongsTo(Quote::class);
    }

    /**
     * Get the messages for this booking.
     */
    public function messages()
    {
        return $this->hasMany(Message::class);
    }

    /**
     * Get the review for this booking.
     */
    public function review()
    {
        return $this->hasOne(Review::class);
    }

    /**
     * Confirm the booking.
     */
    public function confirm()
    {
        $this->status = 'confirmed';
        $this->confirmed_at = now();
        $this->save();

        // Update provider availability
        if ($this->provider_id) {
            ProviderAvailability::markAsBooked(
                $this->provider_id,
                $this->event_date,
                $this->event_start_time,
                $this->event_end_time,
                "Booking #{$this->booking_number}"
            );
        }
    }

    /**
     * Mark the booking as completed.
     */
    public function complete()
    {
        $this->status = 'completed';
        $this->completed_at = now();
        $this->save();
    }

    /**
     * Cancel the booking.
     */
    public function cancel($reason = null)
    {
        $this->status = 'cancelled';
        $this->cancellation_reason = $reason;
        $this->cancelled_at = now();
        $this->save();
    }

    /**
     * Get the payments for this booking.
     */
    public function payments()
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the invoices for this booking.
     */
    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }

    /**
     * Record a payment for the booking.
     */
    public function recordPayment($amount, $paymentMethodId, $transactionId = null, $paymentDetails = null)
    {
        // Create a reference number
        $referenceNumber = 'PAY-' . strtoupper(Str::random(8));

        // Create the payment record
        $payment = new Payment([
            'booking_id' => $this->id,
            'payment_method_id' => $paymentMethodId,
            'transaction_id' => $transactionId,
            'reference_number' => $referenceNumber,
            'amount' => $amount,
            'status' => 'completed',
            'payment_type' => $this->paid_amount == 0 ? 'deposit' : 'balance',
            'payment_details' => $paymentDetails,
            'paid_at' => now(),
        ]);
        $payment->save();

        // Update the booking payment status
        $this->paid_amount += $amount;
        $this->balance_amount = $this->total_amount - $this->paid_amount;

        if ($this->paid_amount >= $this->total_amount) {
            $this->payment_status = 'paid';
        } elseif ($this->paid_amount > 0) {
            $this->payment_status = 'partially_paid';
        }

        $this->save();

        // Update the invoice if it exists
        if ($this->invoices()->exists()) {
            $invoice = $this->invoices()->latest()->first();
            $invoice->paid_amount += $amount;
            $invoice->due_amount = $invoice->total_amount - $invoice->paid_amount;

            if ($invoice->paid_amount >= $invoice->total_amount) {
                $invoice->status = 'paid';
            } elseif ($invoice->paid_amount > 0) {
                $invoice->status = 'partially_paid';
            }

            $invoice->save();

            // Link the payment to the invoice
            $payment->invoice_id = $invoice->id;
            $payment->save();
        }

        return $payment;
    }

    /**
     * Check if the booking has been confirmed.
     */
    public function isConfirmed()
    {
        return $this->status === 'confirmed' || $this->status === 'in_progress' || $this->status === 'completed';
    }

    /**
     * Check if the booking has been completed.
     */
    public function isCompleted()
    {
        return $this->status === 'completed';
    }

    /**
     * Check if the booking has been cancelled.
     */
    public function isCancelled()
    {
        return $this->status === 'cancelled';
    }

    /**
     * Check if the booking has been paid in full.
     */
    public function isPaid()
    {
        return $this->payment_status === 'paid';
    }

    /**
     * Get the formatted date and time.
     */
    public function getFormattedDateTime()
    {
        $result = $this->event_date->format('F j, Y');

        if ($this->event_start_time) {
            $result .= ' at ' . $this->event_start_time->format('g:i A');

            if ($this->event_end_time) {
                $result .= ' - ' . $this->event_end_time->format('g:i A');
            }
        }

        return $result;
    }

    /**
     * Get upcoming bookings for a provider.
     */
    public static function getUpcomingForProvider($providerId)
    {
        return self::where('provider_id', $providerId)
            ->whereIn('status', ['pending', 'confirmed', 'in_progress'])
            ->where('event_date', '>=', now()->format('Y-m-d'))
            ->orderBy('event_date')
            ->get();
    }

    /**
     * Get upcoming bookings for a client.
     */
    public static function getUpcomingForClient($clientId)
    {
        return self::where('client_id', $clientId)
            ->whereIn('status', ['pending', 'confirmed', 'in_progress'])
            ->where('event_date', '>=', now()->format('Y-m-d'))
            ->orderBy('event_date')
            ->get();
    }
}
