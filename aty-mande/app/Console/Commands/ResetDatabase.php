<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class ResetDatabase extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'db:reset {--seed : Seed the database after migration}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Reset the database and run migrations';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        if ($this->confirm('ATTENTION: Cela va supprimer toutes les données de la base de données. Êtes-vous sûr de vouloir continuer?', false)) {
            // Désactiver les contraintes de clé étrangère
            DB::statement('SET FOREIGN_KEY_CHECKS=0');

            // Récupérer toutes les tables
            $tables = DB::select('SHOW TABLES');
            $tableColumn = 'Tables_in_' . env('DB_DATABASE');

            // Supprimer toutes les tables
            foreach ($tables as $table) {
                Schema::dropIfExists($table->$tableColumn);
            }

            // Réactiver les contraintes de clé étrangère
            DB::statement('SET FOREIGN_KEY_CHECKS=1');

            $this->info('Toutes les tables ont été supprimées.');

            // Exécuter les migrations
            $this->info('Exécution des migrations...');
            Artisan::call('migrate', ['--force' => true]);
            $this->info('Migrations terminées.');

            // Exécuter les seeders si l'option --seed est spécifiée
            if ($this->option('seed')) {
                $this->info('Exécution des seeders...');
                Artisan::call('db:seed', ['--force' => true]);
                $this->info('Seeders terminés.');
            }

            $this->info('La base de données a été réinitialisée avec succès.');
        } else {
            $this->info('Opération annulée.');
        }
    }
}
