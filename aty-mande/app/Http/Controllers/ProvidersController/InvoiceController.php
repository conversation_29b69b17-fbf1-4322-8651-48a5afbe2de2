<?php

namespace App\Http\Controllers\ProvidersController;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class InvoiceController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('provider');
    }

    /**
     * Display a listing of the provider's invoices.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $provider = auth()->user()->provider;

        $invoices = \App\Models\Invoice::where('provider_id', $provider->id)
            ->with(['booking', 'client', 'client.user'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('ProvidersView.dashboard.invoices.index', compact('invoices'));
    }

    /**
     * Display the specified invoice.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $provider = auth()->user()->provider;

        $invoice = \App\Models\Invoice::where('id', $id)
            ->where('provider_id', $provider->id)
            ->with(['booking', 'client', 'client.user', 'payments'])
            ->firstOrFail();

        return view('ProvidersView.dashboard.invoices.show', compact('invoice'));
    }

    /**
     * Show the form for creating a new invoice.
     *
     * @param  int  $bookingId
     * @return \Illuminate\View\View
     */
    public function create($bookingId)
    {
        $provider = auth()->user()->provider;

        $booking = \App\Models\Booking::where('id', $bookingId)
            ->where('provider_id', $provider->id)
            ->with(['client', 'client.user', 'providerService'])
            ->firstOrFail();

        // Check if an invoice already exists
        if ($booking->invoices()->exists()) {
            return redirect()->route('provider.invoices.show', $booking->invoices()->latest()->first()->id)
                ->with('info', 'Une facture existe déjà pour cette réservation.');
        }

        return view('ProvidersView.dashboard.invoices.create', compact('booking'));
    }

    /**
     * Store a newly created invoice in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $bookingId
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request, $bookingId)
    {
        $request->validate([
            'subtotal_amount' => 'required|numeric|min:0',
            'tax_amount' => 'required|numeric|min:0',
            'discount_amount' => 'required|numeric|min:0',
            'due_date' => 'required|date|after:today',
            'notes' => 'nullable|string|max:500',
        ]);

        $provider = auth()->user()->provider;

        $booking = \App\Models\Booking::where('id', $bookingId)
            ->where('provider_id', $provider->id)
            ->firstOrFail();

        // Check if an invoice already exists
        if ($booking->invoices()->exists()) {
            return redirect()->route('provider.invoices.show', $booking->invoices()->latest()->first()->id)
                ->with('info', 'Une facture existe déjà pour cette réservation.');
        }

        // Calculate total amount
        $totalAmount = $request->subtotal_amount + $request->tax_amount - $request->discount_amount;

        // Generate a unique invoice number
        $invoiceNumber = 'INV-' . strtoupper(\Illuminate\Support\Str::random(8));

        // Create the invoice
        $invoice = new \App\Models\Invoice([
            'booking_id' => $booking->id,
            'client_id' => $booking->client_id,
            'provider_id' => $provider->id,
            'invoice_number' => $invoiceNumber,
            'subtotal_amount' => $request->subtotal_amount,
            'tax_amount' => $request->tax_amount,
            'discount_amount' => $request->discount_amount,
            'total_amount' => $totalAmount,
            'paid_amount' => $booking->paid_amount,
            'due_amount' => $totalAmount - $booking->paid_amount,
            'status' => $booking->isPaid() ? 'paid' : ($booking->paid_amount > 0 ? 'partially_paid' : 'pending'),
            'due_date' => $request->due_date,
            'notes' => $request->notes,
        ]);

        // Generate QR code
        $qrCodeData = json_encode([
            'invoice_number' => $invoiceNumber,
            'amount' => $totalAmount,
            'client' => $booking->client->user->name,
            'provider' => $provider->business_name,
            'date' => now()->format('Y-m-d'),
        ]);

        $invoice->qr_code = $this->generateQrCode($qrCodeData);
        $invoice->save();

        return redirect()->route('provider.invoices.show', $invoice->id)
            ->with('status', 'Facture créée avec succès.');
    }

    /**
     * Download the specified invoice as PDF.
     *
     * @param  int  $id
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function download($id)
    {
        $provider = auth()->user()->provider;

        $invoice = \App\Models\Invoice::where('id', $id)
            ->where('provider_id', $provider->id)
            ->with(['booking', 'client', 'client.user', 'payments'])
            ->firstOrFail();

        // In a real application, you would generate a PDF here
        // For this example, we'll just return a view
        return view('ProvidersView.dashboard.invoices.pdf', compact('invoice'));
    }

    /**
     * Generate a QR code for an invoice.
     *
     * @param  string  $data
     * @return string
     */
    private function generateQrCode($data)
    {
        // In a real application, you would use a QR code library
        // For this example, we'll just return a placeholder
        return 'qr_code_placeholder';
    }
}
