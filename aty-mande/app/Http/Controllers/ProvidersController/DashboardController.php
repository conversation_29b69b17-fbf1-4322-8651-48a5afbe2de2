<?php

namespace App\Http\Controllers\ProvidersController;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\ProvidersModel\ProviderService;
use App\Models\Review;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    /**
     * Display the provider dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $provider = Auth::user()->provider;

        // Get recent bookings
        $recentBookings = Booking::where('provider_id', $provider->id)
            ->with(['client', 'providerService'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get recent reviews
        $recentReviews = Review::where('provider_id', $provider->id)
            ->with(['client', 'booking'])
            ->orderBy('created_at', 'desc')
            ->take(5)
            ->get();

        // Get counts for dashboard stats
        $stats = [
            'services' => ProviderService::where('provider_id', $provider->id)->count(),
            'bookings' => Booking::where('provider_id', $provider->id)->count(),
            'active_bookings' => Booking::where('provider_id', $provider->id)
                ->whereIn('status', ['pending', 'confirmed', 'in_progress'])
                ->count(),
            'reviews' => Review::where('provider_id', $provider->id)->count(),
            'avg_rating' => Review::where('provider_id', $provider->id)->avg('rating') ?? 0,
        ];

        return view('ProvidersView.dashboard.index', compact(
            'recentBookings',
            'recentReviews',
            'stats'
        ));
    }
}
