<?php

namespace App\Http\Controllers\ProvidersController;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\ProvidersModel\Provider;
use App\Models\ProvidersModel\ProviderService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class ServiceController extends Controller
{
    /**
     * Display a listing of the provider's services.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $provider = Auth::user()->provider;
        $services = ProviderService::where('provider_id', $provider->id)
            ->with('category')
            ->orderBy('is_featured', 'desc')
            ->orderBy('created_at', 'desc')
            ->get();

        return view('ProvidersView.services.index', compact('services'));
    }

    /**
     * Show the form for creating a new service.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $provider = Auth::user()->provider;

        // Récupérer uniquement les catégories que le prestataire a choisies lors de l'inscription
        $categories = Category::whereHas('providers', function($query) use ($provider) {
            $query->where('providers.id', $provider->id);
        })->get();

        // Récupérer toutes les catégories pour le message d'information
        $allCategories = Category::all();

        return view('ProvidersView.services.create', compact('categories', 'allCategories'));
    }

    /**
     * Store a newly created service in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $provider = Auth::user()->provider;

        // Règles de validation de base pour tous les services
        $rules = [
            'category_id' => 'required|exists:categories,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            'price' => 'nullable|numeric|min:0', // Prix optionnel, si non spécifié, affichera "Sur devis"
            'is_negotiable' => 'boolean',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'images.*' => 'nullable|image|max:2048',
        ];

        // Récupérer la catégorie
        $category = Category::find($request->category_id);
        if ($category) {
            // Ajouter des règles de validation spécifiques en fonction de la catégorie
            switch ($category->name) {
                case 'Traiteur':
                    $rules = array_merge($rules, [
                        'menu_items' => 'required|array',
                        'menu_items.*.name' => 'required|string|max:255',
                        'menu_items.*.description' => 'nullable|string',
                        'menu_items.*.type' => 'required|in:entree,plat_principal,dessert,boisson,accompagnement,autre',
                        'menu_items.*.price' => 'nullable|numeric|min:0', // Prix optionnel, si non spécifié, affichera "Sur devis"
                        'min_guests' => 'required|integer|min:1',
                        'max_guests' => 'required|integer|min:1|gte:min_guests',
                        'dietary_options' => 'nullable|array',
                        'dietary_options.*' => 'in:vegetarian,vegan,gluten_free',
                    ]);
                    break;

                case 'Photographie':
                    $rules = array_merge($rules, [
                        'duration' => 'required|numeric|min:0.5',
                        'num_photos' => 'required|integer|min:1',
                        'deliverables' => 'required|array',
                        'deliverables.*' => 'in:digital_files,printed_photos,photo_album',
                        'equipment' => 'nullable|string',
                        'photo_styles' => 'nullable|array',
                        'photo_styles.*' => 'in:traditional,photojournalistic,artistic',
                    ]);
                    break;

                case 'Vidéographie':
                    $rules = array_merge($rules, [
                        'duration' => 'required|numeric|min:0.5',
                        'final_video_length' => 'required|integer|min:1',
                        'delivery_time' => 'required|integer|min:1',
                        'video_formats' => 'required|array',
                        'video_formats.*' => 'in:highlight,documentary,raw',
                        'equipment' => 'nullable|string',
                        'video_services' => 'nullable|array',
                        'video_services.*' => 'in:drone,same_day_edit,live_streaming',
                    ]);
                    break;

                case 'Espaces de Réception':
                    $rules = array_merge($rules, [
                        'capacity' => 'required|integer|min:1',
                        'venue_size' => 'nullable|numeric|min:1',
                        'venue_type' => 'required|in:indoor,outdoor,both',
                        'amenities' => 'nullable|array',
                        'amenities.*' => 'in:tables,sound,lighting,parking,kitchen,wifi',
                        'venue_rules' => 'nullable|string',
                    ]);
                    break;

                case 'Musique & Animation':
                    $rules = array_merge($rules, [
                        'music_type' => 'required|in:dj,band,artist,mc,animator,other',
                        'duration' => 'required|numeric|min:0.5',
                        'music_genres' => 'nullable|array',
                        'music_genres.*' => 'in:pop,rock,jazz,traditional,rnb,electronic',
                        'band_composition' => 'nullable|string|required_if:music_type,band',
                        'music_equipment' => 'nullable|array',
                        'music_equipment.*' => 'in:sound_system,lighting,microphones,instruments',
                    ]);
                    break;

                case 'Transport':
                    $rules = array_merge($rules, [
                        'vehicle_type' => 'required|in:sedan,suv,van,bus,limousine,other',
                        'num_seats' => 'required|integer|min:1',
                        'transport_options' => 'nullable|array',
                        'transport_options.*' => 'in:cortege_lead,fuel_included,decoration',
                        'transport_pricing_model' => 'required|in:hourly,per_trip',
                        'vehicle_details' => 'nullable|string',
                    ]);
                    break;
            }
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            // Récupérer la catégorie pour rediriger vers le bon onglet
            $categoryId = $request->category_id;

            // Récupérer toutes les erreurs de validation
            $errors = $validator->errors()->all();
            $errorMessage = implode('<br>', $errors);

            return redirect()->back()
                ->withErrors($validator)
                ->withInput()
                ->with('active_tab', $categoryId) // Pour activer le bon onglet
                ->with('error', $errorMessage);
        }

        // Traiter les détails du service
        $details = $this->processServiceDetails($request);

        // Aucun traitement supplémentaire nécessaire pour le prix, car il est maintenant stocké directement dans la colonne 'price'

        // Create the service
        $service = ProviderService::create([
            'provider_id' => $provider->id,
            'category_id' => $request->category_id,
            'title' => $request->title,
            'description' => $request->description,
            'price' => $request->price,
            'is_negotiable' => $request->has('is_negotiable'),
            'details' => $details,
            'is_featured' => $request->has('is_featured'),
            'is_active' => $request->has('is_active') ? true : false,
        ]);

        // Handle image uploads
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $index => $image) {
                $path = $image->store('provider_services', 'public');

                $service->gallery()->create([
                    'provider_id' => $provider->id,
                    'title' => $request->image_titles[$index] ?? $service->title . ' - Image ' . ($index + 1),
                    'description' => $request->image_descriptions[$index] ?? null,
                    'image_path' => $path,
                    'type' => 'image',
                    'is_featured' => $index === 0, // First image is featured
                    'order' => $index,
                ]);
            }
        }

        return redirect()->route('provider.services')
            ->with('success', 'Service créé avec succès.');
    }

    /**
     * Display the specified service.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $provider = Auth::user()->provider;
        $service = ProviderService::where('id', $id)
            ->where('provider_id', $provider->id)
            ->with(['category', 'gallery'])
            ->firstOrFail();

        return view('ProvidersView.services.show', compact('service'));
    }

    /**
     * Show the form for editing the specified service.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $provider = Auth::user()->provider;
        $service = ProviderService::where('id', $id)
            ->where('provider_id', $provider->id)
            ->with(['category', 'gallery'])
            ->firstOrFail();

        // Récupérer uniquement les catégories que le prestataire a choisies lors de l'inscription
        $categories = Category::whereHas('providers', function($query) use ($provider) {
            $query->where('providers.id', $provider->id);
        })->get();

        // Récupérer toutes les catégories pour le message d'information
        $allCategories = Category::all();

        return view('ProvidersView.services.edit', compact('service', 'categories', 'allCategories'));
    }

    /**
     * Update the specified service in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $provider = Auth::user()->provider;
        $service = ProviderService::where('id', $id)
            ->where('provider_id', $provider->id)
            ->firstOrFail();

        // Règles de validation de base pour tous les services
        $rules = [
            'category_id' => 'required|exists:categories,id',
            'title' => 'required|string|max:255',
            'description' => 'required|string',
            // Nous n'utilisons plus pricing_type, tout est stocké dans la colonne price
            'price' => 'nullable|numeric|min:0', // Prix optionnel, si non spécifié, affichera "Sur devis"
            'min_price' => 'nullable|numeric|min:0',
            'max_price' => 'nullable|numeric|min:0|gte:min_price',
            'is_featured' => 'boolean',
            'is_active' => 'boolean',
            'images.*' => 'nullable|image|max:2048',
        ];

        // Récupérer la catégorie
        $category = Category::find($request->category_id);
        if ($category) {
            // Ajouter des règles de validation spécifiques en fonction de la catégorie
            switch ($category->name) {
                case 'Traiteur':
                    $rules = array_merge($rules, [
                        'menu_items' => 'required|array',
                        'menu_items.*.name' => 'required|string|max:255',
                        'menu_items.*.description' => 'nullable|string',
                        'menu_items.*.type' => 'required|in:entree,plat_principal,dessert,boisson,accompagnement,autre',
                        'menu_items.*.price' => 'nullable|numeric|min:0', // Prix optionnel, si non spécifié, affichera "Sur devis"
                        'min_guests' => 'required|integer|min:1',
                        'max_guests' => 'required|integer|min:1|gte:min_guests',
                        'dietary_options' => 'nullable|array',
                        'dietary_options.*' => 'in:vegetarian,vegan,gluten_free',
                    ]);
                    break;

                case 'Photographie':
                    $rules = array_merge($rules, [
                        'duration' => 'required|numeric|min:0.5',
                        'num_photos' => 'required|integer|min:1',
                        'deliverables' => 'required|array',
                        'deliverables.*' => 'in:digital_files,printed_photos,photo_album',
                        'equipment' => 'nullable|string',
                        'photo_styles' => 'nullable|array',
                        'photo_styles.*' => 'in:traditional,photojournalistic,artistic',
                    ]);
                    break;

                case 'Vidéographie':
                    $rules = array_merge($rules, [
                        'duration' => 'required|numeric|min:0.5',
                        'final_video_length' => 'required|integer|min:1',
                        'delivery_time' => 'required|integer|min:1',
                        'video_formats' => 'required|array',
                        'video_formats.*' => 'in:highlight,documentary,raw',
                        'equipment' => 'nullable|string',
                        'video_services' => 'nullable|array',
                        'video_services.*' => 'in:drone,same_day_edit,live_streaming',
                    ]);
                    break;

                case 'Espaces de Réception':
                    $rules = array_merge($rules, [
                        'capacity' => 'required|integer|min:1',
                        'venue_size' => 'nullable|numeric|min:1',
                        'venue_type' => 'required|in:indoor,outdoor,both',
                        'amenities' => 'nullable|array',
                        'amenities.*' => 'in:tables,sound,lighting,parking,kitchen,wifi',
                        'venue_rules' => 'nullable|string',
                    ]);
                    break;

                case 'Musique & Animation':
                    $rules = array_merge($rules, [
                        'music_type' => 'required|in:dj,band,artist,mc,animator,other',
                        'duration' => 'required|numeric|min:0.5',
                        'music_genres' => 'nullable|array',
                        'music_genres.*' => 'in:pop,rock,jazz,traditional,rnb,electronic',
                        'band_composition' => 'nullable|string|required_if:music_type,band',
                        'music_equipment' => 'nullable|array',
                        'music_equipment.*' => 'in:sound_system,lighting,microphones,instruments',
                    ]);
                    break;

                case 'Transport':
                    $rules = array_merge($rules, [
                        'vehicle_type' => 'required|in:sedan,suv,van,bus,limousine,other',
                        'num_seats' => 'required|integer|min:1',
                        'transport_options' => 'nullable|array',
                        'transport_options.*' => 'in:cortege_lead,fuel_included,decoration',
                        'transport_pricing_model' => 'required|in:hourly,per_trip',
                        'vehicle_details' => 'nullable|string',
                    ]);
                    break;
            }
        }

        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            // Récupérer toutes les erreurs de validation
            $errors = $validator->errors()->all();
            $errorMessage = implode('<br>', $errors);

            return redirect()->back()
                ->withErrors($validator)
                ->withInput()
                ->with('error', $errorMessage);
        }

        // Traiter les détails du service (sans les informations de prix)
        $details = $this->processServiceDetails($request);

        // Log pour débogage
        \Log::info('Mise à jour du service', [
            'service_id' => $service->id,
            'category' => $service->category->name,
            'price' => $request->price,
            'details' => $details,
            'is_active' => $request->has('is_active') ? true : false
        ]);

        // Update the service
        $service->update([
            'category_id' => $request->category_id,
            'title' => $request->title,
            'description' => $request->description,
            'price' => $request->price,
            'is_negotiable' => $request->has('is_negotiable'),
            'details' => $details,
            'is_featured' => $request->has('is_featured'),
            'is_active' => $request->has('is_active') ? true : false,
        ]);

        // Handle image uploads
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $index => $image) {
                $path = $image->store('provider_services', 'public');

                $service->gallery()->create([
                    'provider_id' => $provider->id,
                    'title' => $request->image_titles[$index] ?? $service->title . ' - Image ' . ($index + 1),
                    'description' => $request->image_descriptions[$index] ?? null,
                    'image_path' => $path,
                    'type' => 'image',
                    'is_featured' => false,
                    'order' => $service->gallery->count() + $index,
                ]);
            }
        }

        // Handle image deletions
        if ($request->has('delete_images')) {
            foreach ($request->delete_images as $imageId) {
                $image = $service->gallery()->find($imageId);
                if ($image) {
                    Storage::disk('public')->delete($image->image_path);
                    $image->delete();
                }
            }
        }

        return redirect()->route('provider.services')
            ->with('success', 'Service mis à jour avec succès.');
    }

    /**
     * Remove the specified service from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $provider = Auth::user()->provider;
        $service = ProviderService::where('id', $id)
            ->where('provider_id', $provider->id)
            ->firstOrFail();

        // Delete gallery images
        foreach ($service->gallery as $image) {
            Storage::disk('public')->delete($image->image_path);
        }

        $service->gallery()->delete();
        $service->delete();

        return redirect()->route('provider.services')
            ->with('success', 'Service supprimé avec succès.');
    }

    /**
     * Process service details based on service type.
     *
     * Cette méthode traite les détails spécifiques à chaque type de service
     * et les stocke dans un tableau JSON qui sera enregistré dans la base de données.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    private function processServiceDetails(Request $request)
    {
        $details = [];

        // Récupérer la catégorie
        $category = Category::find($request->category_id);
        if (!$category) {
            return $details;
        }

        // Process details based on category name
        switch ($category->name) {
            // Traiteur
            case 'Traiteur':
                // Options de menu
                if ($request->has('menu_items')) {
                    $details['menu_items'] = $request->menu_items;
                }

                // Capacité
                if ($request->has('min_guests')) {
                    $details['min_guests'] = (int) $request->min_guests;
                }

                if ($request->has('max_guests')) {
                    $details['max_guests'] = (int) $request->max_guests;
                }

                // Options alimentaires
                if ($request->has('dietary_options')) {
                    $details['dietary_options'] = $request->dietary_options;
                }
                break;

            // Photographie
            case 'Photographie':
                // Durée de la prestation
                if ($request->has('duration')) {
                    $details['duration'] = (float) $request->duration;
                }

                // Nombre de photos
                if ($request->has('num_photos')) {
                    $details['num_photos'] = (int) $request->num_photos;
                }

                // Livrables
                if ($request->has('deliverables')) {
                    $details['deliverables'] = $request->deliverables;
                }

                // Équipement
                if ($request->has('equipment')) {
                    $details['equipment'] = $request->equipment;
                }

                // Styles de photographie
                if ($request->has('photo_styles')) {
                    $details['photo_styles'] = $request->photo_styles;
                }
                break;

            // Vidéographie
            case 'Vidéographie':
                // Durée de la prestation
                if ($request->has('duration')) {
                    $details['duration'] = (float) $request->duration;
                }

                // Durée de la vidéo finale
                if ($request->has('final_video_length')) {
                    $details['final_video_length'] = (int) $request->final_video_length;
                }

                // Délai de livraison
                if ($request->has('delivery_time')) {
                    $details['delivery_time'] = (int) $request->delivery_time;
                }

                // Formats de vidéo
                if ($request->has('video_formats')) {
                    $details['video_formats'] = $request->video_formats;
                }

                // Équipement
                if ($request->has('equipment')) {
                    $details['equipment'] = $request->equipment;
                }

                // Services additionnels
                if ($request->has('video_services')) {
                    $details['video_services'] = $request->video_services;
                }
                break;

            // Espaces de Réception
            case 'Espaces de Réception':
                // Log pour débogage
                \Log::info('Traitement des options pour Espaces de Réception');
                \Log::info('Données reçues:', $request->all());

                // Capacité
                if ($request->has('capacity')) {
                    $details['capacity'] = (int) $request->capacity;
                    \Log::info('Capacité définie:', ['capacity' => $details['capacity']]);
                } else {
                    \Log::info('Capacité non fournie dans la requête');
                }

                // Superficie
                if ($request->has('venue_size')) {
                    $details['venue_size'] = (float) $request->venue_size;
                    \Log::info('Superficie définie:', ['venue_size' => $details['venue_size']]);
                } else {
                    \Log::info('Superficie non fournie dans la requête');
                }

                // Type d'espace
                if ($request->has('venue_type')) {
                    $details['venue_type'] = $request->venue_type;
                    \Log::info('Type d\'espace défini:', ['venue_type' => $details['venue_type']]);
                } else {
                    \Log::info('Type d\'espace non fourni dans la requête');
                }

                // Équipements inclus
                if ($request->has('amenities')) {
                    $details['amenities'] = $request->amenities;
                    \Log::info('Équipements définis:', ['amenities' => $details['amenities']]);
                } else {
                    \Log::info('Équipements non fournis dans la requête');
                }

                // Règles et restrictions
                if ($request->has('venue_rules')) {
                    $details['venue_rules'] = $request->venue_rules;
                    \Log::info('Règles définies:', ['venue_rules' => $details['venue_rules']]);
                } else {
                    \Log::info('Règles non fournies dans la requête');
                }

                // Nous ne stockons plus les prix dans les détails
                \Log::info('Les prix sont maintenant stockés dans la colonne price');

                break;

            // Musique & Animation
            case 'Musique & Animation':
                // Type de service
                if ($request->has('music_type')) {
                    $details['music_type'] = $request->music_type;
                }

                // Durée de la prestation
                if ($request->has('duration')) {
                    $details['duration'] = (float) $request->duration;
                }

                // Genres musicaux
                if ($request->has('music_genres')) {
                    $details['music_genres'] = $request->music_genres;
                }

                // Composition du groupe
                if ($request->has('band_composition')) {
                    $details['band_composition'] = $request->band_composition;
                }

                // Équipement fourni
                if ($request->has('music_equipment')) {
                    $details['music_equipment'] = $request->music_equipment;
                }

                // Répertoire / Spécialités
                if ($request->has('music_repertoire')) {
                    $details['music_repertoire'] = $request->music_repertoire;
                }
                break;

            // Transport
            case 'Transport':
                // Type de véhicule
                if ($request->has('vehicle_type')) {
                    $details['vehicle_type'] = $request->vehicle_type;
                }

                // Nombre de places
                if ($request->has('num_seats')) {
                    $details['num_seats'] = (int) $request->num_seats;
                }

                // Options incluses
                if ($request->has('transport_options')) {
                    $details['transport_options'] = $request->transport_options;
                }

                // Type de tarification
                if ($request->has('transport_pricing_model')) {
                    $details['transport_pricing_model'] = $request->transport_pricing_model;
                }

                // Détails du véhicule
                if ($request->has('vehicle_details')) {
                    $details['vehicle_details'] = $request->vehicle_details;
                }
                break;

            // Autres catégories
            default:
                // Options génériques
                if ($request->has('service_options')) {
                    $details['service_options'] = $request->service_options;
                }
                break;
        }

        return $details;
    }
}
