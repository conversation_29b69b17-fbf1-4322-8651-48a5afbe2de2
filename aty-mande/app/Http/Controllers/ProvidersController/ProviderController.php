<?php

namespace App\Http\Controllers\ProvidersController;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\ProvidersModel\Provider;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class ProviderController extends Controller
{
    /**
     * Affiche le formulaire de modification du profil prestataire
     *
     * @return \Illuminate\View\View
     */
    public function edit()
    {
        $provider = Auth::user()->provider;

        // Décoder les zones de service JSON en tableau PHP
        if ($provider->service_areas) {
            if (is_string($provider->service_areas)) {
                $provider->service_areas = json_decode($provider->service_areas, true);
            }
        } else {
            $provider->service_areas = [];
        }

        $allCategories = Category::orderBy('name')->get();
        $selectedCategories = $provider->categories->pluck('id')->toArray();
        $primaryCategory = $provider->categories()->wherePivot('is_primary', true)->first();

        return view('ProvidersView.profile.edit', compact('provider', 'allCategories', 'selectedCategories', 'primaryCategory'));
    }

    /**
     * Met à jour le profil du prestataire
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request)
    {
        $provider = Auth::user()->provider;

        // Validation des données
        $validator = Validator::make($request->all(), [
            'business_name' => 'required|string|max:255',
            'business_email' => 'required|email|max:255',
            'business_phone' => 'required|string|max:20',
            'business_address' => 'required|string|max:255',
            'business_registration_number' => 'nullable|string|max:50',
            'description' => 'nullable|string|max:1000',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'service_areas' => 'nullable|array',
            'categories' => 'required|array|min:1',
            'categories.*' => 'exists:categories,id',
            'primary_category' => 'required|exists:categories,id',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        // Mise à jour des informations de base
        $provider->business_name = $request->business_name;
        $provider->business_email = $request->business_email;
        $provider->business_phone = $request->business_phone;
        $provider->business_address = $request->business_address;
        $provider->business_registration_number = $request->business_registration_number;
        $provider->description = $request->description;

        // Traitement du logo si fourni
        if ($request->hasFile('logo')) {
            // Supprimer l'ancien logo s'il existe
            if ($provider->logo && Storage::exists('public/' . $provider->logo)) {
                Storage::delete('public/' . $provider->logo);
            }

            // Enregistrer le nouveau logo
            $logoPath = $request->file('logo')->store('providers/logos', 'public');
            $provider->logo = str_replace('public/', '', $logoPath);
        }

        // Mise à jour des zones de service
        if ($request->has('service_areas')) {
            $provider->service_areas = json_encode($request->service_areas);
        } else {
            $provider->service_areas = json_encode([]);
        }

        $provider->save();

        // Mise à jour des catégories
        $provider->categories()->detach(); // Supprimer toutes les catégories existantes

        foreach ($request->categories as $categoryId) {
            $isPrimary = ($categoryId == $request->primary_category);
            $provider->categories()->attach($categoryId, ['is_primary' => $isPrimary]);
        }

        return redirect()->route('provider.profile')->with('success', 'Votre profil a été mis à jour avec succès.');
    }
}
