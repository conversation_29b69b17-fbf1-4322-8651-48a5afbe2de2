<?php

namespace App\Http\Controllers\ProvidersController;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Validation\Rules;

class SettingsController extends Controller
{
    /**
     * Display the provider settings page.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $user = Auth::user();
        $provider = $user->provider;
        
        return view('ProvidersView.settings.index', compact('user', 'provider'));
    }

    /**
     * Update the provider's notification settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateNotifications(Request $request)
    {
        $provider = Auth::user()->provider;
        
        // Validate the request
        $validated = $request->validate([
            'email_notifications' => 'nullable|boolean',
            'sms_notifications' => 'nullable|boolean',
            'quote_request_notifications' => 'nullable|boolean',
            'booking_notifications' => 'nullable|boolean',
            'review_notifications' => 'nullable|boolean',
            'marketing_notifications' => 'nullable|boolean',
        ]);
        
        // Update notification settings
        $notificationSettings = $provider->notification_settings ?? [];
        
        $notificationSettings['email_notifications'] = $request->has('email_notifications');
        $notificationSettings['sms_notifications'] = $request->has('sms_notifications');
        $notificationSettings['quote_request_notifications'] = $request->has('quote_request_notifications');
        $notificationSettings['booking_notifications'] = $request->has('booking_notifications');
        $notificationSettings['review_notifications'] = $request->has('review_notifications');
        $notificationSettings['marketing_notifications'] = $request->has('marketing_notifications');
        
        $provider->notification_settings = $notificationSettings;
        $provider->save();
        
        return redirect()->route('provider.settings')
            ->with('success', 'Paramètres de notification mis à jour avec succès.');
    }

    /**
     * Update the provider's password.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updatePassword(Request $request)
    {
        $user = Auth::user();
        
        // Validate the request
        $validated = $request->validate([
            'current_password' => ['required', 'current_password'],
            'password' => ['required', 'confirmed', Rules\Password::defaults()],
        ]);
        
        // Update the password
        $user->password = Hash::make($validated['password']);
        $user->save();
        
        return redirect()->route('provider.settings')
            ->with('success', 'Mot de passe mis à jour avec succès.');
    }

    /**
     * Update the provider's account settings.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function updateAccount(Request $request)
    {
        $user = Auth::user();
        
        // Validate the request
        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users,email,' . $user->id],
            'phone' => ['nullable', 'string', 'max:20'],
            'language' => ['required', 'string', 'in:fr,en'],
        ]);
        
        // Update the user
        $user->name = $validated['name'];
        $user->email = $validated['email'];
        $user->phone = $validated['phone'];
        $user->language = $validated['language'];
        $user->save();
        
        return redirect()->route('provider.settings')
            ->with('success', 'Paramètres du compte mis à jour avec succès.');
    }

    /**
     * Deactivate the provider's account.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function deactivateAccount(Request $request)
    {
        $user = Auth::user();
        $provider = $user->provider;
        
        // Validate the request
        $validated = $request->validate([
            'deactivation_reason' => ['required', 'string', 'max:500'],
            'confirm_deactivation' => ['required', 'string', 'in:DEACTIVATE'],
        ]);
        
        // Deactivate the provider
        $provider->is_active = false;
        $provider->deactivation_reason = $validated['deactivation_reason'];
        $provider->deactivated_at = now();
        $provider->save();
        
        // Log the user out
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();
        
        return redirect()->route('home')
            ->with('info', 'Votre compte prestataire a été désactivé. Nous espérons vous revoir bientôt !');
    }
}
