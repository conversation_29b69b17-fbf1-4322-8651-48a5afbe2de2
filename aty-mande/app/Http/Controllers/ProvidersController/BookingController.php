<?php

namespace App\Http\Controllers\ProvidersController;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Message;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class BookingController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('provider');
    }

    /**
     * Display a listing of the provider's bookings.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $provider = Auth::user()->provider;

        // Build query
        $query = Booking::where('provider_id', $provider->id)
            ->with(['client', 'providerService', 'event']);

        // Apply filters
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        if ($request->has('date') && $request->date) {
            switch ($request->date) {
                case 'upcoming':
                    $query->where('event_date', '>=', now());
                    break;
                case 'past':
                    $query->where('event_date', '<', now());
                    break;
                case 'today':
                    $query->whereDate('event_date', today());
                    break;
                case 'week':
                    $query->whereBetween('event_date', [now()->startOfWeek(), now()->endOfWeek()]);
                    break;
                case 'month':
                    $query->whereMonth('event_date', now()->month)
                        ->whereYear('event_date', now()->year);
                    break;
                case 'year':
                    $query->whereYear('event_date', now()->year);
                    break;
            }
        }

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('event_location', 'like', "%{$search}%")
                  ->orWhere('booking_number', 'like', "%{$search}%")
                  ->orWhereHas('client', function($q) use ($search) {
                      $q->whereHas('user', function($q) use ($search) {
                          $q->where('name', 'like', "%{$search}%")
                            ->orWhere('email', 'like', "%{$search}%");
                      });
                  });
            });
        }

        // Order by
        $query->orderBy('event_date', $request->has('date') && $request->date === 'past' ? 'desc' : 'asc');

        // Paginate results
        $bookings = $query->paginate(10)->withQueryString();

        return view('ProvidersView.dashboard.bookings.index', compact('bookings'));
    }

    /**
     * Display the specified booking.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $provider = Auth::user()->provider;

        $booking = Booking::where('id', $id)
            ->where('provider_id', $provider->id)
            ->with(['client', 'client.user', 'providerService', 'event', 'quote', 'review'])
            ->firstOrFail();

        // Get messages for this booking
        $messages = Message::where('booking_id', $booking->id)
            ->orderBy('created_at')
            ->get();

        // Mark unread messages as read
        foreach ($messages as $message) {
            if (!$message->is_read && $message->recipient_id == Auth::id()) {
                $message->markAsRead();
            }
        }

        return view('ProvidersView.dashboard.bookings.show', compact('booking', 'messages'));
    }

    /**
     * Confirm the specified booking.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function confirm(Request $request, $id)
    {
        $provider = Auth::user()->provider;

        $booking = Booking::where('id', $id)
            ->where('provider_id', $provider->id)
            ->where('status', 'pending')
            ->firstOrFail();

        // Update booking status
        $booking->status = 'confirmed';
        $booking->confirmed_at = now();
        $booking->save();

        // Add a message about the confirmation
        $message = new Message();
        $message->sender_id = Auth::id();
        $message->recipient_id = $booking->client->user_id;
        $message->booking_id = $booking->id;
        $message->message = 'J\'ai confirmé votre réservation. Merci pour votre confiance!';
        $message->save();

        // Si la réservation est associée à un événement, mettre à jour la tâche correspondante
        if ($booking->event_id) {
            $event = $booking->event;

            if ($event) {
                // Rechercher une tâche liée à ce service
                $serviceId = $booking->provider_service_id;
                $task = $event->findTaskForService($serviceId);

                if ($task) {
                    // Mettre à jour la tâche
                    $task->status = 'in_progress';
                    $task->notes = ($task->notes ? $task->notes . "\n" : "") . "Réservation confirmée le " . now()->format('d/m/Y H:i');
                    $task->save();
                }
            }
        }

        return redirect()->route('provider.bookings.show', $id)
            ->with('status', 'Réservation confirmée avec succès.');
    }

    /**
     * Mark the specified booking as completed.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function complete(Request $request, $id)
    {
        $provider = Auth::user()->provider;

        $booking = Booking::where('id', $id)
            ->where('provider_id', $provider->id)
            ->where('status', 'confirmed')
            ->firstOrFail();

        // Update booking status
        $booking->status = 'completed';
        $booking->completed_at = now();
        $booking->save();

        // Add a message about the completion
        $message = new Message();
        $message->sender_id = Auth::id();
        $message->recipient_id = $booking->client->user_id;
        $message->booking_id = $booking->id;
        $message->message = 'J\'ai marqué cette réservation comme terminée. Merci de nous avoir fait confiance!';
        $message->save();

        // Si la réservation est associée à un événement, mettre à jour la tâche correspondante
        if ($booking->event_id) {
            $event = $booking->event;

            if ($event) {
                // Rechercher une tâche liée à ce service
                $serviceId = $booking->provider_service_id;
                $task = $event->findTaskForService($serviceId);

                if ($task) {
                    // Marquer la tâche comme terminée
                    $task->status = 'completed';
                    $task->actual_cost = $booking->total_amount;
                    $task->notes = ($task->notes ? $task->notes . "\n" : "") . "Service complété le " . now()->format('d/m/Y H:i');
                    $task->save();
                }
            }
        }

        return redirect()->route('provider.bookings.show', $id)
            ->with('status', 'Réservation marquée comme terminée avec succès.');
    }

    /**
     * Cancel the specified booking.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function cancel(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'cancellation_reason' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $provider = Auth::user()->provider;

        $booking = Booking::where('id', $id)
            ->where('provider_id', $provider->id)
            ->whereIn('status', ['pending', 'confirmed'])
            ->firstOrFail();

        // Update booking status
        $booking->status = 'cancelled';
        $booking->cancellation_reason = $request->input('cancellation_reason');
        $booking->cancelled_at = now();
        $booking->cancelled_by = 'provider';
        $booking->save();

        // Add a message about the cancellation
        $message = new Message();
        $message->sender_id = Auth::id();
        $message->recipient_id = $booking->client->user_id;
        $message->booking_id = $booking->id;
        $message->message = 'J\'ai dû annuler cette réservation. Raison: ' . $request->input('cancellation_reason');
        $message->save();

        return redirect()->route('provider.bookings.show', $id)
            ->with('status', 'Réservation annulée avec succès.');
    }

    /**
     * Send a message for the specified booking.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function sendMessage(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $provider = Auth::user()->provider;

        $booking = Booking::where('id', $id)
            ->where('provider_id', $provider->id)
            ->firstOrFail();

        // Create the message
        $message = new Message();
        $message->sender_id = Auth::id();
        $message->recipient_id = $booking->client->user_id;
        $message->booking_id = $booking->id;
        $message->message = $request->input('message');
        $message->save();

        return redirect()->route('provider.bookings.show', $id)
            ->with('status', 'Message envoyé avec succès.');
    }
}
