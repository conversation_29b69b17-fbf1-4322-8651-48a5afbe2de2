<?php

namespace App\Http\Controllers;

use App\Models\ClientsModel\Client;
use App\Models\ProvidersModel\Provider;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class AuthController extends Controller
{
    /**
     * Handle client registration.
     */
    public function registerClient(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            'phone' => ['required', 'string', 'max:20'],
            'client_type' => ['required', 'string', Rule::in(['individual', 'company', 'organization'])],
            'company_name' => ['nullable', 'required_if:client_type,company,organization', 'string', 'max:255'],
            'company_position' => ['nullable', 'string', 'max:255'],
            'terms_accepted' => ['required', 'accepted'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Create the user
        $user = User::create([
            'name' => $request->input('name'),
            'email' => $request->input('email'),
            'password' => Hash::make($request->input('password')),
            'phone' => $request->input('phone'),
            'role' => 'client',
        ]);

        // Create the client
        $client = Client::create([
            'user_id' => $user->id,
            'client_type' => $request->input('client_type'),
            'company_name' => $request->input('company_name'),
            'company_position' => $request->input('company_position'),
        ]);

        // Log the user in
        Auth::login($user);

        return response()->json([
            'success' => true,
            'redirect' => route('client.dashboard')
        ]);
    }

    /**
     * Handle provider registration.
     */
    public function registerProvider(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => ['required', 'string', 'max:255'],
            'email' => ['required', 'string', 'email', 'max:255', 'unique:users'],
            'password' => ['required', 'string', 'min:8', 'confirmed'],
            'phone' => ['required', 'string', 'max:20'],
            'business_name' => ['required', 'string', 'max:255'],
            'business_description' => ['required', 'string'],
            'business_address' => ['required', 'string', 'max:255'],
            'business_city' => ['required', 'string', 'max:255'],
            'business_country' => ['required', 'string', 'max:255'],
            'business_region' => ['nullable', 'string', 'max:255'],
            'categories' => ['required', 'array', 'min:1'],
            'event_types' => ['required', 'array', 'min:1'],
            'services' => ['nullable', 'array'],
            'terms_accepted' => ['required', 'accepted'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        // Create the user
        $user = User::create([
            'name' => $request->input('name'),
            'email' => $request->input('email'),
            'password' => Hash::make($request->input('password')),
            'phone' => $request->input('phone'),
            'role' => 'provider',
        ]);

        // Create the provider
        $provider = Provider::create([
            'user_id' => $user->id,
            'business_name' => $request->input('business_name'),
            'business_email' => $request->input('email'),
            'business_phone' => $request->input('phone'),
            'business_address' => $request->input('business_address'),
            'description' => $request->input('business_description'),
            'service_areas' => json_encode([
                'region' => $request->input('business_region'),
                'event_types' => $request->input('event_types', [])
            ]),
            'is_verified' => false,
            'is_featured' => false,
            'is_active' => true,
        ]);

        // Attach categories
        if ($request->has('categories')) {
            foreach ($request->input('categories') as $index => $categoryId) {
                $isPrimary = ($index === 0); // First category is primary
                $provider->categories()->attach($categoryId, ['is_primary' => $isPrimary]);
            }
        }

        // Log the user in
        Auth::login($user);

        return response()->json([
            'success' => true,
            'redirect' => route('provider.dashboard')
        ]);
    }

    /**
     * Handle user login.
     */
    public function login(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => ['required', 'string', 'email'],
            'password' => ['required', 'string'],
            'remember' => ['nullable', 'boolean'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'errors' => $validator->errors()
            ], 422);
        }

        $credentials = $request->only('email', 'password');
        $remember = $request->filled('remember');

        if (Auth::attempt($credentials, $remember)) {
            $request->session()->regenerate();

            $user = Auth::user();
            $redirectUrl = '/';

            if ($user->isClient()) {
                $redirectUrl = route('client.dashboard');
            } elseif ($user->isProvider()) {
                $redirectUrl = route('provider.dashboard');
            } elseif ($user->isAdmin()) {
                $redirectUrl = route('admin.dashboard');
            }

            return response()->json([
                'success' => true,
                'redirect' => $redirectUrl
            ]);
        }

        return response()->json([
            'success' => false,
            'errors' => ['email' => ['These credentials do not match our records.']]
        ], 422);
    }
}
