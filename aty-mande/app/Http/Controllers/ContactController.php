<?php

namespace App\Http\Controllers;

use App\Mail\ContactConfirmation;
use App\Models\ContactMessage;
use App\Models\Setting;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Mail;

class ContactController extends Controller
{
    /**
     * Affiche la page de contact
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $contactEmail = Setting::get('contact_email', '<EMAIL>');
        $contactPhone = Setting::get('contact_phone', '');
        $contactAddress = Setting::get('contact_address', '');

        return view('ClientsView.contact', compact('contactEmail', 'contactPhone', 'contactAddress'));
    }

    /**
     * Traite le formulaire de contact
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        // Validation des données
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        // Enregistrement du message dans la base de données
        $contactMessage = ContactMessage::create($validated);

        // Envoi de l'email de confirmation
        Mail::to($request->email)->send(new ContactConfirmation($contactMessage));

        // Redirection avec message de succès
        return redirect()->route('contact')
            ->with('success', 'Votre message a été envoyé avec succès. Nous vous contacterons bientôt.');
    }
}
