<?php

namespace App\Http\Controllers\ClientsController;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\Message;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class BookingController extends Controller
{
    /**
     * Display a listing of the client's bookings.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $client = Auth::user()->client;

        // Build query
        $query = Booking::where('client_id', $client->id)
            ->with(['provider', 'providerService', 'event']);

        // Apply filters
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        if ($request->has('date') && $request->date) {
            switch ($request->date) {
                case 'upcoming':
                    $query->where('event_date', '>=', now());
                    break;
                case 'past':
                    $query->where('event_date', '<', now());
                    break;
                case 'today':
                    $query->whereDate('event_date', today());
                    break;
                case 'week':
                    $query->whereBetween('event_date', [now()->startOfWeek(), now()->endOfWeek()]);
                    break;
                case 'month':
                    $query->whereMonth('event_date', now()->month)
                        ->whereYear('event_date', now()->year);
                    break;
                case 'year':
                    $query->whereYear('event_date', now()->year);
                    break;
            }
        }

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('event_location', 'like', "%{$search}%")
                  ->orWhere('booking_number', 'like', "%{$search}%")
                  ->orWhereHas('provider', function($q) use ($search) {
                      $q->where('business_name', 'like', "%{$search}%");
                  });
            });
        }

        // Order by
        $query->orderBy('event_date', $request->has('date') && $request->date === 'past' ? 'desc' : 'asc');

        // Paginate results
        $bookings = $query->paginate(10)->withQueryString();

        return view('ClientsView.dashboard.bookings.index', compact('bookings'));
    }

    /**
     * Display the specified booking.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $client = Auth::user()->client;

        $booking = Booking::where('id', $id)
            ->where('client_id', $client->id)
            ->with(['provider', 'providerService', 'event', 'quote', 'review'])
            ->firstOrFail();

        return view('ClientsView.dashboard.bookings.show', compact('booking'));
    }

    /**
     * Cancel the specified booking.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function cancel(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'cancellation_reason' => 'required|string|max:500',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $client = Auth::user()->client;

        $booking = Booking::where('id', $id)
            ->where('client_id', $client->id)
            ->whereIn('status', ['pending', 'confirmed'])
            ->firstOrFail();

        // Cancel the booking
        $booking->status = 'cancelled';
        $booking->cancellation_reason = $request->input('cancellation_reason');
        $booking->cancelled_at = now();
        $booking->save();

        // Add a message about the cancellation
        $message = new Message();
        $message->sender_id = Auth::id();
        $message->recipient_id = $booking->provider->user_id;
        $message->booking_id = $booking->id;
        $message->message = 'J\'ai annulé cette réservation. Raison: ' . $request->input('cancellation_reason');
        $message->save();

        return redirect()->route('client.bookings.show', $id)
            ->with('status', 'Réservation annulée avec succès.');
    }

    /**
     * Send a message for the specified booking.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function sendMessage(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'message' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $client = Auth::user()->client;

        $booking = Booking::where('id', $id)
            ->where('client_id', $client->id)
            ->firstOrFail();

        // Create the message
        $message = new Message();
        $message->sender_id = Auth::id();
        $message->recipient_id = $booking->provider->user_id;
        $message->booking_id = $booking->id;
        $message->message = $request->input('message');
        $message->save();

        return redirect()->route('client.bookings.show', $id)
            ->with('status', 'Message envoyé avec succès.');
    }

    /**
     * Submit a review for the specified booking.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function submitReview(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'rating' => 'required|integer|min:1|max:5',
            'review_text' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $client = Auth::user()->client;

        $booking = Booking::where('id', $id)
            ->where('client_id', $client->id)
            ->where('status', 'completed')
            ->firstOrFail();

        // Check if a review already exists
        if ($booking->review) {
            return redirect()->back()
                ->with('error', 'Vous avez déjà laissé un avis pour cette réservation.');
        }

        // Create the review
        $review = new \App\Models\Review();
        $review->client_id = $client->id;
        $review->provider_id = $booking->provider_id;
        $review->booking_id = $booking->id;
        $review->rating = $request->input('rating');
        $review->review_text = $request->input('review_text');
        $review->is_approved = true; // Auto-approve for now
        $review->save();

        return redirect()->route('client.bookings.show', $id)
            ->with('status', 'Avis soumis avec succès.');
    }
}