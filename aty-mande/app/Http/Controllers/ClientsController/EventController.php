<?php

namespace App\Http\Controllers\ClientsController;

use App\Http\Controllers\Controller;
use App\Models\ClientsModel\Event;
use App\Models\ClientsModel\EventType;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class EventController extends Controller
{
    /**
     * Display a listing of the client's events.
     *
     * @return \Illuminate\View\View
     */
    public function index(Request $request)
    {
        $client = Auth::user()->client;

        // Build query
        $query = Event::where('client_id', $client->id)
            ->with('eventType');

        // Apply filters
        if ($request->has('status') && $request->status) {
            $query->where('status', $request->status);
        }

        if ($request->has('date') && $request->date) {
            switch ($request->date) {
                case 'upcoming':
                    $query->where('date', '>=', now());
                    break;
                case 'past':
                    $query->where('date', '<', now());
                    break;
                case 'today':
                    $query->whereDate('date', today());
                    break;
                case 'week':
                    $query->whereBetween('date', [now()->startOfWeek(), now()->endOfWeek()]);
                    break;
                case 'month':
                    $query->whereMonth('date', now()->month)
                        ->whereYear('date', now()->year);
                    break;
                case 'year':
                    $query->whereYear('date', now()->year);
                    break;
            }
        }

        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%")
                  ->orWhere('location', 'like', "%{$search}%");
            });
        }

        // Order by
        $query->orderBy('date', $request->has('date') && $request->date === 'past' ? 'desc' : 'asc');

        // Paginate results
        $events = $query->paginate(10)->withQueryString();

        // Get event types for filter
        $eventTypes = EventType::all();

        return view('ClientsView.dashboard.events.index', compact('events', 'eventTypes'));
    }

    /**
     * Show the form for creating a new event.
     *
     * @return \Illuminate\View\View
     */
    public function create()
    {
        $eventTypes = EventType::all();
        return view('ClientsView.dashboard.events.create', compact('eventTypes'));
    }

    /**
     * Store a newly created event in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'event_type_id' => 'required|exists:event_types,id',
            'date' => 'required|date|after_or_equal:today',
            'start_time' => 'nullable|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i|after:start_time',
            'location' => 'required|string|max:255',
            'address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'guest_count' => 'nullable|integer|min:1',
            'budget' => 'nullable|numeric|min:0',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $client = Auth::user()->client;

        $event = new Event();
        $event->client_id = $client->id;
        $event->event_type_id = $request->input('event_type_id');
        $event->title = $request->input('title');
        $event->description = $request->input('description');
        $event->date = $request->input('date');
        $event->start_time = $request->input('start_time') ? date('Y-m-d') . ' ' . $request->input('start_time') : null;
        $event->end_time = $request->input('end_time') ? date('Y-m-d') . ' ' . $request->input('end_time') : null;
        $event->location = $request->input('location');
        $event->address = $request->input('address');
        $event->city = $request->input('city');
        $event->state = $request->input('state');
        $event->postal_code = $request->input('postal_code');
        $event->country = $request->input('country');
        $event->guest_count = $request->input('guest_count');
        $event->budget = $request->input('budget');
        $event->status = 'planning';
        $event->save();

        // Rediriger vers la page de suggestions de services
        return redirect()->route('client.events.service-suggestions', $event->id)
            ->with('status', 'Événement créé avec succès. Voici quelques suggestions de services pour votre événement.');
    }

    /**
     * Display the specified event.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $client = Auth::user()->client;

        $event = Event::where('id', $id)
            ->where('client_id', $client->id)
            ->with(['eventType', 'quoteRequests.provider', 'quoteRequests.providerService', 'bookings.provider', 'bookings.providerService'])
            ->firstOrFail();

        return view('ClientsView.dashboard.events.show', compact('event'));
    }

    /**
     * Show the form for editing the specified event.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function edit($id)
    {
        $client = Auth::user()->client;

        $event = Event::where('id', $id)
            ->where('client_id', $client->id)
            ->firstOrFail();

        $eventTypes = EventType::all();

        return view('ClientsView.dashboard.events.edit', compact('event', 'eventTypes'));
    }

    /**
     * Update the specified event in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'title' => 'required|string|max:255',
            'event_type_id' => 'required|exists:event_types,id',
            'date' => 'required|date',
            'start_time' => 'nullable|date_format:H:i',
            'end_time' => 'nullable|date_format:H:i|after:start_time',
            'location' => 'required|string|max:255',
            'address' => 'nullable|string|max:255',
            'city' => 'nullable|string|max:100',
            'state' => 'nullable|string|max:100',
            'postal_code' => 'nullable|string|max:20',
            'country' => 'nullable|string|max:100',
            'guest_count' => 'nullable|integer|min:1',
            'budget' => 'nullable|numeric|min:0',
            'description' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return redirect()->back()
                ->withErrors($validator)
                ->withInput();
        }

        $client = Auth::user()->client;

        $event = Event::where('id', $id)
            ->where('client_id', $client->id)
            ->firstOrFail();

        $event->event_type_id = $request->input('event_type_id');
        $event->title = $request->input('title');
        $event->description = $request->input('description');
        $event->date = $request->input('date');
        $event->start_time = $request->input('start_time') ? date('Y-m-d') . ' ' . $request->input('start_time') : null;
        $event->end_time = $request->input('end_time') ? date('Y-m-d') . ' ' . $request->input('end_time') : null;
        $event->location = $request->input('location');
        $event->address = $request->input('address');
        $event->city = $request->input('city');
        $event->state = $request->input('state');
        $event->postal_code = $request->input('postal_code');
        $event->country = $request->input('country');
        $event->guest_count = $request->input('guest_count');
        $event->budget = $request->input('budget');
        $event->save();

        return redirect()->route('client.events.show', $id)
            ->with('status', 'Événement mis à jour avec succès.');
    }

    /**
     * Display service suggestions for the event.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function serviceSuggestions($id)
    {
        $client = Auth::user()->client;

        $event = Event::where('id', $id)
            ->where('client_id', $client->id)
            ->with('eventType')
            ->firstOrFail();

        // Get service categories suggested for this event type
        $suggestedCategories = $event->eventType->categories()
            ->orderBy('priority', 'desc')
            ->get();

        // Get providers for each category
        $categoryProviders = [];
        foreach ($suggestedCategories as $category) {
            $providers = \App\Models\ProvidersModel\Provider::whereHas('categories', function($query) use ($category) {
                $query->where('categories.id', $category->id);
            })
            ->with(['services' => function($query) use ($category) {
                $query->where('category_id', $category->id)
                    ->where('is_active', true)
                    ->orderBy('is_featured', 'desc')
                    ->limit(5);
            }])
            ->where('is_active', true)
            ->limit(5)
            ->get();

            $categoryProviders[$category->id] = $providers;
        }

        return view('ClientsView.dashboard.events.service-suggestions', compact('event', 'suggestedCategories', 'categoryProviders'));
    }

    /**
     * Add suggested services as tasks to the event.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function addServiceTasks(Request $request, $id)
    {
        $client = Auth::user()->client;

        $event = Event::where('id', $id)
            ->where('client_id', $client->id)
            ->firstOrFail();

        $selectedServices = $request->input('selected_services', []);

        foreach ($selectedServices as $serviceId) {
            $service = \App\Models\ProvidersModel\ProviderService::find($serviceId);

            if ($service) {
                $category = $service->category;
                $provider = $service->provider;

                // Map service category to task category
                $taskCategory = 'other';
                switch ($category->name) {
                    case 'Traiteur':
                        $taskCategory = 'food';
                        break;
                    case 'Photographie':
                        $taskCategory = 'photography';
                        break;
                    case 'Vidéographie':
                        $taskCategory = 'photography';
                        break;
                    case 'Musique & Animation':
                        $taskCategory = 'music';
                        break;
                    case 'Espaces de Réception':
                        $taskCategory = 'venue';
                        break;
                    case 'Décoration':
                        $taskCategory = 'decoration';
                        break;
                    case 'Transport':
                        $taskCategory = 'transportation';
                        break;
                }

                // Create a task for this service
                $task = new \App\Models\ClientsModel\EventTask();
                $task->event_id = $event->id;
                $task->title = "Réserver: " . $service->title;
                $task->description = "Prestataire: " . $provider->business_name . "\n" .
                                    "Service: " . $service->title . "\n" .
                                    "Description: " . $service->description;
                $task->category = $taskCategory;
                $task->status = 'pending';
                $task->due_date = $event->date;
                $task->budget = $service->getRealPrice();
                $task->assigned_to = $client->user->name;
                $task->save();
            }
        }

        return redirect()->route('client.events.show', $id)
            ->with('status', 'Les tâches ont été ajoutées à votre événement avec succès.');
    }

    /**
     * Remove the specified event from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $client = Auth::user()->client;

        $event = Event::where('id', $id)
            ->where('client_id', $client->id)
            ->firstOrFail();

        // Check if the event has any bookings
        if ($event->bookings()->count() > 0) {
            return redirect()->back()
                ->with('error', 'Impossible de supprimer cet événement car il a des réservations associées.');
        }

        $event->delete();

        return redirect()->route('client.events')
            ->with('status', 'Événement supprimé avec succès.');
    }
}