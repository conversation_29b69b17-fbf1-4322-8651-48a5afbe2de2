<?php

namespace App\Http\Controllers\ClientsController;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class InvoiceController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('client');
    }

    /**
     * Display a listing of the client's invoices.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $client = auth()->user()->client;

        $invoices = \App\Models\Invoice::where('client_id', $client->id)
            ->with(['booking', 'provider'])
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        return view('ClientsView.dashboard.invoices.index', compact('invoices'));
    }

    /**
     * Display the specified invoice.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $client = auth()->user()->client;

        $invoice = \App\Models\Invoice::where('id', $id)
            ->where('client_id', $client->id)
            ->with(['booking', 'provider', 'payments'])
            ->firstOrFail();

        return view('ClientsView.dashboard.invoices.show', compact('invoice'));
    }

    /**
     * Download the specified invoice as PDF.
     *
     * @param  int  $id
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function download($id)
    {
        $client = auth()->user()->client;

        $invoice = \App\Models\Invoice::where('id', $id)
            ->where('client_id', $client->id)
            ->with(['booking', 'provider', 'payments'])
            ->firstOrFail();

        // In a real application, you would generate a PDF here
        // For this example, we'll just return a view
        return view('ClientsView.dashboard.invoices.pdf', compact('invoice'));
    }
}
