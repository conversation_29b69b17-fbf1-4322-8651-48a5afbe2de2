<?php

namespace App\Http\Controllers\ClientsController;

use App\Http\Controllers\Controller;
use App\Mail\ContactConfirmation;
use App\Models\Category;
use App\Models\ClientsModel\EventType;
use App\Models\ContactMessage;
use App\Models\ProvidersModel\Provider;
use App\Models\Review;
use App\Models\Setting;
use Illuminate\Support\Facades\Mail;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * Display the homepage.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        // Get featured categories for the homepage
        $featuredCategories = Category::where('is_active', true)
            ->orderBy('order')
            ->take(6)
            ->get();

        // Get all categories for the registration form
        $allCategories = Category::where('is_active', true)
            ->orderBy('order')
            ->get();

        // Get all providers without verification filters
        $featuredProviders = Provider::with(['services' => function($query) {
                $query->where('is_active', true)->take(1);
            }, 'categories', 'gallery'])
            ->take(8)
            ->inRandomOrder()
            ->get();

        // Get event types
        $eventTypes = EventType::where('is_active', true)
            ->get();

        // Get featured reviews
        $featuredReviews = Review::getFeatured(3);

        // Get site settings
        $heroTitle = Setting::get('home_hero_title', 'Find the Perfect Service Providers for Your Event');
        $heroSubtitle = Setting::get('home_hero_subtitle', 'Connect with trusted professionals for weddings, parties, corporate events, and more.');

        return view('ClientsView.home', compact(
            'featuredCategories',
            'allCategories',
            'featuredProviders',
            'eventTypes',
            'featuredReviews',
            'heroTitle',
            'heroSubtitle'
        ));
    }

    /**
     * Display the about us page.
     *
     * @return \Illuminate\View\View
     */
    public function about()
    {
        $aboutContent = Setting::get('about_content', '');
        $aboutImage = Setting::get('about_image', '');

        return view('ClientsView.about', compact('aboutContent', 'aboutImage'));
    }

    /**
     * Display the contact page.
     *
     * @return \Illuminate\View\View
     */
    public function contact()
    {
        $contactEmail = Setting::get('contact_email', '<EMAIL>');
        $contactPhone = Setting::get('contact_phone', '');
        $contactAddress = Setting::get('contact_address', '');

        return view('ClientsView.contact', compact('contactEmail', 'contactPhone', 'contactAddress'));
    }

    /**
     * Process the contact form submission.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function submitContact(Request $request)
    {
        // Validation des données
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|max:255',
            'phone' => 'nullable|string|max:20',
            'subject' => 'required|string|max:255',
            'message' => 'required|string',
        ]);

        // Enregistrement du message dans la base de données
        $contactMessage = ContactMessage::create($validated);

        // Envoi de l'email de confirmation
        Mail::to($request->email)->send(new ContactConfirmation($contactMessage));

        // Redirection avec message de succès
        return redirect()->route('contact')
            ->with('success', 'Votre message a été envoyé avec succès. Nous vous contacterons bientôt.');
    }

    /**
     * Display the how it works page.
     *
     * @return \Illuminate\View\View
     */
    public function howItWorks()
    {
        $clientSteps = Setting::get('how_it_works_client', []);
        $providerSteps = Setting::get('how_it_works_provider', []);

        return view('ClientsView.how-it-works', compact('clientSteps', 'providerSteps'));
    }

    /**
     * Display the FAQ page.
     *
     * @return \Illuminate\View\View
     */
    public function faq()
    {
        $faqCategoriesJson = Setting::get('faq_categories', '[]');
        $faqsJson = Setting::get('faqs', '[]');

        // Décoder les données JSON
        $faqCategories = is_string($faqCategoriesJson) ? json_decode($faqCategoriesJson, true) : [];
        $faqs = is_string($faqsJson) ? json_decode($faqsJson, true) : [];

        // S'assurer que les variables sont des tableaux
        if (!is_array($faqCategories)) $faqCategories = [];
        if (!is_array($faqs)) $faqs = [];

        return view('ClientsView.faq', compact('faqCategories', 'faqs'));
    }

    /**
     * Display the terms and conditions page.
     *
     * @return \Illuminate\View\View
     */
    public function terms()
    {
        $termsContent = Setting::get('terms_content', '');

        return view('ClientsView.terms', compact('termsContent'));
    }

    /**
     * Display the privacy policy page.
     *
     * @return \Illuminate\View\View
     */
    public function privacy()
    {
        $privacyContent = Setting::get('privacy_content', '');

        return view('ClientsView.privacy', compact('privacyContent'));
    }
}
