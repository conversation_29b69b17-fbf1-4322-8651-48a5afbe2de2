<?php

namespace App\Http\Controllers\ClientsController;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\ClientsModel\EventType;
use App\Models\Location;
use App\Models\ProvidersModel\Provider;
use App\Models\ProvidersModel\ProviderService;
use App\Models\Service;
use Illuminate\Http\Request;

class SearchController extends Controller
{
    /**
     * Display the search form.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $categories = Category::where('is_active', true)->orderBy('name')->get();
        $eventTypes = EventType::where('is_active', true)->orderBy('name')->get();
        $locations = Location::where('is_active', true)->where('type', 'city')->orderBy('name')->get();

        return view('ClientsView.search.index', compact('categories', 'eventTypes', 'locations'));
    }

    /**
     * Search for providers based on the given criteria.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\View\View
     */
    public function search(Request $request)
    {
        // Get search parameters
        $keyword = $request->input('keyword');
        $categoryId = $request->input('category_id');
        $eventTypeId = $request->input('event_type_id');
        $locationId = $request->input('location_id');
        $date = $request->input('date');

        // Get budget parameters
        $budgetRange = $request->input('budget_range');
        $minBudget = $request->input('min_budget');
        $maxBudget = $request->input('max_budget');
        $negotiable = $request->input('negotiable');

        // Get additional search parameters
        $minRating = $request->input('min_rating');
        $availabilityDate = $request->input('availability_date');
        $services = $request->input('services', []);

        // Start with a base query without verification filters
        $query = Provider::query();

        // Apply keyword search if provided
        if ($keyword) {
            $query->where(function($q) use ($keyword) {
                $q->where('business_name', 'like', "%{$keyword}%")
                  ->orWhere('description', 'like', "%{$keyword}%")
                  ->orWhereHas('user', function($userQuery) use ($keyword) {
                      $userQuery->where('name', 'like', "%{$keyword}%");
                  })
                  ->orWhereHas('services', function($serviceQuery) use ($keyword) {
                      $serviceQuery->where('title', 'like', "%{$keyword}%")
                                  ->orWhere('description', 'like', "%{$keyword}%");
                  });
            });
        }

        // Filter by category if provided
        if ($categoryId) {
            $query->whereHas('services', function($q) use ($categoryId) {
                $q->where('category_id', $categoryId);
            });
        }

        // Filter by location if provided
        if ($locationId) {
            $location = Location::find($locationId);
            if ($location) {
                // Check if the location is in the provider's service areas
                $query->where(function($q) use ($location) {
                    $q->whereJsonContains('service_areas', $location->id)
                      ->orWhereJsonContains('service_areas', (string)$location->id);
                });
            }
        }

        // Filter by availability if date is provided
        if ($date) {
            $query->whereDoesntHave('availability', function($q) use ($date) {
                $q->where('date', $date)
                  ->where('status', 'unavailable');
            });
        }

        // Filter by budget range if provided
        if ($budgetRange) {
            // Parse the budget range
            if ($budgetRange === '5000000+') {
                // For "Plus de 5 000 000 Ar"
                $query->whereHas('services', function($q) {
                    $q->whereRaw("JSON_EXTRACT(options, '$.base_price') >= ?", [5000000]);
                });
            } else {
                // For ranges like "0-50000", "50000-100000", etc.
                list($min, $max) = explode('-', $budgetRange);
                $query->whereHas('services', function($q) use ($min, $max) {
                    $q->whereRaw("JSON_EXTRACT(options, '$.base_price') >= ?", [$min])
                      ->whereRaw("JSON_EXTRACT(options, '$.base_price') <= ?", [$max]);
                });
            }
        }

        // Filter by custom budget range if provided
        if ($minBudget || $maxBudget) {
            $query->whereHas('services', function($q) use ($minBudget, $maxBudget) {
                if ($minBudget) {
                    $q->whereRaw("JSON_EXTRACT(options, '$.base_price') >= ?", [$minBudget]);
                }
                if ($maxBudget) {
                    $q->whereRaw("JSON_EXTRACT(options, '$.base_price') <= ?", [$maxBudget]);
                }
            });
        }

        // Filter by negotiable price if requested
        if ($negotiable) {
            $query->whereHas('services', function($q) {
                $q->where('is_negotiable', true);
            });
        }

        // Filter by minimum rating if provided
        if ($minRating) {
            $query->whereHas('reviews', function($q) use ($minRating) {
                $q->selectRaw('AVG(rating) as avg_rating')
                  ->havingRaw('AVG(rating) >= ?', [$minRating]);
            });
        }

        // Filter by availability date if provided
        if ($availabilityDate) {
            $query->whereDoesntHave('availability', function($q) use ($availabilityDate) {
                $q->where('date', $availabilityDate)
                  ->where('status', 'unavailable');
            });
        }

        // Filter by specific services if provided
        if (!empty($services)) {
            $query->whereHas('services', function($q) use ($services) {
                $q->whereIn('services.id', $services);
            });
        }

        // Appliquer le tri
        $sort = $request->input('sort', 'relevance');

        switch ($sort) {
            case 'price_asc':
                $query->whereHas('services', function($q) {
                    $q->whereNotNull('options->base_price');
                })
                ->orderByRaw('(SELECT MIN(CAST(JSON_EXTRACT(options, "$.base_price") AS DECIMAL(10,2))) FROM provider_services WHERE provider_services.provider_id = providers.id) ASC');
                break;

            case 'price_desc':
                $query->whereHas('services', function($q) {
                    $q->whereNotNull('options->base_price');
                })
                ->orderByRaw('(SELECT MAX(CAST(JSON_EXTRACT(options, "$.base_price") AS DECIMAL(10,2))) FROM provider_services WHERE provider_services.provider_id = providers.id) DESC');
                break;

            case 'rating':
                $query->leftJoin('reviews', 'providers.id', '=', 'reviews.provider_id')
                    ->select('providers.*', \DB::raw('AVG(reviews.rating) as avg_rating'))
                    ->groupBy('providers.id')
                    ->orderBy('avg_rating', 'desc');
                break;

            default:
                // Par défaut, tri par pertinence (pas de tri spécifique)
                break;
        }

        // Get the results with pagination
        $providers = $query->with(['categories', 'services', 'gallery' => function($q) {
            $q->where('is_featured', true)->orderBy('order')->limit(1);
        }, 'reviews'])
        ->paginate(10);

        // Calculate average rating for each provider
        foreach ($providers as $provider) {
            $provider->average_rating = $provider->reviews->avg('rating') ?? 0;
            $provider->reviews_count = $provider->reviews->count();
        }

        // Get categories and locations for the filter sidebar
        $categories = Category::where('is_active', true)->orderBy('name')->get();
        $eventTypes = EventType::where('is_active', true)->orderBy('name')->get();
        $locations = Location::where('is_active', true)->where('type', 'city')->orderBy('name')->get();

        return view('ClientsView.search.results', compact(
            'providers',
            'categories',
            'eventTypes',
            'locations',
            'keyword',
            'categoryId',
            'eventTypeId',
            'locationId',
            'date',
            'budgetRange',
            'minBudget',
            'maxBudget',
            'negotiable',
            'minRating',
            'availabilityDate',
            'services',
            'sort'
        ));
    }

    /**
     * Search for providers by category.
     *
     * @param  string  $slug
     * @return \Illuminate\View\View
     */
    public function category($slug, Request $request)
    {
        $category = Category::where('slug', $slug)->where('is_active', true)->firstOrFail();

        // Start with a base query
        $query = Provider::where(function($q) use ($category) {
            // Prestataires avec des services dans cette catégorie
            $q->whereHas('services', function($sq) use ($category) {
                $sq->where('category_id', $category->id);
            })
            // OU prestataires associés à cette catégorie
            ->orWhereHas('categories', function($sq) use ($category) {
                $sq->where('categories.id', $category->id);
            });
        });

        // Get filter parameters
        $locationId = $request->input('location_id');
        $minRating = $request->input('min_rating');
        $minBudget = $request->input('min_budget');
        $maxBudget = $request->input('max_budget');
        $sort = $request->input('sort', 'relevance');

        // Filter by location if provided
        if ($locationId) {
            $location = Location::find($locationId);
            if ($location) {
                // Check if the location is in the provider's service areas
                $query->where(function($q) use ($location) {
                    $q->whereJsonContains('service_areas', $location->id)
                      ->orWhereJsonContains('service_areas', (string)$location->id);
                });
            }
        }

        // Filter by minimum rating if provided
        if ($minRating) {
            $query->whereHas('reviews', function($q) use ($minRating) {
                $q->selectRaw('AVG(rating) as avg_rating')
                  ->havingRaw('AVG(rating) >= ?', [$minRating]);
            });
        }

        // Filter by budget range if provided
        if ($minBudget || $maxBudget) {
            $query->whereHas('services', function($q) use ($minBudget, $maxBudget) {
                if ($minBudget) {
                    $q->where('price', '>=', $minBudget);
                }
                if ($maxBudget) {
                    $q->where('price', '<=', $maxBudget);
                }
            });
        }

        // Apply sorting
        switch ($sort) {
            case 'price_asc':
                $query->whereHas('services')
                    ->orderByRaw('(SELECT MIN(price) FROM provider_services WHERE provider_services.provider_id = providers.id) ASC');
                break;

            case 'price_desc':
                $query->whereHas('services')
                    ->orderByRaw('(SELECT MAX(price) FROM provider_services WHERE provider_services.provider_id = providers.id) DESC');
                break;

            case 'rating':
                $query->leftJoin('reviews', 'providers.id', '=', 'reviews.provider_id')
                    ->select('providers.*', \DB::raw('AVG(reviews.rating) as avg_rating'))
                    ->groupBy('providers.id')
                    ->orderBy('avg_rating', 'desc');
                break;

            default:
                // Par défaut, tri par pertinence (pas de tri spécifique)
                break;
        }

        // Get the results with pagination
        $providers = $query->with(['categories', 'services', 'gallery' => function($q) {
            $q->where('is_featured', true)->orderBy('order')->limit(1);
        }, 'reviews'])
        ->paginate(10)
        ->appends($request->except('page'));

        // Calculate average rating for each provider
        foreach ($providers as $provider) {
            $provider->average_rating = $provider->reviews->avg('rating') ?? 0;
            $provider->reviews_count = $provider->reviews->count();
        }

        // Get categories and locations for the filter sidebar
        $categories = Category::where('is_active', true)->orderBy('name')->get();
        $eventTypes = EventType::where('is_active', true)->orderBy('name')->get();
        $locations = Location::where('is_active', true)->where('type', 'city')->orderBy('name')->get();

        return view('ClientsView.search.category', compact(
            'category',
            'providers',
            'categories',
            'eventTypes',
            'locations'
        ));
    }

    /**
     * Search for providers by location.
     *
     * @param  string  $slug
     * @return \Illuminate\View\View
     */
    public function location($slug)
    {
        $location = Location::where('slug', $slug)->where('is_active', true)->firstOrFail();

        $providers = Provider::where(function($q) use ($location) {
            $q->whereJsonContains('service_areas', $location->id)
              ->orWhereJsonContains('service_areas', (string)$location->id);
        })
        ->with(['categories', 'services', 'gallery' => function($q) {
            $q->where('is_featured', true)->orderBy('order')->limit(1);
        }, 'reviews'])
        ->paginate(10);

        // Calculate average rating for each provider
        foreach ($providers as $provider) {
            $provider->average_rating = $provider->reviews->avg('rating') ?? 0;
            $provider->reviews_count = $provider->reviews->count();
        }

        // Get categories and locations for the filter sidebar
        $categories = Category::where('is_active', true)->orderBy('name')->get();
        $eventTypes = EventType::where('is_active', true)->orderBy('name')->get();
        $locations = Location::where('is_active', true)->where('type', 'city')->orderBy('name')->get();

        return view('ClientsView.search.location', compact(
            'location',
            'providers',
            'categories',
            'eventTypes',
            'locations'
        ));
    }

    /**
     * Search for providers by event type.
     *
     * @param  string  $slug
     * @return \Illuminate\View\View
     */
    public function eventType($slug)
    {
        $eventType = EventType::where('slug', $slug)->where('is_active', true)->firstOrFail();

        $providers = Provider::whereHas('eventTypes', function($q) use ($eventType) {
            $q->where('event_types.id', $eventType->id);
        })
        ->with(['categories', 'services', 'gallery' => function($q) {
            $q->where('is_featured', true)->orderBy('order')->limit(1);
        }, 'reviews'])
        ->paginate(10);

        // Calculate average rating for each provider
        foreach ($providers as $provider) {
            $provider->average_rating = $provider->reviews->avg('rating') ?? 0;
            $provider->reviews_count = $provider->reviews->count();
        }

        // Get categories and locations for the filter sidebar
        $categories = Category::where('is_active', true)->orderBy('name')->get();
        $eventTypes = EventType::where('is_active', true)->orderBy('name')->get();
        $locations = Location::where('is_active', true)->where('type', 'city')->orderBy('name')->get();

        return view('ClientsView.search.event-type', compact(
            'eventType',
            'providers',
            'categories',
            'eventTypes',
            'locations'
        ));
    }
}
