<?php

namespace App\Http\Controllers\ClientsController;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class DirectBookingController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('client');
    }

    /**
     * Show the form for creating a new direct booking.
     *
     * @param  int  $providerId
     * @param  int  $serviceId
     * @return \Illuminate\View\View
     */
    public function create($providerId, $serviceId)
    {
        $client = auth()->user()->client;

        // Find the provider
        $provider = \App\Models\ProvidersModel\Provider::findOrFail($providerId);

        // Find the service
        $service = \App\Models\ProvidersModel\ProviderService::where('id', $serviceId)
            ->where('provider_id', $providerId)
            ->where('is_active', true)
            ->firstOrFail();

        // Get client's events
        $events = $client->events()
            ->where('date', '>=', now())
            ->orderBy('date')
            ->get();

        return view('ClientsView.bookings.direct-booking', compact(
            'provider',
            'service',
            'events'
        ));
    }

    /**
     * Store a newly created direct booking in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $providerId
     * @param  int  $serviceId
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(Request $request, $providerId, $serviceId)
    {
        $request->validate([
            'event_id' => 'nullable|exists:events,id',
            'title' => 'required|string|max:255',
            'description' => 'nullable|string',
            'event_date' => 'required|date|after_or_equal:today',
            'event_start_time' => 'required|date_format:H:i',
            'event_end_time' => 'required|date_format:H:i|after:event_start_time',
            'event_location' => 'required|string|max:255',
            'guest_count' => 'required|integer|min:1',
            'special_requests' => 'nullable|string',
        ]);

        $client = auth()->user()->client;

        // Find the provider
        $provider = \App\Models\ProvidersModel\Provider::findOrFail($providerId);

        // Find the service
        $service = \App\Models\ProvidersModel\ProviderService::where('id', $serviceId)
            ->where('provider_id', $providerId)
            ->where('is_active', true)
            ->firstOrFail();

        // Calculate total amount based on service pricing
        $totalAmount = $this->calculateTotalAmount($service, $request->guest_count);

        // Create the booking
        $booking = new \App\Models\Booking();
        $booking->client_id = $client->id;
        $booking->provider_id = $provider->id;
        $booking->provider_service_id = $service->id;
        $booking->event_id = $request->event_id;
        $booking->title = $request->title;
        $booking->description = $request->description;
        $booking->event_date = $request->event_date;
        $booking->event_start_time = $request->event_start_time;
        $booking->event_end_time = $request->event_end_time;
        $booking->event_location = $request->event_location;
        $booking->total_amount = $totalAmount;
        $booking->deposit_amount = $totalAmount * 0.3; // 30% deposit
        $booking->paid_amount = 0;
        $booking->balance_amount = $totalAmount;
        $booking->payment_status = 'pending';
        $booking->status = 'pending';
        $booking->save();

        // Create a message to notify the provider
        $message = new \App\Models\Message();
        $message->sender_id = auth()->id();
        $message->recipient_id = $provider->user_id;
        $message->booking_id = $booking->id;
        $message->message = "J'ai créé une réservation directe pour votre service. Merci de la confirmer dès que possible.";
        if ($request->special_requests) {
            $message->message .= "\n\nDemandes spéciales: " . $request->special_requests;
        }
        $message->save();

        // Si la réservation est associée à un événement, mettre à jour ou créer une tâche correspondante
        if ($request->event_id) {
            $event = \App\Models\ClientsModel\Event::find($request->event_id);

            if ($event) {
                // Vérifier si une tâche existe déjà pour ce service
                $existingTask = $event->findTaskForService($service->id);

                if ($existingTask) {
                    // Mettre à jour la tâche existante
                    $existingTask->status = 'in_progress';
                    $existingTask->actual_cost = $booking->total_amount;
                    $existingTask->notes = "Réservation créée: #{$booking->booking_number}";
                    $existingTask->save();
                } else {
                    // Créer une nouvelle tâche pour cette réservation
                    $taskCategory = $this->mapServiceCategoryToTaskCategory($service->category->name ?? 'other');

                    $task = new \App\Models\ClientsModel\EventTask();
                    $task->event_id = $event->id;
                    $task->title = "Réservation: " . $service->title;
                    $task->description = "Prestataire: " . $provider->business_name . "\n" .
                                        "Service: " . $service->title . "\n" .
                                        "Description: " . $service->description . "\n" .
                                        "provider_service_id: " . $service->id;
                    $task->category = $taskCategory;
                    $task->status = 'in_progress';
                    $task->due_date = $booking->event_date;
                    $task->budget = $booking->total_amount;
                    $task->actual_cost = $booking->total_amount;
                    $task->assigned_to = $client->user->name;
                    $task->notes = "Réservation créée: #{$booking->booking_number}";
                    $task->save();
                }
            }
        }

        return redirect()->route('client.bookings.show', $booking->id)
            ->with('status', 'Réservation créée avec succès. Veuillez attendre la confirmation du prestataire.');
    }

    /**
     * Calculate the total amount based on service pricing and guest count.
     *
     * @param  \App\Models\ProvidersModel\ProviderService  $service
     * @param  int  $guestCount
     * @return float
     */
    private function calculateTotalAmount($service, $guestCount)
    {
        $options = $service->options ?? [];
        $basePrice = $options['base_price'] ?? 0;

        // Si c'est un service de traiteur, calculer le prix total en fonction du nombre d'invités
        if ($service->category && $service->category->name == 'Traiteur') {
            $perPersonPrice = $service->getRealPrice();
            return $perPersonPrice * $guestCount;
        }

        // Pour les autres types de services, utiliser le prix de base
        return $basePrice;
    }

    /**
     * Map service category name to task category.
     *
     * @param  string  $categoryName
     * @return string
     */
    private function mapServiceCategoryToTaskCategory($categoryName)
    {
        $mapping = [
            'Traiteur' => 'food',
            'Photographie' => 'photography',
            'Vidéographie' => 'photography',
            'Musique & Animation' => 'music',
            'Espaces de Réception' => 'venue',
            'Décoration' => 'decoration',
            'Transport' => 'transportation',
        ];

        return $mapping[$categoryName] ?? 'other';
    }
}
