<?php

namespace App\Http\Controllers\ClientsController;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class PaymentController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
        $this->middleware('client');
    }

    /**
     * Display the payment page for a booking.
     *
     * @param  int  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        $client = auth()->user()->client;

        $booking = \App\Models\Booking::where('id', $id)
            ->where('client_id', $client->id)
            ->with(['provider', 'providerService', 'quote', 'payments'])
            ->firstOrFail();

        // Get all active payment methods
        $paymentMethods = \App\Models\PaymentMethod::where('is_active', true)
            ->orderBy('display_order')
            ->get();

        // Group payment methods by type
        $mobileMoneyMethods = $paymentMethods->where('type', 'mobile_money');
        $bankCardMethods = $paymentMethods->where('type', 'bank_card');

        return view('ClientsView.dashboard.payments.show', compact(
            'booking',
            'mobileMoneyMethods',
            'bankCardMethods'
        ));
    }

    /**
     * Process a mobile money payment.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function processMobileMoney(Request $request, $id)
    {
        $request->validate([
            'payment_method_id' => 'required|exists:payment_methods,id',
            'transaction_id' => 'required|string|max:50',
            'amount' => 'required|numeric|min:1',
        ]);

        $client = auth()->user()->client;

        $booking = \App\Models\Booking::where('id', $id)
            ->where('client_id', $client->id)
            ->firstOrFail();

        // Check if the amount is valid
        if ($request->amount > $booking->total_amount - $booking->paid_amount) {
            return redirect()->back()->with('error', 'Le montant du paiement ne peut pas dépasser le solde restant.');
        }

        // Get the payment method
        $paymentMethod = \App\Models\PaymentMethod::findOrFail($request->payment_method_id);

        // Record the payment
        $payment = $booking->recordPayment(
            $request->amount,
            $paymentMethod->id,
            $request->transaction_id,
            [
                'method' => $paymentMethod->name,
                'provider' => $paymentMethod->provider,
                'transaction_id' => $request->transaction_id,
            ]
        );

        // Create an invoice if it doesn't exist
        if (!$booking->invoices()->exists()) {
            $this->createInvoice($booking);
        }

        return redirect()->route('client.bookings.show', $booking->id)
            ->with('status', 'Paiement effectué avec succès. Votre réservation a été mise à jour.');
    }

    /**
     * Process a bank card payment.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function processBankCard(Request $request, $id)
    {
        $request->validate([
            'payment_method_id' => 'required|exists:payment_methods,id',
            'card_number' => 'required|string|max:19',
            'card_holder' => 'required|string|max:100',
            'expiry_month' => 'required|numeric|min:1|max:12',
            'expiry_year' => 'required|numeric|min:' . date('Y') . '|max:' . (date('Y') + 20),
            'cvv' => 'required|string|max:4',
            'amount' => 'required|numeric|min:1',
        ]);

        $client = auth()->user()->client;

        $booking = \App\Models\Booking::where('id', $id)
            ->where('client_id', $client->id)
            ->firstOrFail();

        // Check if the amount is valid
        if ($request->amount > $booking->total_amount - $booking->paid_amount) {
            return redirect()->back()->with('error', 'Le montant du paiement ne peut pas dépasser le solde restant.');
        }

        // Get the payment method
        $paymentMethod = \App\Models\PaymentMethod::findOrFail($request->payment_method_id);

        // In a real application, you would process the payment with a payment gateway here
        // For this example, we'll simulate a successful payment
        $transactionId = 'CARD-' . strtoupper(\Illuminate\Support\Str::random(8));

        // Record the payment
        $payment = $booking->recordPayment(
            $request->amount,
            $paymentMethod->id,
            $transactionId,
            [
                'method' => $paymentMethod->name,
                'provider' => $paymentMethod->provider,
                'card_number' => substr($request->card_number, -4),
                'card_holder' => $request->card_holder,
            ]
        );

        // Create an invoice if it doesn't exist
        if (!$booking->invoices()->exists()) {
            $this->createInvoice($booking);
        }

        return redirect()->route('client.bookings.show', $booking->id)
            ->with('status', 'Paiement effectué avec succès. Votre réservation a été mise à jour.');
    }

    /**
     * Create an invoice for a booking.
     *
     * @param  \App\Models\Booking  $booking
     * @return \App\Models\Invoice
     */
    private function createInvoice($booking)
    {
        // Generate a unique invoice number
        $invoiceNumber = 'INV-' . strtoupper(\Illuminate\Support\Str::random(8));

        // Create the invoice
        $invoice = new \App\Models\Invoice([
            'booking_id' => $booking->id,
            'client_id' => $booking->client_id,
            'provider_id' => $booking->provider_id,
            'invoice_number' => $invoiceNumber,
            'subtotal_amount' => $booking->total_amount,
            'tax_amount' => 0, // No tax for now
            'discount_amount' => 0, // No discount for now
            'total_amount' => $booking->total_amount,
            'paid_amount' => $booking->paid_amount,
            'due_amount' => $booking->total_amount - $booking->paid_amount,
            'status' => $booking->isPaid() ? 'paid' : ($booking->paid_amount > 0 ? 'partially_paid' : 'pending'),
            'due_date' => now()->addDays(7), // Due in 7 days
            'notes' => 'Facture pour la réservation #' . $booking->booking_number,
        ]);

        // Generate QR code
        $qrCodeData = json_encode([
            'invoice_number' => $invoiceNumber,
            'amount' => $booking->total_amount,
            'client' => $booking->client->user->name,
            'provider' => $booking->provider->business_name,
            'date' => now()->format('Y-m-d'),
        ]);

        $invoice->qr_code = $this->generateQrCode($qrCodeData);
        $invoice->save();

        return $invoice;
    }

    /**
     * Generate a QR code for an invoice.
     *
     * @param  string  $data
     * @return string
     */
    private function generateQrCode($data)
    {
        // In a real application, you would use a QR code library
        // For this example, we'll just return a placeholder
        return 'qr_code_placeholder';
    }
}
