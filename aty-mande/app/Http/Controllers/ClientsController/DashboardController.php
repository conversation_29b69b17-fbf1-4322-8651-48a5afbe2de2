<?php

namespace App\Http\Controllers\ClientsController;

use App\Http\Controllers\Controller;
use App\Models\Booking;
use App\Models\ClientsModel\Event;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    /**
     * Display the client dashboard.
     *
     * @return \Illuminate\View\View
     */
    public function index()
    {
        $client = Auth::user()->client;

        if (!$client) {
            return redirect()->route('home')->with('error', 'Profil client non trouvé. Veuillez contacter l\'administrateur.');
        }

        try {
            // Get upcoming events
            $upcomingEvents = Event::where('client_id', $client->id)
                ->where('date', '>=', now())
                ->orderBy('date')
                ->take(5)
                ->get();

            // Get recent bookings
            $recentBookings = Booking::where('client_id', $client->id)
                ->with(['provider', 'providerService'])
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get();

            // Get counts for dashboard stats
            $stats = [
                'events' => Event::where('client_id', $client->id)->count(),
                'bookings' => Booking::where('client_id', $client->id)->count(),
                'active_bookings' => Booking::where('client_id', $client->id)
                    ->whereIn('status', ['pending', 'confirmed', 'in_progress'])
                    ->count(),
            ];
        } catch (\Exception $e) {
            \Log::error('Erreur dans le tableau de bord client: ' . $e->getMessage());
            $upcomingEvents = collect([]);
            $recentBookings = collect([]);
            $stats = [
                'events' => 0,
                'bookings' => 0,
                'active_bookings' => 0,
            ];
        }

        return view('ClientsView.dashboard.index', compact(
            'upcomingEvents',
            'recentBookings',
            'stats'
        ));
    }
}
