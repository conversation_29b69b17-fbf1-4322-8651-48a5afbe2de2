<?php

namespace App\Http\Controllers\ClientsController;

use App\Http\Controllers\Controller;
use App\Models\ProvidersModel\Provider;
use App\Models\ProvidersModel\ProviderService;
use App\Models\Review;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class ProviderViewController extends Controller
{
    /**
     * Display the provider profile.
     *
     * @param  string  $id
     * @return \Illuminate\View\View
     */
    public function show($id)
    {
        // Find the provider by ID
        $provider = Provider::where('id', $id)
            ->with([
                'user',
                'categories',
                'services' => function($query) {
                    $query->where('is_active', true);
                },
                'gallery',
                'reviews' => function($query) {
                    $query->where('is_approved', true)
                          ->orderBy('created_at', 'desc');
                },
                'servicePackages' => function($query) {
                    $query->where('is_active', true);
                }
            ])
            ->firstOrFail();

        // Calculate average rating
        $averageRating = $provider->reviews->avg('rating') ?? 0;
        $reviewsCount = $provider->reviews->count();

        // Get featured gallery items
        $featuredGallery = $provider->gallery->where('is_featured', true)->take(6);

        // Get similar providers (same primary category)
        $similarProviders = collect();

        // Correct way to access pivot data
        $primaryCategory = null;
        foreach ($provider->categories as $category) {
            if ($category->pivot->is_primary) {
                $primaryCategory = $category;
                break;
            }
        }

        if ($primaryCategory) {
            $similarProviders = Provider::whereHas('categories', function($query) use ($primaryCategory, $provider) {
                $query->where('categories.id', $primaryCategory->id)
                      ->where('provider_categories.is_primary', true);
            })
            ->where('id', '!=', $provider->id)
            ->with(['gallery' => function($query) {
                $query->where('is_featured', true)->orderBy('order')->limit(1);
            }, 'reviews'])
            ->take(3)
            ->get();

            // Calculate average rating for similar providers
            foreach ($similarProviders as $similarProvider) {
                $similarProvider->average_rating = $similarProvider->reviews->avg('rating') ?? 0;
                $similarProvider->reviews_count = $similarProvider->reviews->count();
            }
        }

        return view('ClientsView.providers.show', compact(
            'provider',
            'averageRating',
            'reviewsCount',
            'featuredGallery',
            'similarProviders'
        ));
    }

    /**
     * Display the provider's service details.
     *
     * @param  string  $providerId
     * @param  string  $serviceId
     * @return \Illuminate\View\View
     */
    public function showService($providerId, $serviceId)
    {
        // Find the provider
        $provider = Provider::where('id', $providerId)
            ->with(['user', 'categories'])
            ->firstOrFail();

        // Find the service
        $service = ProviderService::where('id', $serviceId)
            ->where('provider_id', $providerId)
            ->where('is_active', true)
            ->firstOrFail();

        // Get service gallery items
        $serviceGallery = $provider->gallery->where('provider_service_id', $serviceId);

        // Get other services from this provider
        $otherServices = $provider->services->where('id', '!=', $serviceId)->where('is_active', true)->take(3);

        return view('ClientsView.providers.service', compact(
            'provider',
            'service',
            'serviceGallery',
            'otherServices'
        ));
    }

    /**
     * Display the provider's reviews.
     *
     * @param  string  $id
     * @return \Illuminate\View\View
     */
    public function showReviews($id)
    {
        // Find the provider
        $provider = Provider::where('id', $id)
            ->with(['user', 'categories'])
            ->firstOrFail();

        // Get reviews with pagination
        $reviews = Review::where('provider_id', $id)
            ->where('is_approved', true)
            ->with('client.user')
            ->orderBy('created_at', 'desc')
            ->paginate(10);

        // Calculate average rating
        $averageRating = Review::where('provider_id', $id)
            ->where('is_approved', true)
            ->avg('rating') ?? 0;

        // Calculate rating breakdown
        $ratingBreakdown = [
            5 => Review::where('provider_id', $id)->where('is_approved', true)->where('rating', 5)->count(),
            4 => Review::where('provider_id', $id)->where('is_approved', true)->where('rating', 4)->count(),
            3 => Review::where('provider_id', $id)->where('is_approved', true)->where('rating', 3)->count(),
            2 => Review::where('provider_id', $id)->where('is_approved', true)->where('rating', 2)->count(),
            1 => Review::where('provider_id', $id)->where('is_approved', true)->where('rating', 1)->count(),
        ];

        $totalReviews = array_sum($ratingBreakdown);

        return view('ClientsView.providers.reviews', compact(
            'provider',
            'reviews',
            'averageRating',
            'ratingBreakdown',
            'totalReviews'
        ));
    }



    /**
     * Redirect to direct booking form.
     *
     * @param  string  $id
     * @param  string  $serviceId
     * @return \Illuminate\Http\RedirectResponse
     */
    public function redirectToBooking($id, $serviceId)
    {
        // Check if user is logged in
        if (!Auth::check()) {
            // Au lieu de rediriger vers la page de connexion, retourner à la page du service
            // avec un indicateur pour ouvrir la modal de connexion
            return redirect()->route('providers.service', ['providerId' => $id, 'serviceId' => $serviceId])
                ->with('open_login_modal', true)
                ->with('redirect_after_login', route('client.direct-booking.create', ['providerId' => $id, 'serviceId' => $serviceId]));
        }

        // Check if user is a client
        if (!Auth::user()->isClient()) {
            return redirect()->route('providers.service', ['providerId' => $id, 'serviceId' => $serviceId])
                ->with('error', 'Seuls les clients peuvent effectuer des réservations');
        }

        return redirect()->route('client.direct-booking.create', ['providerId' => $id, 'serviceId' => $serviceId]);
    }


}
