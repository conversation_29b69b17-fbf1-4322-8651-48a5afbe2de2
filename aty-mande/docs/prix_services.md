# Documentation sur la structure des prix des services prestataires

## Introduction

Ce document explique la structure des prix des services prestataires dans l'application Planifeo. Il décrit comment les prix sont stockés, affichés et gérés dans le système.

## Structure de la base de données

### Table `provider_services`

La table `provider_services` contient les informations sur les services proposés par les prestataires. Les champs liés aux prix sont :

- `price` : Le prix principal du service (decimal)
- `is_negotiable` : Indique si le prix est négociable (boolean)
- `details` : Un champ JSON contenant les détails spécifiques à chaque type de service (json)

## Stockage des prix

### Principe général

- **Tous les prix sont stockés dans la colonne `price`** de la table `provider_services`
- Le champ JSON `details` ne contient plus d'informations de prix
- Si `price` est égal à 0, le service est considéré comme "Sur devis"

### Cas particuliers par type de service

#### Traiteur

- Le prix dans la colonne `price` représente le prix fixe du service
- Les détails des plats sont stockés dans le champ JSON `details.menu_items` sans information de prix
- Les options alimentaires (végétarien, sans gluten, etc.) sont stockées dans `details.dietary_options`
- Le nombre minimum et maximum d'invités est stocké dans `details.min_guests` et `details.max_guests`

#### Lieu de réception

- Le prix dans la colonne `price` représente le prix de location
- Les détails comme la capacité, les équipements, etc. sont stockés dans `details`

#### Transport

- Le prix dans la colonne `price` représente le prix de base du service
- Les détails comme le type de véhicule, le nombre de places, etc. sont stockés dans `details`

#### Musique et Animation

- Le prix dans la colonne `price` représente le prix de la prestation
- Les détails comme le type d'artiste, la durée, etc. sont stockés dans `details`

## Affichage des prix

La méthode `getDisplayPrice()` du modèle `ProviderService` est utilisée pour afficher le prix de manière formatée :

```php
public function getDisplayPrice()
{
    // Si le prix est défini, l'utiliser
    if ($this->price > 0) {
        // Si c'est un service de traiteur, ajouter "par personne"
        if ($this->category && $this->category->name == 'Traiteur') {
            return number_format($this->price, 0, ',', ' ') . ' Ar / par personne';
        }

        return number_format($this->price, 0, ',', ' ') . ' Ar';
    }

    // Si le prix n'est pas défini, afficher "Sur devis"
    return 'Sur devis';
}
```

## Création et modification des services

Lors de la création ou de la modification d'un service, le prix est stocké uniquement dans la colonne `price` de la table `provider_services`. Les détails spécifiques à chaque type de service sont stockés dans le champ JSON `details`.

## Recherche et filtrage des services

La recherche et le filtrage des services par prix utilisent uniquement la colonne `price` de la table `provider_services`.

## Conclusion

Cette nouvelle structure simplifie la gestion des prix des services prestataires en utilisant une colonne dédiée pour les prix. Cela rend le code plus clair et plus facile à maintenir, tout en permettant une meilleure performance pour la recherche et le filtrage des services par prix.
