/**
 * Planifeo Dashboard JavaScript
 * Version: 1.0.0
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize sidebar toggle
    initSidebar();
    
    // Initialize tooltips
    initTooltips();
    
    // Initialize popovers
    initPopovers();
    
    // Initialize dropdown menus
    initDropdowns();
    
    // Initialize charts if they exist
    initCharts();
    
    // Initialize datepickers if they exist
    initDatepickers();
    
    // Initialize form validation
    initFormValidation();
});

/**
 * Initialize sidebar functionality
 */
function initSidebar() {
    const menuToggle = document.getElementById('menuToggle');
    const sidebarClose = document.getElementById('sidebarClose');
    const sidebar = document.getElementById('sidebar');
    
    if (menuToggle && sidebar) {
        menuToggle.addEventListener('click', function() {
            sidebar.classList.toggle('show');
        });
    }
    
    if (sidebarClose && sidebar) {
        sidebarClose.addEventListener('click', function() {
            sidebar.classList.remove('show');
        });
    }
    
    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(event) {
        if (sidebar && menuToggle && 
            window.innerWidth < 992 &&
            !sidebar.contains(event.target) &&
            !menuToggle.contains(event.target) &&
            sidebar.classList.contains('show')) {
            sidebar.classList.remove('show');
        }
    });
    
    // Handle submenu toggles
    const submenuToggles = document.querySelectorAll('.submenu-toggle');
    submenuToggles.forEach(toggle => {
        toggle.addEventListener('click', function(e) {
            e.preventDefault();
            const parent = this.closest('.nav-item');
            parent.classList.toggle('submenu-open');
        });
    });
}

/**
 * Initialize Bootstrap tooltips
 */
function initTooltips() {
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function(tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl, {
            boundary: document.body
        });
    });
}

/**
 * Initialize Bootstrap popovers
 */
function initPopovers() {
    const popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
    popoverTriggerList.map(function(popoverTriggerEl) {
        return new bootstrap.Popover(popoverTriggerEl);
    });
}

/**
 * Initialize custom dropdown behavior
 */
function initDropdowns() {
    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        const dropdowns = document.querySelectorAll('.dropdown-menu.show');
        dropdowns.forEach(dropdown => {
            const trigger = document.querySelector(`[data-bs-toggle="dropdown"][aria-expanded="true"]`);
            if (trigger && !trigger.contains(e.target) && !dropdown.contains(e.target)) {
                const dropdownInstance = bootstrap.Dropdown.getInstance(trigger);
                if (dropdownInstance) {
                    dropdownInstance.hide();
                }
            }
        });
    });
}

/**
 * Initialize Chart.js charts if they exist
 */
function initCharts() {
    // Check if Chart.js is loaded
    if (typeof Chart === 'undefined') return;
    
    // Set default Chart.js options
    Chart.defaults.font.family = "'Inter', 'Helvetica', 'Arial', sans-serif";
    Chart.defaults.font.size = 13;
    Chart.defaults.color = '#6b7280';
    Chart.defaults.plugins.tooltip.backgroundColor = 'rgba(17, 24, 39, 0.9)';
    Chart.defaults.plugins.tooltip.padding = 10;
    Chart.defaults.plugins.tooltip.cornerRadius = 4;
    Chart.defaults.plugins.tooltip.titleFont = { weight: 'bold' };
    Chart.defaults.plugins.legend.labels.usePointStyle = true;
    
    // Initialize dashboard charts
    initDashboardCharts();
}

/**
 * Initialize dashboard specific charts
 */
function initDashboardCharts() {
    // Events Overview Chart
    const eventsChartEl = document.getElementById('eventsOverviewChart');
    if (eventsChartEl) {
        const ctx = eventsChartEl.getContext('2d');
        new Chart(ctx, {
            type: 'line',
            data: {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                datasets: [{
                    label: 'Événements',
                    data: [5, 8, 12, 10, 15, 18, 14, 20, 16, 22, 25, 30],
                    borderColor: '#4361ee',
                    backgroundColor: 'rgba(67, 97, 238, 0.1)',
                    borderWidth: 2,
                    tension: 0.3,
                    fill: true,
                    pointBackgroundColor: '#4361ee',
                    pointBorderColor: '#fff',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            borderDash: [2, 4],
                            color: '#e5e7eb'
                        },
                        ticks: {
                            stepSize: 5
                        }
                    }
                }
            }
        });
    }
    
    // Budget Distribution Chart
    const budgetChartEl = document.getElementById('budgetDistributionChart');
    if (budgetChartEl) {
        const ctx = budgetChartEl.getContext('2d');
        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['Lieu', 'Traiteur', 'Décoration', 'Musique', 'Transport', 'Autres'],
                datasets: [{
                    data: [30, 25, 15, 10, 10, 10],
                    backgroundColor: [
                        '#4361ee',
                        '#f72585',
                        '#10b981',
                        '#3b82f6',
                        '#f59e0b',
                        '#6b7280'
                    ],
                    borderWidth: 0,
                    hoverOffset: 5
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                cutout: '70%',
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 15,
                            usePointStyle: true,
                            pointStyle: 'circle'
                        }
                    }
                }
            }
        });
    }
}

/**
 * Initialize datepickers if they exist
 */
function initDatepickers() {
    // Check if Flatpickr is loaded
    if (typeof flatpickr === 'undefined') return;
    
    // Initialize date pickers
    const datePickers = document.querySelectorAll('.datepicker');
    if (datePickers.length > 0) {
        datePickers.forEach(picker => {
            flatpickr(picker, {
                dateFormat: 'd/m/Y',
                locale: 'fr',
                disableMobile: true
            });
        });
    }
    
    // Initialize date range pickers
    const dateRangePickers = document.querySelectorAll('.daterangepicker');
    if (dateRangePickers.length > 0) {
        dateRangePickers.forEach(picker => {
            flatpickr(picker, {
                mode: 'range',
                dateFormat: 'd/m/Y',
                locale: 'fr',
                disableMobile: true
            });
        });
    }
}

/**
 * Initialize form validation
 */
function initFormValidation() {
    // Check if the form validation is needed
    const forms = document.querySelectorAll('.needs-validation');
    
    if (forms.length > 0) {
        // Loop over them and prevent submission
        Array.from(forms).forEach(form => {
            form.addEventListener('submit', event => {
                if (!form.checkValidity()) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                
                form.classList.add('was-validated');
            }, false);
        });
    }
}

/**
 * Show a toast notification
 * @param {string} message - The message to display
 * @param {string} type - The type of toast (success, error, warning, info)
 * @param {number} duration - Duration in milliseconds
 */
function showToast(message, type = 'info', duration = 3000) {
    // Create toast container if it doesn't exist
    let toastContainer = document.querySelector('.toast-container');
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed top-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
    
    // Create toast element
    const toastEl = document.createElement('div');
    toastEl.className = `toast align-items-center text-white bg-${type} border-0`;
    toastEl.setAttribute('role', 'alert');
    toastEl.setAttribute('aria-live', 'assertive');
    toastEl.setAttribute('aria-atomic', 'true');
    
    // Create toast content
    toastEl.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;
    
    // Add toast to container
    toastContainer.appendChild(toastEl);
    
    // Initialize and show toast
    const toast = new bootstrap.Toast(toastEl, {
        autohide: true,
        delay: duration
    });
    toast.show();
    
    // Remove toast after it's hidden
    toastEl.addEventListener('hidden.bs.toast', function() {
        toastEl.remove();
    });
}

/**
 * Confirm action with a modal
 * @param {string} title - Modal title
 * @param {string} message - Modal message
 * @param {Function} callback - Function to call when confirmed
 * @param {string} confirmText - Text for confirm button
 * @param {string} cancelText - Text for cancel button
 */
function confirmAction(title, message, callback, confirmText = 'Confirmer', cancelText = 'Annuler') {
    // Create modal element
    const modalEl = document.createElement('div');
    modalEl.className = 'modal fade';
    modalEl.id = 'confirmActionModal';
    modalEl.setAttribute('tabindex', '-1');
    modalEl.setAttribute('aria-labelledby', 'confirmActionModalLabel');
    modalEl.setAttribute('aria-hidden', 'true');
    
    // Create modal content
    modalEl.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="confirmActionModalLabel">${title}</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    ${message}
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">${cancelText}</button>
                    <button type="button" class="btn btn-primary" id="confirmActionBtn">${confirmText}</button>
                </div>
            </div>
        </div>
    `;
    
    // Add modal to body
    document.body.appendChild(modalEl);
    
    // Initialize modal
    const modal = new bootstrap.Modal(modalEl);
    
    // Add event listener to confirm button
    document.getElementById('confirmActionBtn').addEventListener('click', function() {
        modal.hide();
        if (typeof callback === 'function') {
            callback();
        }
    });
    
    // Remove modal from DOM when hidden
    modalEl.addEventListener('hidden.bs.modal', function() {
        modalEl.remove();
    });
    
    // Show modal
    modal.show();
}
