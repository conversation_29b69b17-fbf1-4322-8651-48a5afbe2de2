/**
 * Simply Toast - A Simple jQuery Toast Plugin
 * @version 1.1.0
 * <AUTHOR>
 * @license MIT
 */

(function($) {
    'use strict';

    $.fn.simplyToast = function(options) {
        // Default options
        var settings = $.extend({
            type: 'info',
            message: 'This is a toast message',
            delay: 4000,
            position: 'bottom-right',
            customClass: '',
            onShow: null,
            onHide: null,
            allowDismiss: true
        }, options);

        // Toast container
        var $container = $('.simply-toast-container');
        if (!$container.length) {
            $container = $('<div class="simply-toast-container"></div>');
            $('body').append($container);
        }

        // Set container position
        $container.attr('class', 'simply-toast-container');
        $container.addClass('position-' + settings.position);

        // Create toast element
        var $toast = $('<div class="simply-toast"></div>');
        $toast.addClass('simply-toast-' + settings.type);
        
        if (settings.customClass) {
            $toast.addClass(settings.customClass);
        }

        // Add dismiss button if allowed
        var dismissHtml = '';
        if (settings.allowDismiss) {
            dismissHtml = '<button type="button" class="simply-toast-dismiss">&times;</button>';
        }

        // Set toast content
        $toast.html(dismissHtml + '<div class="simply-toast-content">' + settings.message + '</div>');
        
        // Append toast to container
        $container.append($toast);

        // Show toast with animation
        setTimeout(function() {
            $toast.addClass('simply-toast-show');
            
            // Call onShow callback if provided
            if (typeof settings.onShow === 'function') {
                settings.onShow.call($toast);
            }
        }, 50);

        // Auto hide toast after delay
        if (settings.delay > 0) {
            setTimeout(function() {
                hideToast($toast, settings.onHide);
            }, settings.delay);
        }

        // Handle dismiss button click
        $toast.find('.simply-toast-dismiss').on('click', function() {
            hideToast($toast, settings.onHide);
        });

        // Function to hide toast
        function hideToast($toast, callback) {
            $toast.removeClass('simply-toast-show');
            
            // Remove toast after animation
            setTimeout(function() {
                $toast.remove();
                
                // Call onHide callback if provided
                if (typeof callback === 'function') {
                    callback.call($toast);
                }
                
                // Remove container if empty
                if ($container.children().length === 0) {
                    $container.remove();
                }
            }, 500);
        }

        // Return toast element for chaining
        return $toast;
    };

    // Shorthand methods for different toast types
    $.simplyToast = function(message, type, options) {
        if (typeof type === 'object') {
            options = type;
            type = 'info';
        }
        
        options = options || {};
        options.message = message;
        options.type = type || 'info';
        
        return $(document).simplyToast(options);
    };

    // Define toast types
    $.simplyToast.success = function(message, options) {
        return $.simplyToast(message, 'success', options);
    };

    $.simplyToast.info = function(message, options) {
        return $.simplyToast(message, 'info', options);
    };

    $.simplyToast.warning = function(message, options) {
        return $.simplyToast(message, 'warning', options);
    };

    $.simplyToast.error = function(message, options) {
        return $.simplyToast(message, 'error', options);
    };

})(jQuery);
