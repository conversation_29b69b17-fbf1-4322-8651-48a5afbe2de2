/**
 * Fonction pour afficher les erreurs de validation avec Simply Toast
 */
function showValidationErrors(errors) {
    // Si errors est un objet, le convertir en tableau
    if (typeof errors === 'object' && !Array.isArray(errors)) {
        const errorsArray = [];
        for (const key in errors) {
            if (errors.hasOwnProperty(key)) {
                if (Array.isArray(errors[key])) {
                    errors[key].forEach(error => {
                        errorsArray.push(error);
                    });
                } else {
                    errorsArray.push(errors[key]);
                }
            }
        }
        errors = errorsArray;
    }

    // Si errors est une chaîne, la convertir en tableau
    if (typeof errors === 'string') {
        errors = [errors];
    }

    // Afficher chaque erreur avec Simply Toast
    if (Array.isArray(errors)) {
        errors.forEach(error => {
            $.simplyToast.error(error, {
                position: 'top-center',
                delay: 0, // 0 = permanent
                allowDismiss: true
            });
        });
    }
}

/**
 * Fonction pour gérer les erreurs de validation AJAX
 */
function handleAjaxValidationErrors(xhr) {
    if (xhr.status === 422) {
        const response = xhr.responseJSON;
        if (response && response.errors) {
            showValidationErrors(response.errors);
        } else if (response && response.message) {
            showValidationErrors(response.message);
        }
    } else if (xhr.status === 500) {
        $.simplyToast.error('Erreur serveur. Veuillez réessayer plus tard.', {
            position: 'top-center',
            delay: 0, // 0 = permanent
            allowDismiss: true
        });
    } else {
        $.simplyToast.error('Une erreur est survenue. Veuillez réessayer.', {
            position: 'top-center',
            delay: 0, // 0 = permanent
            allowDismiss: true
        });
    }
}

/**
 * Fonction pour afficher un message de succès
 */
function showSuccessMessage(message) {
    $.simplyToast.success(message, {
        position: 'top-center',
        delay: 0, // 0 = permanent
        allowDismiss: true
    });
}

/**
 * Fonction pour afficher un message d'information
 */
function showInfoMessage(message) {
    $.simplyToast.info(message, {
        position: 'top-center',
        delay: 0, // 0 = permanent
        allowDismiss: true
    });
}

/**
 * Fonction pour afficher un message d'avertissement
 */
function showWarningMessage(message) {
    $.simplyToast.warning(message, {
        position: 'top-center',
        delay: 0, // 0 = permanent
        allowDismiss: true
    });
}

/**
 * Fonction pour afficher un message d'erreur
 */
function showErrorMessage(message) {
    $.simplyToast.error(message, {
        position: 'top-center',
        delay: 0, // 0 = permanent
        allowDismiss: true
    });
}

// Intercepter les soumissions de formulaire pour gérer les erreurs de validation
$(document).ready(function() {
    // Ajouter la classe 'ajax-form' aux formulaires que vous souhaitez traiter avec AJAX
    $('.ajax-form').on('submit', function(e) {
        e.preventDefault();

        const form = $(this);
        const url = form.attr('action');
        const method = form.attr('method') || 'POST';
        const formData = new FormData(form[0]);

        $.ajax({
            url: url,
            type: method,
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    showSuccessMessage(response.success);

                    // Rediriger si nécessaire
                    if (response.redirect) {
                        setTimeout(function() {
                            window.location.href = response.redirect;
                        }, 1000);
                    }
                }
            },
            error: function(xhr) {
                handleAjaxValidationErrors(xhr);
            }
        });
    });
});
