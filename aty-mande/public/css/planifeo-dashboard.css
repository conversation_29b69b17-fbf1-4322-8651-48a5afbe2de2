/* Planifeo Dashboard CSS - Version 1.0 */

:root {
    --primary: #4361ee;
    --primary-dark: #3a56d4;
    --secondary: #f72585;
    --secondary-dark: #e01e79;
    --success: #10b981;
    --info: #3b82f6;
    --warning: #f59e0b;
    --danger: #ef4444;
    --light: #f9fafb;
    --dark: #111827;
    --gray: #6b7280;
    --gray-light: #e5e7eb;
    --gray-dark: #4b5563;
    --body-bg: #f3f4f6;
    --sidebar-width: 280px;
    --sidebar-collapsed-width: 70px;
    --header-height: 70px;
    --footer-height: 60px;
    --border-radius: 0.5rem;
    --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --transition-base: all 0.3s ease;
    --font-family: 'Inter', sans-serif;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    font-family: var(--font-family);
    font-size: 0.95rem;
    line-height: 1.5;
    color: var(--dark);
    background-color: var(--body-bg);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

a {
    text-decoration: none;
    color: var(--primary);
    transition: var(--transition-base);
}

a:hover {
    color: var(--primary-dark);
}

/* App Container */
.app-container {
    display: flex;
    height: 100vh;
    width: 100%;
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    height: 100vh;
    background: linear-gradient(to bottom, var(--dark), #1e293b);
    color: white;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 1000;
    transition: var(--transition-base);
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.2) transparent;
}

.sidebar::-webkit-scrollbar {
    width: 5px;
}

.sidebar::-webkit-scrollbar-track {
    background: transparent;
}

.sidebar::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
}

.sidebar-header {
    height: var(--header-height);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1.5rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-logo {
    display: flex;
    align-items: center;
}

.sidebar-close {
    background: transparent;
    border: none;
    color: rgba(255, 255, 255, 0.7);
    font-size: 1.25rem;
    cursor: pointer;
    transition: var(--transition-base);
}

.sidebar-close:hover {
    color: white;
}

.sidebar-user {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.user-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    background-color: var(--secondary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    font-size: 1.25rem;
}

.user-info {
    flex: 1;
    min-width: 0;
}

.user-name {
    margin: 0;
    font-weight: 600;
    font-size: 1rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.user-role {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.85rem;
}

.sidebar-nav {
    flex: 1;
    padding: 1.5rem 0;
}

.nav-section {
    margin-bottom: 1.5rem;
}

.nav-section-title {
    padding: 0 1.5rem;
    margin-bottom: 0.75rem;
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 1px;
    color: rgba(255, 255, 255, 0.5);
    font-weight: 600;
}

.nav-items {
    list-style: none;
    padding: 0;
    margin: 0;
}

.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: rgba(255, 255, 255, 0.7);
    border-left: 3px solid transparent;
    transition: var(--transition-base);
}

.nav-link:hover {
    color: white;
    background-color: rgba(255, 255, 255, 0.05);
}

.nav-item.active .nav-link {
    color: white;
    background-color: rgba(255, 255, 255, 0.1);
    border-left-color: var(--secondary);
}

.nav-icon {
    font-size: 1.1rem;
    width: 1.5rem;
    margin-right: 1rem;
    text-align: center;
}

.nav-text {
    flex: 1;
}

.nav-badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 20px;
    height: 20px;
    padding: 0 6px;
    border-radius: 10px;
    background-color: var(--secondary);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
}

.sidebar-footer {
    padding: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.help-card {
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: var(--border-radius);
    padding: 1.25rem;
    text-align: center;
}

.help-card-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--secondary);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin: 0 auto 1rem;
}

.help-card-content h6 {
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.help-card-content p {
    margin-bottom: 1rem;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.85rem;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    transition: var(--transition-base);
}

/* Header */
.header {
    height: var(--header-height);
    background-color: white;
    border-bottom: 1px solid var(--gray-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 1.5rem;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
}

.menu-toggle {
    background: transparent;
    border: none;
    color: var(--gray-dark);
    font-size: 1.25rem;
    cursor: pointer;
    margin-right: 1rem;
    display: none;
}

.header-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    color: var(--dark);
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.header-search {
    width: 300px;
}

.header-search .form-control {
    border-radius: 50px;
    padding-left: 1rem;
    border: 1px solid var(--gray-light);
}

.header-search .btn {
    border-radius: 0 50px 50px 0;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.header-action-btn {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--light);
    border: none;
    color: var(--gray-dark);
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    transition: var(--transition-base);
}

.header-action-btn:hover {
    background-color: var(--gray-light);
    color: var(--dark);
}

.action-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    min-width: 18px;
    height: 18px;
    border-radius: 9px;
    background-color: var(--secondary);
    color: white;
    font-size: 0.7rem;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0 5px;
}

.user-dropdown-toggle {
    padding: 0;
    background: none;
    border: none;
}

.user-dropdown-toggle .user-avatar {
    width: 40px;
    height: 40px;
    font-size: 1rem;
}

/* Notifications Dropdown */
.notifications-dropdown {
    width: 320px;
    padding: 0;
    overflow: hidden;
}

.dropdown-header {
    padding: 1rem;
    border-bottom: 1px solid var(--gray-light);
}

.dropdown-body {
    max-height: 350px;
    overflow-y: auto;
}

.notification-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-bottom: 1px solid var(--gray-light);
    transition: var(--transition-base);
}

.notification-item:hover {
    background-color: var(--light);
}

.notification-item.unread {
    background-color: rgba(67, 97, 238, 0.05);
}

.notification-icon {
    width: 40px;
    height: 40px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
}

.notification-content {
    flex: 1;
    min-width: 0;
}

.notification-text {
    margin: 0 0 0.25rem;
    font-weight: 500;
}

.notification-time {
    font-size: 0.75rem;
    color: var(--gray);
}

.dropdown-footer {
    padding: 0.75rem;
    text-align: center;
    border-top: 1px solid var(--gray-light);
}

/* User Dropdown */
.dropdown-user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
}

.dropdown-user-info .user-avatar {
    width: 40px;
    height: 40px;
    font-size: 1rem;
}

.dropdown-user-info .user-name {
    margin: 0 0 0.25rem;
    color: var(--dark);
}

.dropdown-user-info .user-email {
    margin: 0;
    font-size: 0.85rem;
    color: var(--gray);
}

/* Page Content */
.page-content {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
}

/* Footer */
.footer {
    height: var(--footer-height);
    background-color: white;
    border-top: 1px solid var(--gray-light);
    padding: 0 1.5rem;
    display: flex;
    align-items: center;
}

.footer-links {
    list-style: none;
    display: flex;
    gap: 1.5rem;
    margin: 0;
    padding: 0;
}

.footer-links a {
    color: var(--gray);
    font-size: 0.85rem;
    transition: var(--transition-base);
}

.footer-links a:hover {
    color: var(--primary);
}

/* Responsive */
@media (max-width: 991.98px) {
    .sidebar {
        transform: translateX(-100%);
    }

    .sidebar.show {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
    }

    .menu-toggle {
        display: block;
    }
}

@media (max-width: 767.98px) {
    .header-search {
        display: none;
    }

    .page-content {
        padding: 1rem;
    }
}

/* Cards */
.card {
    background-color: white;
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
    margin-bottom: 1.5rem;
    transition: var(--transition-base);
}

.card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.card-header {
    padding: 1.25rem 1.5rem;
    background-color: transparent;
    border-bottom: 1px solid var(--gray-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark);
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    padding: 1.25rem 1.5rem;
    background-color: transparent;
    border-top: 1px solid var(--gray-light);
}

/* Stat Cards */
.stat-card {
    display: flex;
    align-items: center;
    gap: 1.25rem;
    padding: 1.5rem;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 1.75rem;
    font-weight: 700;
    margin: 0 0 0.25rem;
    color: var(--dark);
}

.stat-label {
    margin: 0;
    color: var(--gray);
    font-size: 0.9rem;
}

.stat-change {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.85rem;
    margin-top: 0.5rem;
}

.stat-change.positive {
    color: var(--success);
}

.stat-change.negative {
    color: var(--danger);
}

/* Buttons */
.btn {
    font-weight: 500;
    padding: 0.5rem 1.25rem;
    border-radius: 0.375rem;
    transition: var(--transition-base);
}

.btn-primary {
    background-color: var(--primary);
    border-color: var(--primary);
}

.btn-primary:hover, .btn-primary:focus {
    background-color: var(--primary-dark);
    border-color: var(--primary-dark);
}

.btn-secondary {
    background-color: var(--secondary);
    border-color: var(--secondary);
}

.btn-secondary:hover, .btn-secondary:focus {
    background-color: var(--secondary-dark);
    border-color: var(--secondary-dark);
}

.btn-outline-primary {
    color: var(--primary);
    border-color: var(--primary);
}

.btn-outline-primary:hover, .btn-outline-primary:focus {
    background-color: var(--primary);
    border-color: var(--primary);
    color: white;
}

.btn-icon {
    width: 36px;
    height: 36px;
    padding: 0;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.375rem;
}

/* Forms */
.form-control, .form-select {
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    border: 1px solid var(--gray-light);
    font-size: 0.95rem;
    transition: var(--transition-base);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 0.25rem rgba(67, 97, 238, 0.25);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
    color: var(--dark);
}

.form-text {
    font-size: 0.85rem;
    color: var(--gray);
}

/* Badges */
.badge {
    font-weight: 500;
    padding: 0.35em 0.65em;
    border-radius: 0.25rem;
}

/* Progress */
.progress {
    height: 0.75rem;
    border-radius: 1rem;
    background-color: var(--gray-light);
    margin-bottom: 0.5rem;
}

.progress-bar {
    border-radius: 1rem;
}

.progress-label {
    display: flex;
    justify-content: space-between;
    font-size: 0.85rem;
    color: var(--gray);
}

/* Tables */
.table {
    margin-bottom: 0;
}

.table th {
    font-weight: 600;
    color: var(--dark);
    border-top: none;
    background-color: var(--light);
}

.table td {
    vertical-align: middle;
}

.table-hover tbody tr:hover {
    background-color: rgba(67, 97, 238, 0.05);
}

/* Utilities */
.bg-primary {
    background-color: var(--primary) !important;
}

.bg-secondary {
    background-color: var(--secondary) !important;
}

.bg-success {
    background-color: var(--success) !important;
}

.bg-info {
    background-color: var(--info) !important;
}

.bg-warning {
    background-color: var(--warning) !important;
}

.bg-danger {
    background-color: var(--danger) !important;
}

.text-primary {
    color: var(--primary) !important;
}

.text-secondary {
    color: var(--secondary) !important;
}

.text-success {
    color: var(--success) !important;
}

.text-info {
    color: var(--info) !important;
}

.text-warning {
    color: var(--warning) !important;
}

.text-danger {
    color: var(--danger) !important;
}

.border-primary {
    border-color: var(--primary) !important;
}

.border-secondary {
    border-color: var(--secondary) !important;
}

.border-success {
    border-color: var(--success) !important;
}

.border-info {
    border-color: var(--info) !important;
}

.border-warning {
    border-color: var(--warning) !important;
}

.border-danger {
    border-color: var(--danger) !important;
}
