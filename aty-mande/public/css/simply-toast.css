/**
 * Simply Toast - A Simple jQuery Toast Plugin
 * @version 1.1.0
 * <AUTHOR>
 * @license MIT
 */

/* Toast Container */
.simply-toast-container {
    position: fixed;
    z-index: 9999;
    padding: 15px;
    width: 320px;
    max-width: 100%;
    box-sizing: border-box;
}

/* Container Positions */
.simply-toast-container.position-top-left {
    top: 0;
    left: 0;
}

.simply-toast-container.position-top-center {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
}

.simply-toast-container.position-top-right {
    top: 0;
    right: 0;
}

.simply-toast-container.position-bottom-left {
    bottom: 0;
    left: 0;
}

.simply-toast-container.position-bottom-center {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
}

.simply-toast-container.position-bottom-right {
    bottom: 0;
    right: 0;
}

/* Toast */
.simply-toast {
    position: relative;
    margin-bottom: 10px;
    padding: 15px 15px 15px 50px;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    color: #fff;
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease-in-out;
    overflow: hidden;
}

.simply-toast:before {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    width: 40px;
    font-family: 'Font Awesome 5 Free', 'FontAwesome';
    font-weight: 900;
    font-size: 18px;
    line-height: 1;
    display: flex;
    align-items: center;
    justify-content: center;
}

.simply-toast.simply-toast-show {
    opacity: 1;
    transform: translateY(0);
}

/* Toast Types */
.simply-toast.simply-toast-success {
    background-color: #28a745;
}

.simply-toast.simply-toast-success:before {
    content: '\f00c';
    background-color: rgba(0, 0, 0, 0.1);
}

.simply-toast.simply-toast-info {
    background-color: #17a2b8;
}

.simply-toast.simply-toast-info:before {
    content: '\f129';
    background-color: rgba(0, 0, 0, 0.1);
}

.simply-toast.simply-toast-warning {
    background-color: #ffc107;
    color: #343a40;
}

.simply-toast.simply-toast-warning:before {
    content: '\f071';
    background-color: rgba(0, 0, 0, 0.1);
}

.simply-toast.simply-toast-error {
    background-color: #dc3545;
}

.simply-toast.simply-toast-error:before {
    content: '\f00d';
    background-color: rgba(0, 0, 0, 0.1);
}

/* Toast Content */
.simply-toast-content {
    word-wrap: break-word;
}

/* Toast Dismiss Button */
.simply-toast-dismiss {
    position: absolute;
    top: 8px;
    right: 8px;
    background: transparent;
    border: none;
    color: inherit;
    font-size: 16px;
    line-height: 1;
    cursor: pointer;
    opacity: 0.7;
    padding: 0;
    width: 20px;
    height: 20px;
    text-align: center;
}

.simply-toast-dismiss:hover {
    opacity: 1;
}

/* Responsive */
@media (max-width: 480px) {
    .simply-toast-container {
        width: 100%;
        padding: 10px;
    }
    
    .simply-toast {
        margin-bottom: 5px;
    }
}
