<!-- Auth Modals for How It Works Page -->

<!-- Client Registration Modal -->
<div class="modal fade" id="clientRegisterModal" tabindex="-1" aria-labelledby="clientRegisterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content border-0 shadow">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="clientRegisterModalLabel">
                    <i class="fas fa-user-plus me-2"></i>Inscription Client
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <div class="row g-0">
                    <div class="col-md-4 d-none d-md-block">
                        <div class="h-100 bg-primary bg-gradient p-4 text-white d-flex flex-column justify-content-between">
                            <div>
                                <h4 class="fw-bold mb-4">Bienvenue sur Planifeo</h4>
                                <p>Rejoignez notre plateforme pour organiser vos événements en toute simplicité.</p>
                            </div>

                            <div class="text-center mb-4">
                                <img src="https://img.freepik.com/free-vector/business-team-discussing-ideas-startup_74855-4380.jpg" alt="Registration" class="img-fluid rounded">
                            </div>

                            <div>
                                <p class="mb-0 small">En vous inscrivant, vous acceptez nos <a href="{{ route('terms') }}" class="text-white text-decoration-underline">conditions d'utilisation</a> et notre <a href="{{ route('privacy') }}" class="text-white text-decoration-underline">politique de confidentialité</a>.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="p-4">
                            <div class="alert alert-danger d-none" id="client-error-alert"></div>

                            <form id="clientRegisterForm" method="POST" action="{{ url('/auth/register-client') }}">
                                @csrf

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="client-name" class="form-label">Nom complet</label>
                                        <input type="text" class="form-control" id="client-name" name="name" required>
                                        <div class="invalid-feedback" id="client-name-error"></div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="client-email" class="form-label">Email</label>
                                        <input type="email" class="form-control" id="client-email" name="email" required>
                                        <div class="invalid-feedback" id="client-email-error"></div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label for="client-password" class="form-label">Mot de passe</label>
                                        <div class="input-group">
                                            <input type="password" class="form-control" id="client-password" name="password" required>
                                            <button class="btn btn-outline-secondary toggle-password" type="button">
                                                <i class="fas fa-eye"></i>
                                            </button>
                                        </div>
                                        <div class="invalid-feedback" id="client-password-error"></div>
                                    </div>

                                    <div class="col-md-6 mb-3">
                                        <label for="client-password-confirm" class="form-label">Confirmer le mot de passe</label>
                                        <input type="password" class="form-control" id="client-password-confirm" name="password_confirmation" required>
                                    </div>
                                </div>

                                <div class="mb-3">
                                    <label for="client-phone" class="form-label">Téléphone</label>
                                    <input type="tel" class="form-control" id="client-phone" name="phone" required>
                                    <div class="invalid-feedback" id="client-phone-error"></div>
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Type de client</label>
                                    <div class="d-flex">
                                        <div class="form-check me-3">
                                            <input class="form-check-input" type="radio" name="client_type" id="client-type-individual" value="individual" checked>
                                            <label class="form-check-label" for="client-type-individual">
                                                Particulier
                                            </label>
                                        </div>
                                        <div class="form-check me-3">
                                            <input class="form-check-input" type="radio" name="client_type" id="client-type-company" value="company">
                                            <label class="form-check-label" for="client-type-company">
                                                Entreprise
                                            </label>
                                        </div>
                                        <div class="form-check">
                                            <input class="form-check-input" type="radio" name="client_type" id="client-type-organization" value="organization">
                                            <label class="form-check-label" for="client-type-organization">
                                                Organisation
                                            </label>
                                        </div>
                                    </div>
                                    <div class="invalid-feedback" id="client-type-error"></div>
                                </div>

                                <div id="company-fields" class="d-none">
                                    <div class="mb-3">
                                        <label for="company-name" class="form-label">Nom de l'entreprise/organisation</label>
                                        <input type="text" class="form-control" id="company-name" name="company_name">
                                        <div class="invalid-feedback" id="company-name-error"></div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="company-position" class="form-label">Votre poste</label>
                                        <input type="text" class="form-control" id="company-position" name="company_position">
                                        <div class="invalid-feedback" id="company-position-error"></div>
                                    </div>
                                </div>

                                <div class="mb-3 form-check">
                                    <input type="checkbox" class="form-check-input" id="client-terms" name="terms_accepted" required>
                                    <label class="form-check-label" for="client-terms">
                                        J'accepte les <a href="{{ route('terms') }}" target="_blank">conditions d'utilisation</a> et la <a href="{{ route('privacy') }}" target="_blank">politique de confidentialité</a>
                                    </label>
                                    <div class="invalid-feedback" id="client-terms-error">
                                        Vous devez accepter les conditions d'utilisation et la politique de confidentialité pour continuer.
                                    </div>
                                </div>

                                <div class="d-grid gap-2 mt-4">
                                    <button type="submit" class="btn btn-primary py-2">
                                        <i class="fas fa-user-plus me-2"></i>Devenir client
                                    </button>
                                </div>
                            </form>

                            <div class="text-center mt-4">
                                <p class="mb-0">Vous avez déjà un compte? <a href="#" data-bs-toggle="modal" data-bs-target="#loginModal" data-bs-dismiss="modal">Se connecter</a></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Provider Registration Modal -->
<div class="modal fade" id="providerRegisterModal" tabindex="-1" aria-labelledby="providerRegisterModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content border-0 shadow">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="providerRegisterModalLabel">
                    <i class="fas fa-briefcase me-2"></i>Inscription Prestataire
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-0">
                <div class="row g-0">
                    <div class="col-md-4 d-none d-md-block">
                        <div class="h-100 bg-primary bg-gradient p-4 text-white d-flex flex-column justify-content-between">
                            <div>
                                <h4 class="fw-bold mb-4">Rejoignez nos prestataires</h4>
                                <p>Développez votre activité en proposant vos services sur notre plateforme.</p>
                            </div>

                            <div class="text-center mb-4">
                                <img src="https://img.freepik.com/free-vector/business-team-discussing-ideas-startup_74855-4380.jpg" alt="Registration" class="img-fluid rounded">
                            </div>

                            <div>
                                <p class="mb-0 small">En vous inscrivant, vous acceptez nos <a href="{{ route('terms') }}" class="text-white text-decoration-underline">conditions d'utilisation</a> et notre <a href="{{ route('privacy') }}" class="text-white text-decoration-underline">politique de confidentialité</a>.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="p-4">
                            <div class="alert alert-danger d-none" id="provider-error-alert"></div>

                            <form id="providerRegisterForm" method="POST" action="{{ url('/auth/register-provider') }}">
                                @csrf

                                <!-- Step Indicator -->
                                <div class="mb-4">
                                    <div class="d-flex justify-content-between position-relative">
                                        <div class="step-item active">
                                            <div class="step-circle">1</div>
                                            <div class="step-text">Compte</div>
                                        </div>
                                        <div class="step-item">
                                            <div class="step-circle">2</div>
                                            <div class="step-text">Entreprise</div>
                                        </div>
                                        <div class="step-item">
                                            <div class="step-circle">3</div>
                                            <div class="step-text">Adresse</div>
                                        </div>
                                        <div class="step-item">
                                            <div class="step-circle">4</div>
                                            <div class="step-text">Services</div>
                                        </div>
                                        <div class="progress position-absolute" style="width: 100%; height: 2px; top: 15px; z-index: -1;">
                                            <div class="progress-bar" role="progressbar" style="width: 0%"></div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Step 1: Account Information -->
                                <div class="provider-step" id="provider-step-1">
                                    <!-- Account information fields -->
                                    <!-- (Same as in the original modal) -->
                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="provider-name" class="form-label">Nom complet</label>
                                            <input type="text" class="form-control" id="provider-name" name="name" required>
                                            <div class="invalid-feedback" id="provider-name-error"></div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="provider-email" class="form-label">Email</label>
                                            <input type="email" class="form-control" id="provider-email" name="email" required>
                                            <div class="invalid-feedback" id="provider-email-error"></div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="provider-password" class="form-label">Mot de passe</label>
                                            <div class="input-group">
                                                <input type="password" class="form-control" id="provider-password" name="password" required>
                                                <button class="btn btn-outline-secondary toggle-password" type="button">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                            <div class="invalid-feedback" id="provider-password-error"></div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="provider-password-confirm" class="form-label">Confirmer le mot de passe</label>
                                            <input type="password" class="form-control" id="provider-password-confirm" name="password_confirmation" required>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="provider-phone" class="form-label">Téléphone</label>
                                        <input type="tel" class="form-control" id="provider-phone" name="phone" required>
                                        <div class="invalid-feedback" id="provider-phone-error"></div>
                                    </div>

                                    <div class="d-flex justify-content-end mt-4">
                                        <button type="button" class="btn btn-primary next-step" data-step="1">
                                            Suivant <i class="fas fa-arrow-right ms-2"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Step 2: Business Information -->
                                <div class="provider-step d-none" id="provider-step-2">
                                    <div class="mb-3">
                                        <label for="business-name" class="form-label">Nom de l'entreprise</label>
                                        <input type="text" class="form-control" id="business-name" name="business_name" required>
                                        <div class="invalid-feedback" id="business-name-error"></div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="business-description" class="form-label">Description de l'entreprise</label>
                                        <textarea class="form-control" id="business-description" name="business_description" rows="3" required></textarea>
                                        <div class="invalid-feedback" id="business-description-error"></div>
                                        <small class="text-muted">Décrivez brièvement votre entreprise et les services que vous proposez.</small>
                                    </div>

                                    <div class="d-flex justify-content-between mt-4">
                                        <button type="button" class="btn btn-outline-secondary prev-step" data-step="2">
                                            <i class="fas fa-arrow-left me-2"></i> Précédent
                                        </button>
                                        <button type="button" class="btn btn-primary next-step" data-step="2">
                                            Suivant <i class="fas fa-arrow-right ms-2"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Step 3: Address Information -->
                                <div class="provider-step d-none" id="provider-step-3">
                                    <div class="row">
                                        <div class="col-md-12 mb-3">
                                            <label for="business-address" class="form-label">Adresse</label>
                                            <input type="text" class="form-control" id="business-address" name="business_address" required>
                                            <div class="invalid-feedback" id="business-address-error"></div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-md-6 mb-3">
                                            <label for="business-city" class="form-label">Ville</label>
                                            <input type="text" class="form-control" id="business-city" name="business_city" required>
                                            <div class="invalid-feedback" id="business-city-error"></div>
                                        </div>

                                        <div class="col-md-6 mb-3">
                                            <label for="business-country" class="form-label">Pays</label>
                                            <input type="text" class="form-control" id="business-country" name="business_country" value="Madagascar" required>
                                            <div class="invalid-feedback" id="business-country-error"></div>
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between mt-4">
                                        <button type="button" class="btn btn-outline-secondary prev-step" data-step="3">
                                            <i class="fas fa-arrow-left me-2"></i> Précédent
                                        </button>
                                        <button type="button" class="btn btn-primary next-step" data-step="3">
                                            Suivant <i class="fas fa-arrow-right ms-2"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Step 4: Services and Categories -->
                                <div class="provider-step d-none" id="provider-step-4">
                                    <div class="mb-4">
                                        <label class="form-label">Catégories de services</label>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" id="category-1" name="categories[]" value="1">
                                                    <label class="form-check-label" for="category-1">Photographie</label>
                                                </div>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" id="category-2" name="categories[]" value="2">
                                                    <label class="form-check-label" for="category-2">Traiteur</label>
                                                </div>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" id="category-3" name="categories[]" value="3">
                                                    <label class="form-check-label" for="category-3">Décoration</label>
                                                </div>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" id="category-4" name="categories[]" value="4">
                                                    <label class="form-check-label" for="category-4">Animation</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" id="category-5" name="categories[]" value="5">
                                                    <label class="form-check-label" for="category-5">Lieu</label>
                                                </div>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" id="category-6" name="categories[]" value="6">
                                                    <label class="form-check-label" for="category-6">Musique</label>
                                                </div>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" id="category-7" name="categories[]" value="7">
                                                    <label class="form-check-label" for="category-7">Vidéographie</label>
                                                </div>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" id="category-8" name="categories[]" value="8">
                                                    <label class="form-check-label" for="category-8">Transport</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="invalid-feedback" id="provider-categories-error"></div>
                                        <small class="text-muted">Sélectionnez toutes les catégories qui s'appliquent à vos services.</small>
                                    </div>

                                    <div class="mb-4">
                                        <label class="form-label">Types d'événements</label>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" id="event-weddings" name="event_types[]" value="weddings">
                                                    <label class="form-check-label" for="event-weddings">Mariages</label>
                                                </div>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" id="event-corporate" name="event_types[]" value="corporate_events">
                                                    <label class="form-check-label" for="event-corporate">Événements d'entreprise</label>
                                                </div>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" id="event-private" name="event_types[]" value="private_parties">
                                                    <label class="form-check-label" for="event-private">Fêtes privées</label>
                                                </div>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" id="event-social" name="event_types[]" value="social_events">
                                                    <label class="form-check-label" for="event-social">Événements sociaux</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" id="event-birthdays" name="event_types[]" value="birthdays">
                                                    <label class="form-check-label" for="event-birthdays">Anniversaires</label>
                                                </div>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" id="event-graduations" name="event_types[]" value="graduations">
                                                    <label class="form-check-label" for="event-graduations">Remises de diplômes</label>
                                                </div>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" id="event-religious" name="event_types[]" value="religious_events">
                                                    <label class="form-check-label" for="event-religious">Événements religieux</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="invalid-feedback" id="provider-event-types-error"></div>
                                        <small class="text-muted">Sélectionnez tous les types d'événements pour lesquels vous offrez vos services.</small>
                                    </div>

                                    <div class="mb-4">
                                        <label class="form-label">Services spécifiques</label>
                                        <div class="row">
                                            <div class="col-md-6">
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" id="service-delivery" name="services[]" value="delivery">
                                                    <label class="form-check-label" for="service-delivery">Livraison incluse</label>
                                                </div>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" id="service-installation" name="services[]" value="installation">
                                                    <label class="form-check-label" for="service-installation">Installation incluse</label>
                                                </div>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" id="service-customization" name="services[]" value="customization">
                                                    <label class="form-check-label" for="service-customization">Personnalisation possible</label>
                                                </div>
                                            </div>
                                            <div class="col-md-6">
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" id="service-samples" name="services[]" value="samples">
                                                    <label class="form-check-label" for="service-samples">Échantillons disponibles</label>
                                                </div>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" id="service-online-booking" name="services[]" value="online_booking">
                                                    <label class="form-check-label" for="service-online-booking">Réservation en ligne</label>
                                                </div>
                                                <div class="form-check mb-2">
                                                    <input class="form-check-input" type="checkbox" id="service-online-payment" name="services[]" value="online_payment">
                                                    <label class="form-check-label" for="service-online-payment">Paiement en ligne</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="mb-4 form-check">
                                        <input type="checkbox" class="form-check-input" id="provider-terms" name="terms_accepted" required>
                                        <label class="form-check-label" for="provider-terms">
                                            J'accepte les <a href="{{ route('terms') }}" target="_blank">conditions d'utilisation</a> et la <a href="{{ route('privacy') }}" target="_blank">politique de confidentialité</a>
                                        </label>
                                        <div class="invalid-feedback" id="provider-terms-error">
                                            Vous devez accepter les conditions d'utilisation et la politique de confidentialité pour continuer.
                                        </div>
                                    </div>

                                    <div class="d-flex justify-content-between mt-4">
                                        <button type="button" class="btn btn-outline-secondary prev-step" data-step="4">
                                            <i class="fas fa-arrow-left me-2"></i> Précédent
                                        </button>
                                        <button type="submit" class="btn btn-primary py-2">
                                            <i class="fas fa-briefcase me-2"></i>Devenir prestataire
                                        </button>
                                    </div>
                                </div>
                            </form>

                            <div class="text-center mt-4">
                                <p class="mb-0">Vous avez déjà un compte? <a href="#" data-bs-toggle="modal" data-bs-target="#loginModal" data-bs-dismiss="modal">Se connecter</a></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- CSS for Provider Registration Steps -->
<style>
    .step-item {
        text-align: center;
        z-index: 1;
        flex: 1;
    }

    .step-circle {
        width: 30px;
        height: 30px;
        border-radius: 50%;
        background-color: #e9ecef;
        color: #6c757d;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 8px;
        font-weight: bold;
        transition: all 0.3s ease;
    }

    .step-text {
        font-size: 0.8rem;
        color: #6c757d;
        transition: all 0.3s ease;
    }

    .step-item.active .step-circle {
        background-color: var(--primary-color);
        color: white;
    }

    .step-item.active .step-text {
        color: var(--primary-color);
        font-weight: bold;
    }

    .step-item.completed .step-circle {
        background-color: #28a745;
        color: white;
    }

    .provider-step {
        transition: all 0.3s ease;
    }
</style>

<!-- JavaScript for Auth Modals -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Toggle password visibility
    document.querySelectorAll('.toggle-password').forEach(button => {
        button.addEventListener('click', function() {
            const input = this.previousElementSibling;
            const icon = this.querySelector('i');

            if (input.type === 'password') {
                input.type = 'text';
                icon.classList.remove('fa-eye');
                icon.classList.add('fa-eye-slash');
            } else {
                input.type = 'password';
                icon.classList.remove('fa-eye-slash');
                icon.classList.add('fa-eye');
            }
        });
    });

    // Toggle company fields based on client type
    const clientTypeRadios = document.querySelectorAll('input[name="client_type"]');
    const companyFields = document.getElementById('company-fields');

    if (clientTypeRadios.length > 0 && companyFields) {
        clientTypeRadios.forEach(radio => {
            radio.addEventListener('change', function() {
                if (this.value === 'company' || this.value === 'organization') {
                    companyFields.classList.remove('d-none');
                } else {
                    companyFields.classList.add('d-none');
                }
            });
        });
    }

    // Provider registration steps navigation
    function setupProviderSteps() {
        // Get all step navigation buttons
        const nextButtons = document.querySelectorAll('.next-step');
        const prevButtons = document.querySelectorAll('.prev-step');
        const stepItems = document.querySelectorAll('.step-item');
        const progressBar = document.querySelector('.progress-bar');

        if (nextButtons.length === 0 || prevButtons.length === 0 || stepItems.length === 0 || !progressBar) {
            return;
        }

        // Add click event to all next buttons
        nextButtons.forEach(button => {
            button.onclick = function() {
                const currentStep = parseInt(this.getAttribute('data-step'));
                const currentStepElement = document.getElementById(`provider-step-${currentStep}`);
                const nextStepElement = document.getElementById(`provider-step-${currentStep + 1}`);

                // Simple validation - check if required fields are filled
                let isValid = true;
                const requiredFields = currentStepElement.querySelectorAll('input[required], textarea[required]');

                requiredFields.forEach(field => {
                    if (!field.value.trim()) {
                        field.classList.add('is-invalid');
                        isValid = false;
                    } else {
                        field.classList.remove('is-invalid');
                    }
                });

                if (!isValid) return;

                // Move to next step
                currentStepElement.classList.add('d-none');
                nextStepElement.classList.remove('d-none');

                // Update step indicators
                stepItems[currentStep - 1].classList.add('completed');
                stepItems[currentStep].classList.add('active');

                // Update progress bar
                const progress = (currentStep / (stepItems.length - 1)) * 100;
                progressBar.style.width = `${progress}%`;
            };
        });

        // Add click event to all previous buttons
        prevButtons.forEach(button => {
            button.onclick = function() {
                const currentStep = parseInt(this.getAttribute('data-step'));
                const currentStepElement = document.getElementById(`provider-step-${currentStep}`);
                const prevStepElement = document.getElementById(`provider-step-${currentStep - 1}`);

                // Move to previous step
                currentStepElement.classList.add('d-none');
                prevStepElement.classList.remove('d-none');

                // Update step indicators
                stepItems[currentStep - 1].classList.remove('active');
                stepItems[currentStep - 2].classList.remove('completed');
                stepItems[currentStep - 2].classList.add('active');

                // Update progress bar
                const progress = ((currentStep - 2) / (stepItems.length - 1)) * 100;
                progressBar.style.width = `${progress}%`;
            };
        });
    }

    // Initialize provider steps when the modal is shown
    const providerRegisterModal = document.getElementById('providerRegisterModal');
    if (providerRegisterModal) {
        providerRegisterModal.addEventListener('shown.bs.modal', setupProviderSteps);
    }

    // Also initialize immediately
    setupProviderSteps();

    // Handle form submissions
    const clientRegisterForm = document.getElementById('clientRegisterForm');
    if (clientRegisterForm) {
        clientRegisterForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const errorAlert = document.getElementById('client-error-alert');

            // Reset previous errors
            errorAlert.classList.add('d-none');
            errorAlert.innerHTML = '';

            // Submit the form
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = data.redirect;
                } else {
                    // Display validation errors
                    errorAlert.classList.remove('d-none');
                    errorAlert.innerHTML = '<ul class="mb-0"><li>Une erreur est survenue lors de l\'inscription. Veuillez vérifier vos informations.</li></ul>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                errorAlert.classList.remove('d-none');
                errorAlert.textContent = 'Une erreur est survenue. Veuillez réessayer.';
            });
        });
    }

    const providerRegisterForm = document.getElementById('providerRegisterForm');
    if (providerRegisterForm) {
        providerRegisterForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const errorAlert = document.getElementById('provider-error-alert');

            // Reset previous errors
            errorAlert.classList.add('d-none');
            errorAlert.innerHTML = '';

            // Submit the form
            fetch(this.action, {
                method: 'POST',
                body: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    window.location.href = data.redirect;
                } else {
                    // Display validation errors
                    errorAlert.classList.remove('d-none');
                    errorAlert.innerHTML = '<ul class="mb-0"><li>Une erreur est survenue lors de l\'inscription. Veuillez vérifier vos informations.</li></ul>';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                errorAlert.classList.remove('d-none');
                errorAlert.textContent = 'Une erreur est survenue. Veuillez réessayer.';
            });
        });
    }
});
</script>
