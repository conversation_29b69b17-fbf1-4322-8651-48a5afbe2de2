<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title') - Planifeo</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="{{ asset('images/favicon.svg') }}">
    <link rel="icon" type="image/png" href="{{ asset('images/favicon.png') }}">

    <!-- Styles -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="{{ asset('css/planifeo-dashboard.css') }}">

    @stack('styles')
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="{{ route('home') }}" class="sidebar-logo">
                    <img src="{{ asset('images/logo.svg') }}" alt="Planifeo" height="35">
                </a>
                <button type="button" class="sidebar-close d-lg-none" id="sidebarClose">
                    <i class="fas fa-times"></i>
                </button>
            </div>

            <div class="sidebar-user">
                <div class="user-avatar">
                    {{ substr(Auth::user()->name, 0, 1) }}
                </div>
                <div class="user-info">
                    <h6 class="user-name">{{ Auth::user()->name }}</h6>
                    <span class="user-role">Client</span>
                </div>
            </div>

            <nav class="sidebar-nav">
                <div class="nav-section">
                    <h6 class="nav-section-title">Menu principal</h6>
                    <ul class="nav-items">
                        <li class="nav-item {{ request()->routeIs('client.dashboard') ? 'active' : '' }}">
                            <a href="{{ route('client.dashboard') }}" class="nav-link">
                                <i class="nav-icon fas fa-tachometer-alt"></i>
                                <span class="nav-text">Tableau de bord</span>
                            </a>
                        </li>
                        <li class="nav-item {{ request()->routeIs('client.events*') ? 'active' : '' }}">
                            <a href="{{ route('client.events') }}" class="nav-link">
                                <i class="nav-icon fas fa-calendar-alt"></i>
                                <span class="nav-text">Mes événements</span>
                                @if(isset($stats['events']) && $stats['events'] > 0)
                                    <span class="nav-badge">{{ $stats['events'] }}</span>
                                @endif
                            </a>
                        </li>

                        <li class="nav-item {{ request()->routeIs('client.bookings*') ? 'active' : '' }}">
                            <a href="{{ route('client.bookings') }}" class="nav-link">
                                <i class="nav-icon fas fa-bookmark"></i>
                                <span class="nav-text">Réservations</span>
                                @if(isset($stats['bookings']) && $stats['bookings'] > 0)
                                    <span class="nav-badge">{{ $stats['bookings'] }}</span>
                                @endif
                            </a>
                        </li>
                        <li class="nav-item {{ request()->routeIs('client.invoices*') ? 'active' : '' }}">
                            <a href="{{ route('client.invoices') }}" class="nav-link">
                                <i class="nav-icon fas fa-file-invoice-dollar"></i>
                                <span class="nav-text">Factures</span>
                            </a>
                        </li>
                    </ul>
                </div>

                <div class="nav-section">
                    <h6 class="nav-section-title">Compte</h6>
                    <ul class="nav-items">
                        <li class="nav-item {{ request()->routeIs('profile.edit') ? 'active' : '' }}">
                            <a href="{{ route('profile.edit') }}" class="nav-link">
                                <i class="nav-icon fas fa-user-circle"></i>
                                <span class="nav-text">Mon profil</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ route('search') }}" class="nav-link">
                                <i class="nav-icon fas fa-search"></i>
                                <span class="nav-text">Rechercher</span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <form method="POST" action="{{ route('logout') }}" id="logout-form">
                                @csrf
                                <a href="#" class="nav-link text-danger" onclick="event.preventDefault(); document.getElementById('logout-form').submit();">
                                    <i class="nav-icon fas fa-sign-out-alt"></i>
                                    <span class="nav-text">Déconnexion</span>
                                </a>
                            </form>
                        </li>
                    </ul>
                </div>
            </nav>

            <div class="sidebar-footer">
                <div class="help-card">
                    <div class="help-card-icon">
                        <i class="fas fa-headset"></i>
                    </div>
                    <div class="help-card-content">
                        <h6>Besoin d'aide ?</h6>
                        <p>Notre équipe est là pour vous aider</p>
                        <a href="{{ route('contact') }}" class="btn btn-sm btn-light w-100">Contactez-nous</a>
                    </div>
                </div>
            </div>
        </aside>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <button type="button" class="menu-toggle" id="menuToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="header-title">@yield('header-title', 'Tableau de bord')</h1>
                </div>

                <div class="header-right">
                    <div class="header-search d-none d-md-flex">
                        <form action="{{ route('search') }}" method="GET">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Rechercher des prestataires..." name="q">
                                <button class="btn btn-primary" type="submit">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>

                    <div class="header-actions">
                        <div class="dropdown">
                            <button class="header-action-btn" type="button" id="notificationsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-bell"></i>
                                <span class="action-badge">3</span>
                            </button>
                            <div class="dropdown-menu dropdown-menu-end notifications-dropdown" aria-labelledby="notificationsDropdown">
                                <div class="dropdown-header d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">Notifications</h6>
                                    <a href="#" class="text-muted small">Tout marquer comme lu</a>
                                </div>
                                <div class="dropdown-body">

                                    <a href="#" class="dropdown-item notification-item unread">
                                        <div class="notification-icon bg-success">
                                            <i class="fas fa-calendar-check text-white"></i>
                                        </div>
                                        <div class="notification-content">
                                            <p class="notification-text">Réservation confirmée</p>
                                            <span class="notification-time">Il y a 1 jour</span>
                                        </div>
                                    </a>
                                    <a href="#" class="dropdown-item notification-item">
                                        <div class="notification-icon bg-warning">
                                            <i class="fas fa-exclamation-triangle text-white"></i>
                                        </div>
                                        <div class="notification-content">
                                            <p class="notification-text">Rappel: Événement dans 3 jours</p>
                                            <span class="notification-time">Il y a 2 jours</span>
                                        </div>
                                    </a>
                                </div>
                                <div class="dropdown-footer">
                                    <a href="#" class="dropdown-item text-center">Voir toutes les notifications</a>
                                </div>
                            </div>
                        </div>

                        <div class="dropdown">
                            <button class="header-action-btn user-dropdown-toggle" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <div class="user-avatar">
                                    {{ substr(Auth::user()->name, 0, 1) }}
                                </div>
                            </button>
                            <div class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                <div class="dropdown-user-info">
                                    <div class="user-avatar">
                                        {{ substr(Auth::user()->name, 0, 1) }}
                                    </div>
                                    <div>
                                        <h6 class="user-name">{{ Auth::user()->name }}</h6>
                                        <p class="user-email">{{ Auth::user()->email }}</p>
                                    </div>
                                </div>
                                <div class="dropdown-divider"></div>
                                <a class="dropdown-item" href="{{ route('client.dashboard') }}">
                                    <i class="fas fa-tachometer-alt me-2"></i>Tableau de bord
                                </a>
                                <a class="dropdown-item" href="{{ route('profile.edit') }}">
                                    <i class="fas fa-user-edit me-2"></i>Mon profil
                                </a>
                                <div class="dropdown-divider"></div>
                                <form method="POST" action="{{ route('logout') }}">
                                    @csrf
                                    <button type="submit" class="dropdown-item text-danger">
                                        <i class="fas fa-sign-out-alt me-2"></i>Déconnexion
                                    </button>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Page Content -->
            <div class="page-content">
                @if (session('status'))
                    <div class="alert alert-success alert-dismissible fade show" role="alert">
                        {{ session('status') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                @if (session('error'))
                    <div class="alert alert-danger alert-dismissible fade show" role="alert">
                        {{ session('error') }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                @endif

                @yield('content')
            </div>

            <!-- Footer -->
            <footer class="footer">
                <div class="container-fluid">
                    <div class="row align-items-center">
                        <div class="col-md-6 text-center text-md-start">
                            <p class="mb-0">&copy; {{ date('Y') }} Planifeo. Tous droits réservés.</p>
                        </div>
                        <div class="col-md-6 text-center text-md-end">
                            <ul class="footer-links">
                                <li><a href="{{ route('home') }}">Accueil</a></li>
                                <li><a href="{{ route('search') }}">Rechercher</a></li>
                                <li><a href="{{ route('faq') }}">FAQ</a></li>
                                <li><a href="{{ route('contact') }}">Contact</a></li>
                            </ul>
                        </div>
                    </div>
                </div>
            </footer>
        </main>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="{{ asset('js/planifeo-dashboard.js') }}"></script>

    @stack('scripts')
</body>
</html>
