<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title') - Planifeo | So easy to plan, so hard to forget</title>

    <!-- Favicon -->
    <link rel="icon" type="image/svg+xml" href="{{ asset('images/favicon.svg') }}">
    <link rel="icon" type="image/png" href="{{ asset('images/favicon.png') }}">

    <!-- Styles -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/animate.css/4.1.1/animate.min.css">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css" />
    <link rel="stylesheet" href="{{ asset('css/custom-pagination.css') }}">
    <link rel="stylesheet" href="{{ asset('css/provider-styles.css') }}">

    <style>
        :root {
            --primary-color: #6366F1;
            --secondary-color: #4F46E5;
            --accent-color: #EC4899;
            --dark-color: #1E293B;
            --light-color: #F8FAFC;
            --gray-color: #94A3B8;
        }

        body {
            font-family: 'Poppins', sans-serif;
            color: var(--dark-color);
            overflow-x: hidden;
            padding-top: 76px; /* For fixed navbar */
        }

        .navbar {
            background-color: rgba(255, 255, 255, 0.95) !important;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 15px 0;
            transition: all 0.3s ease;
        }

        .navbar-scrolled {
            padding: 10px 0;
            background-color: rgba(255, 255, 255, 0.98) !important;
        }

        .navbar-brand {
            font-weight: 700;
            color: var(--primary-color) !important;
            font-size: 1.5rem;
        }

        .card {
            border: none;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
        }

        /* Auth Modal Styles */
        .modal-content {
            border-radius: 15px;
            overflow: hidden;
        }

        .modal-header {
            border-bottom: none;
            padding: 1.5rem;
        }

        .nav-tabs .nav-link {
            color: var(--gray-color);
            border: none;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
            border-radius: 0;
            position: relative;
        }

        .nav-tabs .nav-link.active {
            color: var(--primary-color);
            background: transparent;
            border: none;
        }

        .nav-tabs .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background-color: var(--primary-color);
            border-radius: 3px 3px 0 0;
        }

        .form-control, .input-group-text {
            padding: 0.75rem 1rem;
            border-radius: 8px;
        }

        .form-control:focus {
            box-shadow: 0 0 0 0.25rem rgba(99, 102, 241, 0.25);
        }

        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-primary:hover {
            background-color: var(--secondary-color);
            border-color: var(--secondary-color);
        }

        .btn-outline-primary {
            color: var(--primary-color);
            border-color: var(--primary-color);
        }

        .btn-outline-primary:hover {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }

        /* Animation classes */
        .fade-in {
            animation: fadeIn 0.5s ease-in-out;
        }

        .slide-up {
            animation: slideUp 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }

        @keyframes slideUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
    </style>

    @stack('styles')
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand" href="{{ route('home') }}">
                <img src="{{ asset('images/logo.svg') }}" alt="Planifeo Logo" height="40" class="me-2">
                <div class="d-none d-lg-block" style="font-size: 0.65rem; line-height: 1; margin-top: -3px; letter-spacing: 0.5px;">
                    <span class="text-muted fst-italic">So easy to plan, so hard to forget</span>
                </div>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('home') ? 'active' : '' }}" href="{{ route('home') }}">Accueil</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('search*') ? 'active' : '' }}" href="{{ route('search') }}">Rechercher</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('how-it-works') ? 'active' : '' }}" href="{{ route('how-it-works') }}">Comment ça marche</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('about') ? 'active' : '' }}" href="{{ route('about') }}">À propos</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link {{ request()->routeIs('contact') ? 'active' : '' }}" href="{{ route('contact') }}">Contact</a>
                    </li>
                </ul>
                <div class="d-flex">
                    @auth
                        <div class="dropdown">
                            <button class="btn btn-outline-primary dropdown-toggle d-flex align-items-center" type="button" id="userDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px; font-size: 0.9rem;">
                                    {{ substr(Auth::user()->name, 0, 1) }}
                                </div>
                                <span class="d-none d-md-inline">{{ Auth::user()->name }}</span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="userDropdown">
                                @if(Auth::user()->isClient())
                                    <li><a class="dropdown-item" href="{{ route('client.dashboard') }}"><i class="fas fa-tachometer-alt me-2"></i>Tableau de bord</a></li>
                                    <li><a class="dropdown-item" href="{{ route('client.quote-requests') }}"><i class="fas fa-file-invoice me-2"></i>Demandes de devis</a></li>
                                @elseif(Auth::user()->isProvider())
                                    <li><a class="dropdown-item" href="{{ route('provider.dashboard') }}"><i class="fas fa-tachometer-alt me-2"></i>Tableau de bord</a></li>
                                @elseif(Auth::user()->isAdmin())
                                    <li><a class="dropdown-item" href="{{ route('admin.dashboard') }}"><i class="fas fa-tachometer-alt me-2"></i>Administration</a></li>
                                @endif
                                <li><a class="dropdown-item" href="{{ route('profile.edit') }}"><i class="fas fa-user-edit me-2"></i>Profil</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li>
                                    <form method="POST" action="{{ route('logout') }}">
                                        @csrf
                                        <button type="submit" class="dropdown-item text-danger">
                                            <i class="fas fa-sign-out-alt me-2"></i>Déconnexion
                                        </button>
                                    </form>
                                </li>
                            </ul>
                        </div>
                    @else
                        <a href="#" class="btn btn-outline-primary me-2" data-bs-toggle="modal" data-bs-target="#loginModal">
                            <i class="fas fa-sign-in-alt me-1"></i>Connexion
                        </a>
                        <a href="#" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#registerModal">
                            <i class="fas fa-user-plus me-1"></i>Inscription
                        </a>
                    @endauth
                </div>
            </div>
        </div>
    </nav>

    <main>
        @yield('content')
    </main>

    <footer class="bg-dark text-white py-5 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <div class="mb-4">
                        <a href="{{ route('home') }}" class="text-white text-decoration-none">
                            <img src="{{ asset('images/logo.svg') }}" alt="Planifeo Logo" height="50" class="mb-2" style="filter: brightness(0) invert(1);">
                        </a>
                    </div>
                    <p class="mb-2"><em class="fw-bold">"So easy to plan, so hard to forget"</em></p>
                    <p class="mb-4">Planifeo est une plateforme innovante qui met en relation les organisateurs d'événements avec des prestataires de services qualifiés pour créer des moments inoubliables.</p>
                    <div class="d-flex gap-3">
                        <a href="#" class="text-white fs-5"><i class="fab fa-facebook"></i></a>
                        <a href="#" class="text-white fs-5"><i class="fab fa-instagram"></i></a>
                        <a href="#" class="text-white fs-5"><i class="fab fa-twitter"></i></a>
                        <a href="#" class="text-white fs-5"><i class="fab fa-linkedin"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-4 mb-4">
                    <h5 class="fw-bold mb-4">Liens rapides</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="{{ route('home') }}" class="text-white text-decoration-none"><i class="fas fa-chevron-right me-2 small"></i>Accueil</a></li>
                        <li class="mb-2"><a href="{{ route('search') }}" class="text-white text-decoration-none"><i class="fas fa-chevron-right me-2 small"></i>Rechercher</a></li>
                        <li class="mb-2"><a href="{{ route('how-it-works') }}" class="text-white text-decoration-none"><i class="fas fa-chevron-right me-2 small"></i>Comment ça marche</a></li>
                        <li class="mb-2"><a href="{{ route('about') }}" class="text-white text-decoration-none"><i class="fas fa-chevron-right me-2 small"></i>À propos</a></li>
                        <li class="mb-2"><a href="{{ route('contact') }}" class="text-white text-decoration-none"><i class="fas fa-chevron-right me-2 small"></i>Contact</a></li>
                        <li class="mb-2"><a href="{{ route('faq') }}" class="text-white text-decoration-none"><i class="fas fa-chevron-right me-2 small"></i>FAQ</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-4 mb-4">
                    <h5 class="fw-bold mb-4">Catégories</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2"><a href="{{ route('categories.show', 'photographie') }}" class="text-white text-decoration-none"><i class="fas fa-chevron-right me-2 small"></i>Photographie</a></li>
                        <li class="mb-2"><a href="{{ route('categories.show', 'traiteur') }}" class="text-white text-decoration-none"><i class="fas fa-chevron-right me-2 small"></i>Traiteur</a></li>
                        <li class="mb-2"><a href="{{ route('categories.show', 'decoration') }}" class="text-white text-decoration-none"><i class="fas fa-chevron-right me-2 small"></i>Décoration</a></li>
                        <li class="mb-2"><a href="{{ route('categories.show', 'animation') }}" class="text-white text-decoration-none"><i class="fas fa-chevron-right me-2 small"></i>Animation</a></li>
                        <li class="mb-2"><a href="{{ route('categories.show', 'lieux') }}" class="text-white text-decoration-none"><i class="fas fa-chevron-right me-2 small"></i>Lieux</a></li>
                    </ul>
                </div>
                <div class="col-lg-4 col-md-4 mb-4">
                    <h5 class="fw-bold mb-4">Contactez-nous</h5>
                    <div class="mb-3">
                        <div class="d-flex align-items-center mb-2">
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                <i class="fas fa-envelope text-white"></i>
                            </div>
                            <p class="mb-0"><EMAIL></p>
                        </div>
                        <div class="d-flex align-items-center mb-2">
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                <i class="fas fa-phone text-white"></i>
                            </div>
                            <p class="mb-0">+261 34 00 000 00</p>
                        </div>
                        <div class="d-flex align-items-center">
                            <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center me-3" style="width: 40px; height: 40px;">
                                <i class="fas fa-map-marker-alt text-white"></i>
                            </div>
                            <p class="mb-0">Antananarivo, Madagascar</p>
                        </div>
                    </div>
                </div>
            </div>
            <hr>
            <div class="row align-items-center">
                <div class="col-md-6 text-center text-md-start">
                    <p class="mb-md-0">&copy; {{ date('Y') }} Planifeo. Tous droits réservés.</p>
                </div>
                <div class="col-md-6 text-center text-md-end">
                    <ul class="list-inline mb-0">
                        <li class="list-inline-item"><a href="{{ route('terms') }}" class="text-white text-decoration-none">Conditions d'utilisation</a></li>
                        <li class="list-inline-item mx-3">|</li>
                        <li class="list-inline-item"><a href="{{ route('privacy') }}" class="text-white text-decoration-none">Politique de confidentialité</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/aos@2.3.4/dist/aos.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/gsap@3.11.4/dist/gsap.min.js"></script>
    <script src="{{ asset('js/pagination-fix.js') }}"></script>
    <script src="{{ asset('js/provider-gallery.js') }}"></script>
    <script>
        // Initialize AOS animation
        AOS.init({
            duration: 800,
            once: true
        });

        // Navbar scroll effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('navbar-scrolled');
            } else {
                navbar.classList.remove('navbar-scrolled');
            }
        });

        // GSAP animations for page elements
        document.addEventListener('DOMContentLoaded', function() {
            // Animate navbar items
            gsap.from('.navbar-brand', {
                duration: 1,
                y: -50,
                opacity: 0,
                ease: 'power3.out'
            });

            gsap.from('.navbar-nav .nav-item', {
                duration: 1,
                y: -50,
                opacity: 0,
                stagger: 0.1,
                ease: 'power3.out'
            });

            gsap.from('.navbar .d-flex .btn', {
                duration: 1,
                y: -50,
                opacity: 0,
                stagger: 0.2,
                ease: 'power3.out',
                delay: 0.5
            });
        });
    </script>

    @stack('scripts')

    @guest
        @include('ClientsView.partials.auth-modals')
    @endguest

    <!-- Bulle FAQ flottante et mini-widget -->
    <div class="faq-bubble-container">
        <div class="faq-mini-widget" id="faqMiniWidget">
            <div class="faq-mini-widget-header">
                <h5 class="mb-0">Centre d'aide</h5>
                <button type="button" class="btn-close btn-close-white" aria-label="Close" onclick="toggleFaqWidget(false)"></button>
            </div>

            <!-- Onglets FAQ / Chat -->
            <ul class="nav nav-tabs nav-fill" id="helpTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="faq-tab" data-bs-toggle="tab" data-bs-target="#faq-content" type="button" role="tab" aria-controls="faq-content" aria-selected="true">
                        <i class="fas fa-question-circle me-1"></i>FAQ
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="chat-tab" data-bs-toggle="tab" data-bs-target="#chat-content" type="button" role="tab" aria-controls="chat-content" aria-selected="false">
                        <i class="fas fa-comment-dots me-1"></i>Chat avec Miora
                    </button>
                </li>
            </ul>

            <!-- Contenu des onglets -->
            <div class="tab-content" id="helpTabsContent">
                <!-- Onglet FAQ -->
                <div class="tab-pane fade show active" id="faq-content" role="tabpanel" aria-labelledby="faq-tab">
                    <div class="faq-mini-widget-body">
                        <p class="text-white mb-3">Besoin d'aide ? Voici quelques questions fréquentes :</p>
                        <div class="faq-mini-links">
                            <a href="{{ route('faq') }}#general" class="faq-mini-link">
                                <i class="fas fa-info-circle me-2"></i>Qu'est-ce que Planifeo ?
                            </a>
                            <a href="{{ route('faq') }}#clients" class="faq-mini-link">
                                <i class="fas fa-user me-2"></i>Comment créer un compte ?
                            </a>
                            <a href="{{ route('faq') }}#prestataires" class="faq-mini-link">
                                <i class="fas fa-briefcase me-2"></i>Comment devenir prestataire ?
                            </a>
                            <a href="{{ route('faq') }}#paiements" class="faq-mini-link">
                                <i class="fas fa-credit-card me-2"></i>Quels modes de paiement ?
                            </a>
                        </div>
                        <div class="text-center mt-3">
                            <a href="{{ route('faq') }}" class="btn btn-light btn-sm w-100">
                                Voir toutes les FAQ
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Onglet Chat avec Miora -->
                <div class="tab-pane fade" id="chat-content" role="tabpanel" aria-labelledby="chat-tab">
                    <div class="chat-container">
                        <div class="chat-messages" id="chatMessages">
                            <div class="chat-message ai">
                                <div class="chat-avatar">
                                    <i class="fas fa-robot"></i>
                                </div>
                                <div class="chat-bubble">
                                    <div class="chat-name">Miora</div>
                                    <div class="chat-text">Bonjour ! Je suis Miora, votre assistante virtuelle. Comment puis-je vous aider avec Planifeo aujourd'hui ?</div>
                                </div>
                            </div>
                        </div>
                        <div class="chat-input-container">
                            <input type="text" id="chatInput" class="chat-input" placeholder="Posez votre question..." aria-label="Message">
                            <button id="sendMessageBtn" class="chat-send-btn" onclick="sendMessage()">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                        <div class="chat-suggestions">
                            <button class="chat-suggestion-btn" onclick="useSuggestion('Comment créer un compte ?')">Comment créer un compte ?</button>
                            <button class="chat-suggestion-btn" onclick="useSuggestion('Comment contacter un prestataire ?')">Comment contacter un prestataire ?</button>
                            <button class="chat-suggestion-btn" onclick="useSuggestion('Comment réserver un service ?')">Comment réserver un service ?</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <button class="faq-bubble" id="faqBubble" onclick="toggleFaqWidget(true)" data-bs-toggle="tooltip" data-bs-placement="left" title="Besoin d'aide ? Cliquez ici">
            <div class="faq-bubble-icon">
                <i class="fas fa-question"></i>
            </div>
            <div class="faq-bubble-label">
                <span>Aide</span>
            </div>
        </button>
    </div>

    <!-- Styles pour la bulle FAQ et le mini-widget -->
    <style>
        .faq-bubble-container {
            position: fixed;
            bottom: 30px;
            right: 30px;
            z-index: 1000;
        }

        .faq-bubble {
            display: flex;
            align-items: center;
            border: none;
            background: none;
            padding: 0;
            cursor: pointer;
        }

        .faq-bubble-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(135deg, #6366F1 0%, #4F46E5 100%);
            box-shadow: 0 4px 20px rgba(99, 102, 241, 0.3);
            color: white;
            transition: all 0.3s ease;
            animation: pulse 2s infinite;
            font-size: 24px;
        }

        .faq-bubble-label {
            position: absolute;
            right: 70px;
            background-color: #4F46E5;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 500;
            font-size: 14px;
            opacity: 0;
            transform: translateX(20px);
            transition: all 0.3s ease;
            white-space: nowrap;
            box-shadow: 0 4px 10px rgba(79, 70, 229, 0.3);
        }

        .faq-bubble:hover .faq-bubble-icon {
            transform: scale(1.1);
            box-shadow: 0 6px 25px rgba(99, 102, 241, 0.4);
            animation: none;
        }

        .faq-bubble:hover .faq-bubble-label {
            opacity: 1;
            transform: translateX(0);
        }

        .faq-mini-widget {
            position: absolute;
            bottom: 80px;
            right: 0;
            width: 360px;
            background: linear-gradient(135deg, #6366F1 0%, #4F46E5 100%);
            border-radius: 16px;
            box-shadow: 0 10px 30px rgba(99, 102, 241, 0.3);
            overflow: hidden;
            transform-origin: bottom right;
            transform: scale(0);
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
            pointer-events: none;
        }

        .faq-mini-widget.active {
            transform: scale(1);
            opacity: 1;
            pointer-events: all;
        }

        .faq-mini-widget-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background-color: rgba(0, 0, 0, 0.1);
            color: white;
        }

        .faq-mini-widget-body {
            padding: 20px;
        }

        .faq-mini-links {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .faq-mini-link {
            display: flex;
            align-items: center;
            color: white;
            text-decoration: none;
            padding: 10px;
            border-radius: 8px;
            background-color: rgba(255, 255, 255, 0.1);
            transition: all 0.2s ease;
            font-size: 14px;
        }

        .faq-mini-link:hover {
            background-color: rgba(255, 255, 255, 0.2);
            color: white;
            transform: translateX(5px);
        }

        /* Styles pour les onglets */
        .faq-mini-widget .nav-tabs {
            border-bottom: none;
            background-color: rgba(0, 0, 0, 0.05);
        }

        .faq-mini-widget .nav-tabs .nav-link {
            color: rgba(255, 255, 255, 0.7);
            border: none;
            padding: 10px 0;
            font-size: 14px;
            font-weight: 500;
            border-radius: 0;
            transition: all 0.2s ease;
        }

        .faq-mini-widget .nav-tabs .nav-link:hover {
            color: white;
            background-color: rgba(255, 255, 255, 0.1);
        }

        .faq-mini-widget .nav-tabs .nav-link.active {
            color: white;
            background-color: transparent;
            border-bottom: 3px solid white;
        }

        /* Styles pour le chat */
        .chat-container {
            display: flex;
            flex-direction: column;
            height: 400px;
        }

        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 15px;
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .chat-message {
            display: flex;
            gap: 10px;
            max-width: 85%;
        }

        .chat-message.user {
            align-self: flex-end;
            flex-direction: row-reverse;
        }

        .chat-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
            flex-shrink: 0;
        }

        .chat-message.user .chat-avatar {
            background-color: rgba(255, 255, 255, 0.3);
        }

        .chat-bubble {
            background-color: rgba(255, 255, 255, 0.1);
            padding: 10px 15px;
            border-radius: 18px;
            color: white;
            position: relative;
        }

        .chat-message.ai .chat-bubble {
            border-top-left-radius: 4px;
        }

        .chat-message.user .chat-bubble {
            background-color: rgba(255, 255, 255, 0.2);
            border-top-right-radius: 4px;
        }

        .chat-name {
            font-size: 12px;
            font-weight: 600;
            margin-bottom: 4px;
            opacity: 0.8;
        }

        .chat-text {
            font-size: 14px;
            line-height: 1.4;
        }

        .chat-input-container {
            display: flex;
            padding: 10px 15px;
            background-color: rgba(0, 0, 0, 0.1);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .chat-input {
            flex: 1;
            background-color: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 20px;
            padding: 8px 15px;
            color: white;
            font-size: 14px;
        }

        .chat-input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .chat-input:focus {
            outline: none;
            background-color: rgba(255, 255, 255, 0.15);
        }

        .chat-send-btn {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            margin-left: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .chat-send-btn:hover {
            background-color: rgba(255, 255, 255, 0.3);
        }

        .chat-suggestions {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            padding: 10px 15px;
            background-color: rgba(0, 0, 0, 0.05);
        }

        .chat-suggestion-btn {
            background-color: rgba(255, 255, 255, 0.1);
            border: none;
            border-radius: 16px;
            padding: 6px 12px;
            color: white;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
            white-space: nowrap;
        }

        .chat-suggestion-btn:hover {
            background-color: rgba(255, 255, 255, 0.2);
        }

        /* Animation de chargement pour les réponses de l'IA */
        .typing-indicator {
            display: flex;
            align-items: center;
            gap: 4px;
            padding: 5px 0;
        }

        .typing-indicator span {
            width: 8px;
            height: 8px;
            background-color: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            display: inline-block;
            animation: typing 1.4s infinite ease-in-out both;
        }

        .typing-indicator span:nth-child(1) {
            animation-delay: 0s;
        }

        .typing-indicator span:nth-child(2) {
            animation-delay: 0.2s;
        }

        .typing-indicator span:nth-child(3) {
            animation-delay: 0.4s;
        }

        @keyframes typing {
            0%, 100% {
                transform: scale(0.7);
                opacity: 0.5;
            }
            50% {
                transform: scale(1);
                opacity: 1;
            }
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.5);
            }
            70% {
                box-shadow: 0 0 0 15px rgba(99, 102, 241, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
            }
        }

        /* Animation d'entrée pour la bulle */
        @media (prefers-reduced-motion: no-preference) {
            .faq-bubble-container {
                animation: slideInRight 0.5s ease-out 1s forwards;
                opacity: 0;
                transform: translateX(50px);
            }

            @keyframes slideInRight {
                to {
                    opacity: 1;
                    transform: translateX(0);
                }
            }
        }
    </style>

    <!-- Script pour la bulle FAQ et le mini-widget -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Initialiser les tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Fermer le widget quand on clique en dehors
            document.addEventListener('click', function(event) {
                var widget = document.getElementById('faqMiniWidget');
                var bubble = document.getElementById('faqBubble');

                if (widget.classList.contains('active') &&
                    !widget.contains(event.target) &&
                    !bubble.contains(event.target)) {
                    toggleFaqWidget(false);
                }
            });

            // Configurer l'entrée de chat
            var chatInput = document.getElementById('chatInput');
            if (chatInput) {
                chatInput.addEventListener('keypress', function(event) {
                    if (event.key === 'Enter') {
                        event.preventDefault();
                        sendMessage();
                    }
                });
            }
        });

        function toggleFaqWidget(show) {
            var widget = document.getElementById('faqMiniWidget');
            var bubble = document.getElementById('faqBubble');

            if (show) {
                widget.classList.add('active');
                // Masquer le tooltip quand le widget est ouvert
                var tooltip = bootstrap.Tooltip.getInstance(bubble);
                if (tooltip) {
                    tooltip.hide();
                }

                // Faire défiler jusqu'au bas du chat
                var chatMessages = document.getElementById('chatMessages');
                if (chatMessages) {
                    chatMessages.scrollTop = chatMessages.scrollHeight;
                }

                // Mettre le focus sur l'entrée de chat
                setTimeout(function() {
                    var chatTab = document.getElementById('chat-tab');
                    if (chatTab && chatTab.classList.contains('active')) {
                        var chatInput = document.getElementById('chatInput');
                        if (chatInput) {
                            chatInput.focus();
                        }
                    }
                }, 300);
            } else {
                widget.classList.remove('active');
            }
        }

        // Fonction pour envoyer un message
        function sendMessage() {
            var chatInput = document.getElementById('chatInput');
            var chatMessages = document.getElementById('chatMessages');

            if (!chatInput || !chatMessages || !chatInput.value.trim()) {
                return;
            }

            var userMessage = chatInput.value.trim();
            chatInput.value = '';

            // Ajouter le message de l'utilisateur
            addMessage('user', userMessage);

            // Afficher l'indicateur de frappe
            addTypingIndicator();

            // Simuler une réponse après un délai
            setTimeout(function() {
                // Supprimer l'indicateur de frappe
                removeTypingIndicator();

                // Générer et ajouter la réponse de l'IA
                var response = generateResponse(userMessage);
                addMessage('ai', response);
            }, 1500);
        }

        // Fonction pour utiliser une suggestion
        function useSuggestion(suggestion) {
            var chatInput = document.getElementById('chatInput');
            if (chatInput) {
                chatInput.value = suggestion;
                chatInput.focus();
            }
        }

        // Fonction pour ajouter un message au chat
        function addMessage(type, text) {
            var chatMessages = document.getElementById('chatMessages');
            if (!chatMessages) return;

            var messageDiv = document.createElement('div');
            messageDiv.className = 'chat-message ' + type;

            var avatarDiv = document.createElement('div');
            avatarDiv.className = 'chat-avatar';

            var iconElement = document.createElement('i');
            iconElement.className = type === 'ai' ? 'fas fa-robot' : 'fas fa-user';
            avatarDiv.appendChild(iconElement);

            var bubbleDiv = document.createElement('div');
            bubbleDiv.className = 'chat-bubble';

            var nameDiv = document.createElement('div');
            nameDiv.className = 'chat-name';
            nameDiv.textContent = type === 'ai' ? 'Miora' : 'Vous';

            var textDiv = document.createElement('div');
            textDiv.className = 'chat-text';
            textDiv.textContent = text;

            bubbleDiv.appendChild(nameDiv);
            bubbleDiv.appendChild(textDiv);

            messageDiv.appendChild(avatarDiv);
            messageDiv.appendChild(bubbleDiv);

            chatMessages.appendChild(messageDiv);

            // Faire défiler jusqu'au bas
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Fonction pour ajouter l'indicateur de frappe
        function addTypingIndicator() {
            var chatMessages = document.getElementById('chatMessages');
            if (!chatMessages) return;

            var typingDiv = document.createElement('div');
            typingDiv.className = 'chat-message ai typing-message';
            typingDiv.id = 'typingIndicator';

            var avatarDiv = document.createElement('div');
            avatarDiv.className = 'chat-avatar';

            var iconElement = document.createElement('i');
            iconElement.className = 'fas fa-robot';
            avatarDiv.appendChild(iconElement);

            var bubbleDiv = document.createElement('div');
            bubbleDiv.className = 'chat-bubble';

            var nameDiv = document.createElement('div');
            nameDiv.className = 'chat-name';
            nameDiv.textContent = 'Miora';

            var typingIndicator = document.createElement('div');
            typingIndicator.className = 'typing-indicator';

            for (var i = 0; i < 3; i++) {
                var dot = document.createElement('span');
                typingIndicator.appendChild(dot);
            }

            bubbleDiv.appendChild(nameDiv);
            bubbleDiv.appendChild(typingIndicator);

            typingDiv.appendChild(avatarDiv);
            typingDiv.appendChild(bubbleDiv);

            chatMessages.appendChild(typingDiv);

            // Faire défiler jusqu'au bas
            chatMessages.scrollTop = chatMessages.scrollHeight;
        }

        // Fonction pour supprimer l'indicateur de frappe
        function removeTypingIndicator() {
            var typingIndicator = document.getElementById('typingIndicator');
            if (typingIndicator) {
                typingIndicator.remove();
            }
        }

        // Fonction pour générer une réponse basée sur le message de l'utilisateur
        function generateResponse(message) {
            // Convertir le message en minuscules pour faciliter la correspondance
            var lowerMessage = message.toLowerCase();

            // Réponses prédéfinies basées sur des mots-clés
            if (lowerMessage.includes('bonjour') || lowerMessage.includes('salut') || lowerMessage.includes('hello')) {
                return "Bonjour ! Comment puis-je vous aider avec Planifeo aujourd'hui ?";
            }
            else if (lowerMessage.includes('compte') && (lowerMessage.includes('créer') || lowerMessage.includes('creer') || lowerMessage.includes('inscription'))) {
                return "Pour créer un compte, cliquez sur le bouton 'Inscription' en haut à droite de la page. Vous pourrez choisir entre 'Je suis un client' ou 'Je suis un prestataire' selon votre besoin. Remplissez ensuite le formulaire avec vos informations et validez.";
            }
            else if (lowerMessage.includes('prestataire') && (lowerMessage.includes('devenir') || lowerMessage.includes('être') || lowerMessage.includes('etre'))) {
                return "Pour devenir prestataire sur Planifeo, inscrivez-vous en choisissant l'option 'Je suis un prestataire', complétez votre profil professionnel avec vos services, tarifs et disponibilités. Votre profil sera ensuite visible par les clients potentiels.";
            }
            else if (lowerMessage.includes('contact') && lowerMessage.includes('prestataire')) {
                return "Pour contacter un prestataire, vous devez d'abord créer un compte client. Ensuite, naviguez vers le profil du prestataire qui vous intéresse et cliquez sur 'Contacter' ou 'Demander un devis'. Vous pourrez alors échanger des messages directement via notre plateforme.";
            }
            else if (lowerMessage.includes('réserv') || lowerMessage.includes('reserv') || lowerMessage.includes('book')) {
                return "Pour réserver un service, connectez-vous à votre compte client, trouvez le prestataire qui vous intéresse, demandez un devis en précisant vos besoins, puis acceptez le devis proposé. Vous pourrez ensuite procéder au paiement de l'acompte pour confirmer votre réservation.";
            }
            else if (lowerMessage.includes('paiement') || lowerMessage.includes('payment') || lowerMessage.includes('payer')) {
                return "Planifeo accepte plusieurs modes de paiement : cartes bancaires (Visa, Mastercard), Mobile Money (Orange Money, Airtel Money, MVola), virements bancaires et, dans certains cas, paiements en espèces directement au prestataire.";
            }
            else if (lowerMessage.includes('annul') || lowerMessage.includes('cancel')) {
                return "Les conditions d'annulation dépendent de chaque prestataire. En général, une annulation plus de 30 jours avant l'événement permet un remboursement partiel ou total de l'acompte. Pour une annulation entre 15 et 30 jours, l'acompte n'est généralement pas remboursable. Consultez les conditions spécifiques du prestataire avant de confirmer votre réservation.";
            }
            else if (lowerMessage.includes('planifeo')) {
                return "Planifeo est une plateforme qui met en relation les organisateurs d'événements avec des prestataires de services qualifiés. Notre mission est de simplifier l'organisation d'événements en offrant un accès facile à un réseau de professionnels fiables et talentueux.";
            }
            else if (lowerMessage.includes('événement') || lowerMessage.includes('evenement') || lowerMessage.includes('event')) {
                return "Planifeo vous aide à organiser tous types d'événements : mariages, anniversaires, conférences d'entreprise, séminaires, fêtes privées, événements corporatifs et bien plus encore !";
            }
            else if (lowerMessage.includes('merci') || lowerMessage.includes('thanks')) {
                return "Je vous en prie ! N'hésitez pas si vous avez d'autres questions sur Planifeo.";
            }
            else if (lowerMessage.includes('au revoir') || lowerMessage.includes('bye')) {
                return "Au revoir ! N'hésitez pas à revenir si vous avez d'autres questions. Bonne journée !";
            }
            else {
                return "Je suis désolée, je ne peux vous aider qu'avec des informations concernant la plateforme Planifeo. Pourriez-vous reformuler votre question concernant nos services, l'inscription, la réservation ou nos prestataires ?";
            }
        }
    </script>
</body>
</html>
