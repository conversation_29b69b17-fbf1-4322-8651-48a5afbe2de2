@extends('ClientsView.layouts.app')

@section('title', 'Comment ça marche')

@section('content')
<div class="container py-5">
    <div class="text-center mb-5">
        <h1 class="display-5 fw-bold mb-3">Comment ça marche</h1>
        <p class="lead text-muted">Découvrez comment Planifeo facilite l'organisation de vos événements</p>
    </div>

    <div class="row mb-5">
        <div class="col-lg-10 mx-auto">
            <ul class="nav nav-pills nav-justified mb-5" id="howItWorksTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="client-tab" data-bs-toggle="tab" data-bs-target="#client-tab-pane" type="button" role="tab" aria-controls="client-tab-pane" aria-selected="true">
                        <i class="fas fa-user me-2"></i>Pour les clients
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="provider-tab" data-bs-toggle="tab" data-bs-target="#provider-tab-pane" type="button" role="tab" aria-controls="provider-tab-pane" aria-selected="false">
                        <i class="fas fa-briefcase me-2"></i>Pour les prestataires
                    </button>
                </li>
            </ul>

            <div class="tab-content" id="howItWorksTabContent">
                <!-- Client Tab -->
                <div class="tab-pane fade show active" id="client-tab-pane" role="tabpanel" aria-labelledby="client-tab" tabindex="0">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-4 p-lg-5">
                            @if(!empty($clientSteps) && is_array($clientSteps))
                                <div class="row">
                                    @foreach($clientSteps as $index => $step)
                                        <div class="col-md-6 mb-4">
                                            <div class="d-flex">
                                                <div class="flex-shrink-0">
                                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white" style="width: 50px; height: 50px; font-size: 1.5rem;">
                                                        {{ $index + 1 }}
                                                    </div>
                                                </div>
                                                <div class="flex-grow-1 ms-3">
                                                    <h3 class="h5 fw-bold">{{ $step['title'] ?? '' }}</h3>
                                                    <p class="text-muted mb-0">{{ $step['description'] ?? '' }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="row">
                                    <div class="col-md-6 mb-4">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white" style="width: 50px; height: 50px; font-size: 1.5rem;">
                                                    1
                                                </div>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h3 class="h5 fw-bold">Créez un compte</h3>
                                                <p class="text-muted mb-0">Inscrivez-vous gratuitement sur notre plateforme en quelques minutes.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-4">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white" style="width: 50px; height: 50px; font-size: 1.5rem;">
                                                    2
                                                </div>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h3 class="h5 fw-bold">Recherchez des prestataires</h3>
                                                <p class="text-muted mb-0">Parcourez notre sélection de prestataires qualifiés et filtrez selon vos besoins.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-4">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white" style="width: 50px; height: 50px; font-size: 1.5rem;">
                                                    3
                                                </div>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h3 class="h5 fw-bold">Demandez des devis</h3>
                                                <p class="text-muted mb-0">Contactez les prestataires qui vous intéressent et demandez des devis personnalisés.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-4">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white" style="width: 50px; height: 50px; font-size: 1.5rem;">
                                                    4
                                                </div>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h3 class="h5 fw-bold">Réservez et payez</h3>
                                                <p class="text-muted mb-0">Confirmez votre réservation et effectuez le paiement en toute sécurité via notre plateforme.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <div class="text-center mt-4">
                                <button type="button" class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#clientRegisterModal">
                                    <i class="fas fa-user-plus me-2"></i>Devenir client
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Provider Tab -->
                <div class="tab-pane fade" id="provider-tab-pane" role="tabpanel" aria-labelledby="provider-tab" tabindex="0">
                    <div class="card border-0 shadow-sm">
                        <div class="card-body p-4 p-lg-5">
                            @if(!empty($providerSteps) && is_array($providerSteps))
                                <div class="row">
                                    @foreach($providerSteps as $index => $step)
                                        <div class="col-md-6 mb-4">
                                            <div class="d-flex">
                                                <div class="flex-shrink-0">
                                                    <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white" style="width: 50px; height: 50px; font-size: 1.5rem;">
                                                        {{ $index + 1 }}
                                                    </div>
                                                </div>
                                                <div class="flex-grow-1 ms-3">
                                                    <h3 class="h5 fw-bold">{{ $step['title'] ?? '' }}</h3>
                                                    <p class="text-muted mb-0">{{ $step['description'] ?? '' }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="row">
                                    <div class="col-md-6 mb-4">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white" style="width: 50px; height: 50px; font-size: 1.5rem;">
                                                    1
                                                </div>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h3 class="h5 fw-bold">Créez votre profil professionnel</h3>
                                                <p class="text-muted mb-0">Inscrivez-vous en tant que prestataire et créez un profil attrayant pour présenter vos services.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-4">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white" style="width: 50px; height: 50px; font-size: 1.5rem;">
                                                    2
                                                </div>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h3 class="h5 fw-bold">Ajoutez vos services</h3>
                                                <p class="text-muted mb-0">Détaillez vos services, tarifs et disponibilités pour attirer des clients potentiels.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-4">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white" style="width: 50px; height: 50px; font-size: 1.5rem;">
                                                    3
                                                </div>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h3 class="h5 fw-bold">Recevez des demandes</h3>
                                                <p class="text-muted mb-0">Les clients intéressés vous enverront des demandes de devis pour vos services.</p>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-4">
                                        <div class="d-flex">
                                            <div class="flex-shrink-0">
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white" style="width: 50px; height: 50px; font-size: 1.5rem;">
                                                    4
                                                </div>
                                            </div>
                                            <div class="flex-grow-1 ms-3">
                                                <h3 class="h5 fw-bold">Gérez vos réservations</h3>
                                                <p class="text-muted mb-0">Confirmez les réservations, communiquez avec vos clients et recevez vos paiements.</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            @endif

                            <div class="text-center mt-4">
                                <button type="button" class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#providerRegisterModal">
                                    <i class="fas fa-briefcase me-2"></i>Devenir prestataire
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
    @include('ClientsView.partials.how-it-works-auth-modals')
@endsection
