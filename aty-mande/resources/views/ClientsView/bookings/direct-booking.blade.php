@extends('ClientsView.layouts.dashboard')

@section('title', 'Réservation directe')

@section('header-title', 'Réservation directe')

@section('content')
    <div class="row">
        <div class="col-lg-8">
            <div class="dashboard-card mb-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar-plus me-2 text-primary"></i>
                        Réservation directe
                    </h5>
                </div>

                <form action="{{ route('client.direct-booking.store', ['providerId' => $provider->id, 'serviceId' => $service->id]) }}" method="POST">
                    @csrf

                    <div class="mb-4">
                        <div class="d-flex align-items-center mb-3">
                            <div class="provider-logo me-3">
                                <img src="{{ $provider->logo_url ? asset('storage/' . $provider->logo_url) : asset('images/default-logo.png') }}"
                                    alt="{{ $provider->business_name }}" class="rounded-circle" width="50" height="50">
                            </div>
                            <div>
                                <h5 class="mb-0">{{ $provider->business_name }}</h5>
                                <div class="mt-1">
                                    @foreach($provider->categories as $category)
                                        <span class="badge bg-primary me-1">{{ $category->name }}</span>
                                    @endforeach
                                </div>
                            </div>
                        </div>

                        <div class="card bg-light mb-3">
                            <div class="card-body">
                                <h6 class="card-title">{{ $service->title }}</h6>
                                <p class="card-text small">{{ Str::limit($service->description, 150) }}</p>
                                <div class="d-flex justify-content-between align-items-center">
                                    <span class="text-primary fw-bold">{{ $service->getDisplayPrice() }}</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Informations de l'événement</h6>

                        <div class="mb-3">
                            <label for="event_id" class="form-label">Associer à un événement existant (optionnel)</label>
                            <select class="form-select @error('event_id') is-invalid @enderror" id="event_id" name="event_id">
                                <option value="">Aucun événement</option>
                                @foreach($events as $event)
                                    <option value="{{ $event->id }}" {{ old('event_id') == $event->id ? 'selected' : '' }}>
                                        {{ $event->title }} ({{ $event->date->format('d/m/Y') }})
                                    </option>
                                @endforeach
                            </select>
                            @error('event_id')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">
                                Si vous souhaitez associer cette réservation à un événement existant, sélectionnez-le ici.
                                Sinon, laissez ce champ vide.
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="title" class="form-label">Titre de la réservation <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('title') is-invalid @enderror" id="title" name="title" value="{{ old('title', $service->title) }}" required>
                            @error('title')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="description" class="form-label">Description</label>
                            <textarea class="form-control @error('description') is-invalid @enderror" id="description" name="description" rows="3">{{ old('description') }}</textarea>
                            @error('description')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label for="event_date" class="form-label">Date de l'événement <span class="text-danger">*</span></label>
                                <input type="date" class="form-control @error('event_date') is-invalid @enderror" id="event_date" name="event_date" value="{{ old('event_date') }}" required>
                                @error('event_date')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-3">
                                <label for="event_start_time" class="form-label">Heure de début <span class="text-danger">*</span></label>
                                <input type="time" class="form-control @error('event_start_time') is-invalid @enderror" id="event_start_time" name="event_start_time" value="{{ old('event_start_time') }}" required>
                                @error('event_start_time')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                            <div class="col-md-3">
                                <label for="event_end_time" class="form-label">Heure de fin <span class="text-danger">*</span></label>
                                <input type="time" class="form-control @error('event_end_time') is-invalid @enderror" id="event_end_time" name="event_end_time" value="{{ old('event_end_time') }}" required>
                                @error('event_end_time')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>

                        <div class="mb-3">
                            <label for="event_location" class="form-label">Lieu de l'événement <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('event_location') is-invalid @enderror" id="event_location" name="event_location" value="{{ old('event_location') }}" required>
                            @error('event_location')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="guest_count" class="form-label">Nombre d'invités <span class="text-danger">*</span></label>
                            <input type="number" class="form-control @error('guest_count') is-invalid @enderror" id="guest_count" name="guest_count" value="{{ old('guest_count', 1) }}" min="1" required>
                            @error('guest_count')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="mb-3">
                            <label for="special_requests" class="form-label">Demandes spéciales</label>
                            <textarea class="form-control @error('special_requests') is-invalid @enderror" id="special_requests" name="special_requests" rows="3">{{ old('special_requests') }}</textarea>
                            @error('special_requests')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ route('providers.service', ['providerId' => $provider->id, 'serviceId' => $service->id]) }}" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Retour
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-calendar-check me-2"></i>Confirmer la réservation
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="dashboard-card mb-4">
                <h5 class="card-title mb-3">
                    <i class="fas fa-info-circle me-2 text-primary"></i>
                    Informations sur la réservation directe
                </h5>
                <div class="alert alert-info">
                    <h6 class="alert-heading"><i class="fas fa-lightbulb me-2"></i>Comment ça fonctionne ?</h6>
                    <p class="mb-0">
                        La réservation directe vous permet de réserver immédiatement un service sans passer par le processus de demande de devis.
                        Votre réservation sera soumise au prestataire qui pourra la confirmer ou la refuser.
                    </p>
                </div>

                <div class="card bg-light mb-3">
                    <div class="card-body">
                        <h6 class="card-title">Processus de réservation</h6>
                        <ol class="mb-0">
                            <li>Remplissez le formulaire avec les détails de votre événement</li>
                            <li>Soumettez votre réservation</li>
                            <li>Le prestataire recevra votre demande et la confirmera</li>
                            <li>Une fois confirmée, vous pourrez effectuer le paiement</li>
                        </ol>
                    </div>
                </div>

                <div class="card bg-light">
                    <div class="card-body">
                        <h6 class="card-title">Politique d'annulation</h6>
                        <p class="small mb-0">
                            Vous pouvez annuler votre réservation jusqu'à 48 heures avant la date de l'événement.
                            Après cette période, des frais d'annulation peuvent s'appliquer.
                        </p>
                    </div>
                </div>
            </div>

            <div class="dashboard-card mb-4">
                <h5 class="card-title mb-3">
                    <i class="fas fa-user me-2 text-primary"></i>
                    Coordonnées du prestataire
                </h5>
                <ul class="list-unstyled">
                    <li class="mb-2"><i class="fas fa-building me-2"></i> {{ $provider->business_name }}</li>
                    <li class="mb-2"><i class="fas fa-map-marker-alt me-2"></i> {{ $provider->business_address }}</li>
                    <li class="mb-2"><i class="fas fa-phone me-2"></i> {{ $provider->business_phone }}</li>
                    <li class="mb-2"><i class="fas fa-envelope me-2"></i> {{ $provider->business_email }}</li>
                </ul>
            </div>
        </div>
    </div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Mettre à jour les champs en fonction de l'événement sélectionné
        const eventSelect = document.getElementById('event_id');
        
        eventSelect.addEventListener('change', function() {
            if (this.value) {
                // Récupérer les données de l'événement sélectionné
                const selectedEvent = {
                    @foreach($events as $event)
                    {{ $event->id }}: {
                        title: "{{ $event->title }}",
                        date: "{{ $event->date->format('Y-m-d') }}",
                        location: "{{ $event->location }}",
                        guest_count: {{ $event->guest_count }}
                    },
                    @endforeach
                };
                
                // Mettre à jour les champs du formulaire
                if (selectedEvent[this.value]) {
                    const event = selectedEvent[this.value];
                    document.getElementById('title').value = event.title + ' - ' + '{{ $service->title }}';
                    document.getElementById('event_date').value = event.date;
                    document.getElementById('event_location').value = event.location;
                    document.getElementById('guest_count').value = event.guest_count;
                }
            }
        });
    });
</script>
@endpush
