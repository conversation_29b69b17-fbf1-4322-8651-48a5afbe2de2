@extends('ClientsView.layouts.app')

@section('title', 'Contact - Planifeo')

@section('content')
<section class="page-header bg-primary text-white">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="display-4">Contactez-nous</h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{{ route('home') }}" class="text-white">Accueil</a></li>
                        <li class="breadcrumb-item active text-white" aria-current="page">Contact</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>
</section>

<section class="contact-section py-5">
    <div class="container">
        @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        <div class="row">
            <div class="col-lg-6 mb-4 mb-lg-0">
                <div class="contact-info bg-light p-4 rounded shadow-sm h-100">
                    <h2 class="h3 mb-4">Informations de contact</h2>
                    
                    <div class="d-flex mb-4">
                        <div class="icon-box me-3">
                            <i class="fas fa-map-marker-alt text-primary fa-2x"></i>
                        </div>
                        <div>
                            <h5>Notre adresse</h5>
                            <p class="mb-0">{{ $contactAddress ?? 'Antananarivo, Madagascar' }}</p>
                        </div>
                    </div>
                    
                    <div class="d-flex mb-4">
                        <div class="icon-box me-3">
                            <i class="fas fa-phone-alt text-primary fa-2x"></i>
                        </div>
                        <div>
                            <h5>Téléphone</h5>
                            <p class="mb-0">{{ $contactPhone ?? '+261 34 12 345 67' }}</p>
                        </div>
                    </div>
                    
                    <div class="d-flex mb-4">
                        <div class="icon-box me-3">
                            <i class="fas fa-envelope text-primary fa-2x"></i>
                        </div>
                        <div>
                            <h5>Email</h5>
                            <p class="mb-0">{{ $contactEmail ?? '<EMAIL>' }}</p>
                        </div>
                    </div>
                    
                    <div class="d-flex">
                        <div class="icon-box me-3">
                            <i class="fas fa-clock text-primary fa-2x"></i>
                        </div>
                        <div>
                            <h5>Heures d'ouverture</h5>
                            <p class="mb-0">Lundi - Vendredi: 8h00 - 17h00<br>Samedi: 9h00 - 13h00</p>
                        </div>
                    </div>
                    
                    <div class="social-links mt-4">
                        <h5>Suivez-nous</h5>
                        <div class="d-flex">
                            <a href="#" class="me-2 btn btn-outline-primary"><i class="fab fa-facebook-f"></i></a>
                            <a href="#" class="me-2 btn btn-outline-primary"><i class="fab fa-twitter"></i></a>
                            <a href="#" class="me-2 btn btn-outline-primary"><i class="fab fa-instagram"></i></a>
                            <a href="#" class="btn btn-outline-primary"><i class="fab fa-linkedin-in"></i></a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-6">
                <div class="contact-form bg-white p-4 rounded shadow-sm">
                    <h2 class="h3 mb-4">Envoyez-nous un message</h2>
                    
                    <form action="{{ route('contact.store') }}" method="POST">
                        @csrf
                        
                        <div class="mb-3">
                            <label for="name" class="form-label">Nom complet <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('name') is-invalid @enderror" id="name" name="name" value="{{ old('name') }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                            <input type="email" class="form-control @error('email') is-invalid @enderror" id="email" name="email" value="{{ old('email') }}" required>
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="mb-3">
                            <label for="phone" class="form-label">Téléphone</label>
                            <input type="tel" class="form-control @error('phone') is-invalid @enderror" id="phone" name="phone" value="{{ old('phone') }}">
                            @error('phone')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="mb-3">
                            <label for="subject" class="form-label">Sujet <span class="text-danger">*</span></label>
                            <input type="text" class="form-control @error('subject') is-invalid @enderror" id="subject" name="subject" value="{{ old('subject') }}" required>
                            @error('subject')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="mb-3">
                            <label for="message" class="form-label">Message <span class="text-danger">*</span></label>
                            <textarea class="form-control @error('message') is-invalid @enderror" id="message" name="message" rows="5" required>{{ old('message') }}</textarea>
                            @error('message')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-paper-plane me-2"></i>Envoyer le message
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="map-section py-5 bg-light">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="h3 text-center mb-4">Notre localisation</h2>
                <div class="map-container rounded shadow-sm">
                    <iframe src="https://www.google.com/maps/embed?pb=!1m18!1m12!1m3!1d234700.30480046062!2d47.41891146566354!3d-18.87975228051198!2m3!1f0!2f0!3f0!3m2!1i1024!2i768!4f13.1!3m3!1m2!1s0x21f07de34f1f4eb3%3A0xdf110608bcc082f9!2sAntananarivo%2C%20Madagascar!5e0!3m2!1sfr!2sfr!4v1652345678901!5m2!1sfr!2sfr" width="100%" height="450" style="border:0;" allowfullscreen="" loading="lazy" referrerpolicy="no-referrer-when-downgrade"></iframe>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
