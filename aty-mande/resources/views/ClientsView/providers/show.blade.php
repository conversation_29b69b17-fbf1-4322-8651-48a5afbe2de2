@extends('ClientsView.layouts.app')

@section('title', $provider->business_name . ' - Prestataire de services')

@section('meta_description', 'Découvrez ' . $provider->business_name . ', prestataire de services pour vos événements. ' . Str::limit(strip_tags($provider->description), 150))

@section('content')
<div class="container py-5">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('home') }}">Accueil</a></li>
            <li class="breadcrumb-item"><a href="{{ route('search') }}">Prestataires</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ $provider->business_name }}</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Provider Info -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body">
                    <div class="d-flex align-items-center mb-3">
                        <div class="provider-logo me-3">
                            <img src="{{ $provider->logo_url ? asset('storage/' . $provider->logo_url) : asset('images/default-logo.png') }}"
                                alt="{{ $provider->business_name }}" class="rounded-circle" width="80" height="80">
                        </div>
                        <div>
                            <h1 class="h3 mb-1">{{ $provider->business_name }}</h1>
                            <div class="d-flex align-items-center">
                                <div class="rating me-2">
                                    @for ($i = 1; $i <= 5; $i++)
                                        @if ($i <= round($averageRating))
                                            <i class="fas fa-star text-warning"></i>
                                        @else
                                            <i class="far fa-star text-warning"></i>
                                        @endif
                                    @endfor
                                </div>
                                <span class="text-muted">{{ number_format($averageRating, 1) }} ({{ $reviewsCount }} avis)</span>
                            </div>
                            <div class="mt-1">
                                @foreach($provider->categories as $category)
                                    <span class="badge bg-primary me-1">{{ $category->name }}</span>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    <div class="provider-details">
                        <div class="mb-3">
                            <h5>À propos</h5>
                            <div class="provider-description">
                                {!! nl2br(e($provider->description)) !!}
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-md-6">
                                <h5>Coordonnées</h5>
                                <ul class="list-unstyled">
                                    <li><i class="fas fa-map-marker-alt me-2"></i> {{ $provider->business_address }}</li>
                                    <li><i class="fas fa-phone me-2"></i> {{ $provider->business_phone }}</li>
                                    <li><i class="fas fa-envelope me-2"></i> {{ $provider->business_email }}</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h5>Zones de service</h5>
                                <ul class="list-unstyled">
                                    @php
                                        $serviceAreas = $provider->getServiceAreasArray();
                                    @endphp

                                    @if(count($serviceAreas) > 0)
                                        @foreach($serviceAreas as $area)
                                            <li><i class="fas fa-check-circle me-2 text-success"></i> {{ $area }}</li>
                                        @endforeach
                                    @else
                                        <li>Aucune zone spécifiée</li>
                                    @endif
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Services -->
            @if($provider->services->count() > 0)
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Services proposés</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($provider->services as $service)
                        <div class="col-md-6 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h5 class="card-title">{{ $service->title }}</h5>

                                    @php
                                        $details = $service->details ?? [];
                                        $categoryName = $service->category ? $service->category->name : '';
                                    @endphp

                                    <!-- Aperçu des détails spécifiques à chaque catégorie -->
                                    <div class="service-highlights mb-2">
                                        @if($categoryName == 'Traiteur')
                                            @if(isset($details['menu_items']) && is_array($details['menu_items']) && count($details['menu_items']) > 0)
                                                <span class="badge bg-info me-1">{{ count($details['menu_items']) }} plats</span>
                                            @endif
                                            @if(isset($details['includes_service']) && $details['includes_service'])
                                                <span class="badge bg-info me-1">Service inclus</span>
                                            @endif
                                        @elseif($categoryName == 'Musique et Animation')
                                            @if(isset($details['artist_type']))
                                                <span class="badge bg-info me-1">{{ $details['artist_type'] }}</span>
                                            @endif
                                            @if(isset($details['duration']))
                                                <span class="badge bg-info me-1">{{ $details['duration'] }}h</span>
                                            @endif
                                        @elseif($categoryName == 'Transport')
                                            @if(isset($details['vehicle_type']))
                                                <span class="badge bg-info me-1">{{ $details['vehicle_type'] }}</span>
                                            @endif
                                            @if(isset($details['seats']))
                                                <span class="badge bg-info me-1">{{ $details['seats'] }} places</span>
                                            @endif
                                        @elseif($categoryName == 'Lieu de Réception')
                                            @if(isset($details['capacity']))
                                                <span class="badge bg-info me-1">{{ $details['capacity'] }} pers.</span>
                                            @endif
                                            @if(isset($details['indoor']) && $details['indoor'] && isset($details['outdoor']) && $details['outdoor'])
                                                <span class="badge bg-info me-1">Intérieur/Extérieur</span>
                                            @elseif(isset($details['indoor']) && $details['indoor'])
                                                <span class="badge bg-info me-1">Intérieur</span>
                                            @elseif(isset($details['outdoor']) && $details['outdoor'])
                                                <span class="badge bg-info me-1">Extérieur</span>
                                            @endif
                                        @endif
                                    </div>

                                    <p class="card-text small">{{ Str::limit($service->description, 80) }}</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="text-primary fw-bold">{{ $service->getDisplayPrice() }}
                                        </span>
                                        <a href="{{ route('providers.service', ['providerId' => $provider->id, 'serviceId' => $service->id]) }}" class="btn btn-sm btn-outline-primary">Détails</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif

            <!-- Gallery -->
            @if($featuredGallery->count() > 0)
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Galerie</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        @foreach($featuredGallery as $item)
                        <div class="col-md-4">
                            <a href="{{ asset('storage/' . $item->image_path) }}" data-lightbox="provider-gallery" data-title="{{ $item->title }}">
                                <img src="{{ asset('storage/' . $item->image_path) }}" alt="{{ $item->title }}" class="img-fluid rounded">
                            </a>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif

            <!-- Reviews -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Avis clients ({{ $reviewsCount }})</h5>
                    <a href="{{ route('providers.reviews', $provider->id) }}" class="btn btn-sm btn-outline-primary">Voir tous les avis</a>
                </div>
                <div class="card-body">
                    @if($provider->reviews->count() > 0)
                        @foreach($provider->reviews->take(3) as $review)
                        <div class="review mb-3 pb-3 border-bottom">
                            <div class="d-flex justify-content-between align-items-start">
                                <div>
                                    <h6 class="mb-1">{{ $review->client->user->name }}</h6>
                                    <div class="rating mb-1">
                                        @for ($i = 1; $i <= 5; $i++)
                                            @if ($i <= $review->rating)
                                                <i class="fas fa-star text-warning"></i>
                                            @else
                                                <i class="far fa-star text-warning"></i>
                                            @endif
                                        @endfor
                                    </div>
                                </div>
                                <small class="text-muted">{{ $review->created_at->format('d/m/Y') }}</small>
                            </div>
                            <p class="mb-0">{{ $review->comment }}</p>
                        </div>
                        @endforeach
                    @else
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>Aucun avis pour le moment.
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Actions -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        @if($provider->services->count() > 0)
                            <div class="dropdown mb-2">
                                <button class="btn btn-primary dropdown-toggle w-100" type="button" id="bookingDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                    <i class="fas fa-calendar-check me-1"></i>Réserver un service
                                </button>
                                <ul class="dropdown-menu w-100" aria-labelledby="bookingDropdown">
                                    @foreach($provider->services as $service)
                                        <li><a class="dropdown-item" href="{{ route('providers.book', ['id' => $provider->id, 'serviceId' => $service->id]) }}">{{ $service->title }}</a></li>
                                    @endforeach
                                </ul>
                            </div>
                        @else
                            <button class="btn btn-primary mb-2 w-100" disabled>
                                <i class="fas fa-calendar-check me-1"></i>Aucun service disponible
                            </button>
                        @endif
                        <a href="tel:{{ $provider->business_phone }}" class="btn btn-outline-primary">
                            <i class="fas fa-phone me-1"></i>Appeler
                        </a>
                        <a href="mailto:{{ $provider->business_email }}" class="btn btn-outline-primary">
                            <i class="fas fa-envelope me-1"></i>Envoyer un email
                        </a>
                    </div>
                </div>
            </div>

            <!-- Similar Providers -->
            @if($similarProviders->count() > 0)
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Prestataires similaires</h5>
                </div>
                <div class="card-body">
                    @foreach($similarProviders as $similarProvider)
                    <div class="similar-provider mb-3 pb-3 border-bottom">
                        <div class="d-flex">
                            <div class="flex-shrink-0 me-3">
                                @if($similarProvider->gallery->count() > 0)
                                    <img src="{{ asset('storage/' . $similarProvider->gallery->first()->image_path) }}" alt="{{ $similarProvider->business_name }}" class="rounded" width="60" height="60">
                                @else
                                    <img src="{{ asset('images/default-image.png') }}" alt="{{ $similarProvider->business_name }}" class="rounded" width="60" height="60">
                                @endif
                            </div>
                            <div>
                                <h6 class="mb-1">{{ $similarProvider->business_name }}</h6>
                                <div class="rating mb-1">
                                    @for ($i = 1; $i <= 5; $i++)
                                        @if ($i <= round($similarProvider->average_rating))
                                            <i class="fas fa-star text-warning"></i>
                                        @else
                                            <i class="far fa-star text-warning"></i>
                                        @endif
                                    @endfor
                                    <small class="text-muted ms-1">({{ $similarProvider->reviews_count }})</small>
                                </div>
                                <a href="{{ route('providers.show', $similarProvider->id) }}" class="btn btn-sm btn-outline-primary">Voir le profil</a>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
            @endif
        </div>
    </div>
</div>
@endsection

@push('scripts')
@if(session('open_login_modal'))
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Ouvrir la modal de connexion automatiquement
        var loginModal = new bootstrap.Modal(document.getElementById('loginModal'));
        loginModal.show();

        // Stocker l'URL de redirection dans une variable de session côté client
        if ('{{ session('redirect_after_login') }}') {
            localStorage.setItem('redirect_after_login', '{{ session('redirect_after_login') }}');
        }
    });
</script>
@endif
@endpush
