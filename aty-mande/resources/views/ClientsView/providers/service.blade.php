@extends('ClientsView.layouts.app')

@section('title', $service->title . ' - ' . $provider->business_name)

@section('meta_description', 'Découvrez le service ' . $service->title . ' proposé par ' . $provider->business_name . '. ' . Str::limit(strip_tags($service->description), 150))

@section('content')
<div class="container py-5">
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{{ route('home') }}">Accueil</a></li>
            <li class="breadcrumb-item"><a href="{{ route('search') }}">Prestataires</a></li>
            <li class="breadcrumb-item"><a href="{{ route('providers.show', $provider->id) }}">{{ $provider->business_name }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ $service->title }}</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Service Info -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-body">
                    <h1 class="h3 mb-3">{{ $service->title }}</h1>

                    <div class="d-flex align-items-center mb-3">
                        <div class="provider-logo me-3">
                            <img src="{{ $provider->logo_url ? asset('storage/' . $provider->logo_url) : asset('images/default-logo.png') }}"
                                alt="{{ $provider->business_name }}" class="rounded-circle" width="50" height="50">
                        </div>
                        <div>
                            <h5 class="mb-0">{{ $provider->business_name }}</h5>
                            <div class="mt-1">
                                @foreach($provider->categories as $category)
                                    <span class="badge bg-primary me-1">{{ $category->name }}</span>
                                @endforeach
                            </div>
                        </div>
                    </div>

                    <div class="service-price mb-3">
                        <h5>Prix</h5>
                        <div class="d-flex align-items-center">
                            <span class="h4 text-primary mb-0">{{ $service->getDisplayPrice() }}</span>
                        </div>
                    </div>

                    <div class="service-description mb-3">
                        <h5>Description</h5>
                        <div>
                            {!! nl2br(e($service->description)) !!}
                        </div>
                    </div>

                    <!-- Détails spécifiques à chaque catégorie de service -->
                    @php
                        $details = $service->details ?? [];
                        $categoryName = $service->category ? $service->category->name : '';
                    @endphp

                    <!-- Service de Traiteur -->
                    @if($categoryName == 'Traiteur')
                    <div class="service-details mb-4">
                        <h5>Détails du service de traiteur</h5>
                        <div class="card border-0 bg-light">
                            <div class="card-body">
                                @if(isset($details['base_price']))
                                <div class="mb-3">
                                    <h6 class="mb-2">Frais de service de base</h6>
                                    <p class="mb-0">{{ $service->price > 0 ? number_format($service->price, 0, ',', ' ') . ' Ar' : 'Inclus dans le prix des plats' }}</p>
                                </div>
                                @endif

                                @if(isset($details['menu_items']) && is_array($details['menu_items']) && count($details['menu_items']) > 0)
                                <div class="mb-3">
                                    <h6 class="mb-2">Menu proposé</h6>
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>Plat</th>
                                                    <th>Type</th>
                                                    <th>Description</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach($details['menu_items'] as $item)
                                                <tr>
                                                    <td>{{ $item['name'] ?? '' }}</td>
                                                    <td><span class="badge bg-secondary">{{ $item['type'] ?? '' }}</span></td>
                                                    <td>{{ $item['description'] ?? '' }}</td>
                                                </tr>
                                                @endforeach
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                @endif

                                @if(isset($details['min_guests']))
                                <div class="mb-2">
                                    <strong>Nombre minimum d'invités :</strong> {{ $details['min_guests'] }} personnes
                                </div>
                                @endif

                                @if(isset($details['max_guests']))
                                <div class="mb-2">
                                    <strong>Nombre maximum d'invités :</strong> {{ $details['max_guests'] }} personnes
                                </div>
                                @endif

                                @if(isset($details['includes_service']) && $details['includes_service'])
                                <div class="mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i> Service à table inclus
                                </div>
                                @endif

                                @if(isset($details['includes_setup']) && $details['includes_setup'])
                                <div class="mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i> Installation et nettoyage inclus
                                </div>
                                @endif

                                @if(isset($details['includes_tableware']) && $details['includes_tableware'])
                                <div class="mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i> Vaisselle et couverts inclus
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Service de Musique et Animation -->
                    @if($categoryName == 'Musique et Animation')
                    <div class="service-details mb-4">
                        <h5>Détails du service de musique et animation</h5>
                        <div class="card border-0 bg-light">
                            <div class="card-body">
                                @if(isset($details['music_type']))
                                <div class="mb-2">
                                    <strong>Type de musique :</strong> {{ $details['music_type'] }}
                                </div>
                                @endif

                                @if(isset($details['artist_type']))
                                <div class="mb-2">
                                    <strong>Type d'artiste :</strong> {{ $details['artist_type'] }}
                                </div>
                                @endif

                                @if(isset($details['duration']))
                                <div class="mb-2">
                                    <strong>Durée de la prestation :</strong> {{ $details['duration'] }} heures
                                </div>
                                @endif

                                @if(isset($details['equipment_included']) && $details['equipment_included'])
                                <div class="mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i> Équipement sonore inclus
                                </div>
                                @endif

                                @if(isset($details['lighting_included']) && $details['lighting_included'])
                                <div class="mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i> Éclairage inclus
                                </div>
                                @endif

                                @if(isset($details['repertoire']))
                                <div class="mb-3">
                                    <h6 class="mb-2">Répertoire</h6>
                                    <p>{{ $details['repertoire'] }}</p>
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Service de Transport -->
                    @if($categoryName == 'Transport')
                    <div class="service-details mb-4">
                        <h5>Détails du service de transport</h5>
                        <div class="card border-0 bg-light">
                            <div class="card-body">
                                @if(isset($details['vehicle_type']))
                                <div class="mb-2">
                                    <strong>Type de véhicule :</strong> {{ $details['vehicle_type'] }}
                                </div>
                                @endif

                                @if(isset($details['seats']))
                                <div class="mb-2">
                                    <strong>Nombre de places :</strong> {{ $details['seats'] }}
                                </div>
                                @endif

                                @if(isset($details['pricing_model']))
                                <div class="mb-2">
                                    <strong>Modèle de tarification :</strong>
                                    {{ $details['pricing_model'] == 'hourly' ? 'Tarif horaire' : 'Tarif par trajet' }}
                                </div>
                                @endif

                                @if(isset($details['includes_fuel']) && $details['includes_fuel'])
                                <div class="mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i> Carburant inclus
                                </div>
                                @endif

                                @if(isset($details['includes_decoration']) && $details['includes_decoration'])
                                <div class="mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i> Décoration du véhicule incluse
                                </div>
                                @endif

                                @if(isset($details['is_convoy_lead']) && $details['is_convoy_lead'])
                                <div class="mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i> Véhicule de tête de cortège
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Service de Lieu de Réception -->
                    @if($categoryName == 'Lieu de Réception')
                    <div class="service-details mb-4">
                        <h5>Détails du lieu de réception</h5>
                        <div class="card border-0 bg-light">
                            <div class="card-body">
                                @if(isset($details['capacity']))
                                <div class="mb-2">
                                    <strong>Capacité :</strong> {{ $details['capacity'] }} personnes
                                </div>
                                @endif

                                @if(isset($details['area']))
                                <div class="mb-2">
                                    <strong>Superficie :</strong> {{ $details['area'] }} m²
                                </div>
                                @endif

                                @if(isset($details['indoor']) && $details['indoor'])
                                <div class="mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i> Espace intérieur
                                </div>
                                @endif

                                @if(isset($details['outdoor']) && $details['outdoor'])
                                <div class="mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i> Espace extérieur
                                </div>
                                @endif

                                @if(isset($details['parking']) && $details['parking'])
                                <div class="mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i> Parking disponible
                                </div>
                                @endif

                                @if(isset($details['includes_tables']) && $details['includes_tables'])
                                <div class="mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i> Tables et chaises incluses
                                </div>
                                @endif

                                @if(isset($details['includes_sound']) && $details['includes_sound'])
                                <div class="mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i> Système sonore inclus
                                </div>
                                @endif

                                @if(isset($details['includes_lighting']) && $details['includes_lighting'])
                                <div class="mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i> Éclairage inclus
                                </div>
                                @endif

                                @if(isset($details['includes_catering']) && $details['includes_catering'])
                                <div class="mb-2">
                                    <i class="fas fa-check-circle text-success me-2"></i> Service de restauration disponible
                                </div>
                                @endif
                            </div>
                        </div>
                    </div>
                    @endif

                    <!-- Caractéristiques générales -->
                    @if($service->features)
                    <div class="service-features mb-3">
                        <h5>Caractéristiques supplémentaires</h5>
                        <ul class="list-group list-group-flush">
                            @foreach(json_decode($service->features) as $feature)
                                <li class="list-group-item bg-transparent px-0">
                                    <i class="fas fa-check-circle text-success me-2"></i> {{ $feature }}
                                </li>
                            @endforeach
                        </ul>
                    </div>
                    @endif
                </div>
            </div>

            <!-- Gallery -->
            @if($serviceGallery->count() > 0)
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Galerie</h5>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        @foreach($serviceGallery as $item)
                        <div class="col-md-4">
                            <a href="{{ asset('storage/' . $item->image_path) }}" data-lightbox="service-gallery" data-title="{{ $item->title }}">
                                <img src="{{ asset('storage/' . $item->image_path) }}" alt="{{ $item->title }}" class="img-fluid rounded">
                            </a>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif

            <!-- Other Services -->
            @if($otherServices->count() > 0)
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Autres services de ce prestataire</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        @foreach($otherServices as $otherService)
                        <div class="col-md-4 mb-3">
                            <div class="card h-100">
                                <div class="card-body">
                                    <h6 class="card-title">{{ $otherService->title }}</h6>
                                    <p class="card-text small">{{ Str::limit($otherService->description, 80) }}</p>
                                    <div class="d-flex justify-content-between align-items-center">
                                        <span class="text-primary fw-bold">{{ $otherService->getDisplayPrice() }}
                                        </span>
                                        <a href="{{ route('providers.service', ['providerId' => $provider->id, 'serviceId' => $otherService->id]) }}" class="btn btn-sm btn-outline-primary">Détails</a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        @endforeach
                    </div>
                </div>
            </div>
            @endif
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Actions -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Actions</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="{{ route('providers.book', ['id' => $provider->id, 'serviceId' => $service->id]) }}" class="btn btn-success">
                            <i class="fas fa-calendar-check me-1"></i>Réserver directement
                        </a>
                        <a href="{{ route('client.events') }}" class="btn btn-primary">
                            <i class="fas fa-calendar-alt me-1"></i>Ajouter à mon événement
                        </a>
                        <a href="{{ route('providers.show', $provider->id) }}" class="btn btn-outline-primary">
                            <i class="fas fa-user me-1"></i>Voir le profil du prestataire
                        </a>
                        <a href="tel:{{ $provider->business_phone }}" class="btn btn-outline-primary">
                            <i class="fas fa-phone me-1"></i>Appeler
                        </a>
                        <a href="mailto:{{ $provider->business_email }}" class="btn btn-outline-primary">
                            <i class="fas fa-envelope me-1"></i>Envoyer un email
                        </a>
                    </div>
                </div>
            </div>

            <!-- Provider Info -->
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white">
                    <h5 class="mb-0">Coordonnées</h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2"><i class="fas fa-map-marker-alt me-2"></i> {{ $provider->business_address }}</li>
                        <li class="mb-2"><i class="fas fa-phone me-2"></i> {{ $provider->business_phone }}</li>
                        <li class="mb-2"><i class="fas fa-envelope me-2"></i> {{ $provider->business_email }}</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
