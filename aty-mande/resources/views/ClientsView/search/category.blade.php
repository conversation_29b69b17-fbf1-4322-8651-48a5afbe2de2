@extends('ClientsView.layouts.app')

@section('title', $category->name . ' - Prestataires de ' . $category->name)

@section('content')
<section class="search-header py-5 bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">Prestataires de {{ $category->name }}</h1>
                <p class="lead mb-0">Trouvez les meilleurs prestataires spécialisés en {{ $category->name }}</p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="d-inline-block bg-white text-primary p-3 rounded-circle">
                    <i class="fas {{ $category->icon }} fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Sidebar Filters -->
            <div class="col-lg-3 mb-4 mb-lg-0">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <h5 class="card-title mb-4">Filtres</h5>

                        <form action="{{ route('categories.show', $category->slug) }}" method="GET">

                            <div class="mb-4">
                                <label class="form-label fw-bold">Catégories</label>
                                <div class="list-group list-group-flush">
                                    @foreach($categories as $cat)
                                        <a href="{{ route('categories.show', $cat->slug) }}" class="list-group-item list-group-item-action border-0 ps-0 {{ $cat->id == $category->id ? 'active bg-primary text-white' : '' }}">
                                            <i class="fas {{ $cat->icon }} me-2 {{ $cat->id == $category->id ? 'text-white' : 'text-primary' }}"></i>
                                            {{ $cat->name }}
                                        </a>
                                    @endforeach
                                </div>
                            </div>

                            <div class="mb-4">
                                <label class="form-label fw-bold">Types d'événements</label>
                                <div class="list-group list-group-flush">
                                    @foreach($eventTypes as $type)
                                        <a href="{{ route('event-types.show', $type->slug) }}" class="list-group-item list-group-item-action border-0 ps-0">
                                            <i class="fas {{ $type->icon }} me-2 text-primary"></i>
                                            {{ $type->name }}
                                        </a>
                                    @endforeach
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="location" class="form-label fw-bold">Lieu</label>
                                <select class="form-select" id="location" name="location_id">
                                    <option value="">Tous les lieux</option>
                                    @foreach($locations as $location)
                                        <option value="{{ $location->id }}">{{ $location->name }}</option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="mb-4">
                                <label class="form-label fw-bold">Budget</label>
                                <div class="row g-2">
                                    <div class="col-6">
                                        <input type="number" class="form-control" name="min_budget" placeholder="Min">
                                    </div>
                                    <div class="col-6">
                                        <input type="number" class="form-control" name="max_budget" placeholder="Max">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="rating" class="form-label fw-bold">Évaluation minimum</label>
                                <select class="form-select" id="rating" name="min_rating">
                                    <option value="">Toutes les évaluations</option>
                                    <option value="5">5 étoiles</option>
                                    <option value="4">4 étoiles et plus</option>
                                    <option value="3">3 étoiles et plus</option>
                                    <option value="2">2 étoiles et plus</option>
                                    <option value="1">1 étoile et plus</option>
                                </select>
                            </div>

                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-filter me-2"></i>Filtrer
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Search Results -->
            <div class="col-lg-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <p class="mb-0">{{ $providers->total() }} prestataires trouvés</p>
                    <div>
                        <select id="sort" class="form-select form-select-sm" style="width: auto;">
                            <option value="relevance" {{ request('sort') == 'relevance' || !request('sort') ? 'selected' : '' }}>Pertinence</option>
                            <option value="rating" {{ request('sort') == 'rating' ? 'selected' : '' }}>Évaluation (plus élevée d'abord)</option>
                            <option value="price_asc" {{ request('sort') == 'price_asc' ? 'selected' : '' }}>Prix (croissant)</option>
                            <option value="price_desc" {{ request('sort') == 'price_desc' ? 'selected' : '' }}>Prix (décroissant)</option>
                        </select>
                    </div>
                </div>

                <!-- Filtres actifs -->
                @if(request('location_id') || request('min_rating') || request('min_budget') || request('max_budget'))
                <div class="mb-4">
                    <h6 class="mb-2">Filtres actifs:</h6>
                    <div class="d-flex flex-wrap gap-2">
                        @if(request('location_id'))
                            @php
                                $activeLocation = $locations->where('id', request('location_id'))->first();
                            @endphp
                            @if($activeLocation)
                                <div class="badge bg-primary p-2">
                                    Lieu: {{ $activeLocation->name }}
                                    <a href="{{ route('categories.show', array_merge(['slug' => $category->slug], request()->except(['location_id', 'page']))) }}" class="text-white ms-1">
                                        <i class="fas fa-times"></i>
                                    </a>
                                </div>
                            @endif
                        @endif

                        @if(request('min_rating'))
                            <div class="badge bg-primary p-2">
                                Évaluation: {{ request('min_rating') }}+ étoiles
                                <a href="{{ route('categories.show', array_merge(['slug' => $category->slug], request()->except(['min_rating', 'page']))) }}" class="text-white ms-1">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        @endif

                        @if(request('min_budget') && request('max_budget'))
                            <div class="badge bg-primary p-2">
                                Budget: {{ number_format(request('min_budget'), 0, ',', ' ') }} - {{ number_format(request('max_budget'), 0, ',', ' ') }} Ar
                                <a href="{{ route('categories.show', array_merge(['slug' => $category->slug], request()->except(['min_budget', 'max_budget', 'page']))) }}" class="text-white ms-1">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        @elseif(request('min_budget'))
                            <div class="badge bg-primary p-2">
                                Budget min: {{ number_format(request('min_budget'), 0, ',', ' ') }} Ar
                                <a href="{{ route('categories.show', array_merge(['slug' => $category->slug], request()->except(['min_budget', 'page']))) }}" class="text-white ms-1">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        @elseif(request('max_budget'))
                            <div class="badge bg-primary p-2">
                                Budget max: {{ number_format(request('max_budget'), 0, ',', ' ') }} Ar
                                <a href="{{ route('categories.show', array_merge(['slug' => $category->slug], request()->except(['max_budget', 'page']))) }}" class="text-white ms-1">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        @endif

                        <a href="{{ route('categories.show', $category->slug) }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Effacer tous les filtres
                        </a>
                    </div>
                </div>
                @endif

                @if($providers->count() > 0)
                    <div class="row">
                        @foreach($providers as $provider)
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card h-100 border-0 shadow-sm">
                                    @if($provider->gallery->where('is_featured', true)->first())
                                        <img src="{{ $provider->gallery->where('is_featured', true)->first()->getImageUrl() }}" class="card-img-top" alt="{{ $provider->business_name }}">
                                    @else
                                        <div class="bg-primary bg-opacity-10 text-primary text-center py-5">
                                            <i class="fas fa-building fa-3x"></i>
                                        </div>
                                    @endif
                                    <div class="card-body">
                                        <h5 class="card-title">
                                            {{ $provider->business_name }}
                                            @if(!$provider->is_verified)
                                                <span class="badge bg-warning ms-1" title="Prestataire non vérifié">Non vérifié</span>
                                            @endif
                                            @if(!$provider->is_active)
                                                <span class="badge bg-danger ms-1" title="Prestataire inactif">Inactif</span>
                                            @endif
                                        </h5>
                                        <p class="card-text small text-muted">
                                            <i class="fas fa-tag me-1"></i>{{ $category->name }}
                                        </p>
                                        <p class="card-text small">
                                            @php
                                                $categoryServices = $provider->services->where('category_id', $category->id)->take(3);
                                            @endphp
                                            @if($categoryServices->count() > 0)
                                                @foreach($categoryServices as $service)
                                                    <span class="badge bg-light text-dark me-1 mb-1">{{ $service->title }}</span>
                                                @endforeach
                                                @if($provider->services->where('category_id', $category->id)->count() > 3)
                                                    <span class="badge bg-light text-dark">+{{ $provider->services->where('category_id', $category->id)->count() - 3 }}</span>
                                                @endif
                                            @endif
                                        </p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="rating-stars">
                                                @for($i = 1; $i <= 5; $i++)
                                                    @if($i <= round($provider->average_rating))
                                                        <i class="fas fa-star text-warning"></i>
                                                    @else
                                                        <i class="far fa-star text-warning"></i>
                                                    @endif
                                                @endfor
                                                <span class="small text-muted ms-1">({{ $provider->reviews_count ?? 0 }})</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-footer bg-transparent border-0">
                                        <a href="{{ route('providers.show', $provider->id) }}" class="btn btn-primary w-100">
                                            <i class="fas fa-eye me-1"></i>Voir le profil
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <div class="d-flex justify-content-center mt-4">
                        {{ $providers->appends(request()->except('page'))->links() }}
                    </div>
                @else
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>Aucun prestataire trouvé avec des services dans cette catégorie. Essayez d'élargir votre recherche ou de supprimer certains filtres.
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Tri des résultats
        const sortSelect = document.getElementById('sort');
        if (sortSelect) {
            sortSelect.addEventListener('change', function() {
                const currentUrl = new URL(window.location.href);
                currentUrl.searchParams.set('sort', this.value);
                currentUrl.searchParams.set('page', 1);
                window.location.href = currentUrl.toString();
            });
        }
    });
</script>
@endsection
