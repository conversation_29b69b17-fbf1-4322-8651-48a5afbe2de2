@extends('ClientsView.layouts.app')

@section('title', 'Rechercher des prestataires')

@section('content')
<section class="search-header py-5 bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">Rechercher des prestataires</h1>
                <p class="lead mb-0">Trouvez les meilleurs prestataires pour votre événement</p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="d-inline-block bg-white text-primary p-3 rounded-circle">
                    <i class="fas fa-search fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="py-5">
    <div class="container">
        <div class="row">
            <div class="col-lg-8 mx-auto">
                <div class="card border-0 shadow-sm mb-5">
                    <div class="card-body p-4">
                        <h4 class="card-title mb-4">Recherche avancée</h4>

                        <form action="{{ route('search.results') }}" method="GET">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="keyword" class="form-label">Mot-clé</label>
                                    <input type="text" class="form-control" id="keyword" name="keyword" placeholder="Que recherchez-vous ?">
                                </div>
                                <div class="col-md-6">
                                    <label for="category_id" class="form-label">Catégorie</label>
                                    <select class="form-select" id="category_id" name="category_id">
                                        <option value="">Toutes les catégories</option>
                                        @foreach($categories as $category)
                                            <option value="{{ $category->id }}">{{ $category->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="event_type_id" class="form-label">Type d'événement</label>
                                    <select class="form-select" id="event_type_id" name="event_type_id">
                                        <option value="">Tous les types d'événements</option>
                                        @foreach($eventTypes as $type)
                                            <option value="{{ $type->id }}">{{ $type->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="location_id" class="form-label">Lieu</label>
                                    <select class="form-select" id="location_id" name="location_id">
                                        <option value="">Tous les lieux</option>
                                        @foreach($locations as $location)
                                            <option value="{{ $location->id }}">{{ $location->name }}</option>
                                        @endforeach
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label class="form-label">Budget</label>
                                    <div class="row">
                                        <div class="col-6">
                                            <input type="number" class="form-control" name="min_budget" placeholder="Min">
                                        </div>
                                        <div class="col-6">
                                            <input type="number" class="form-control" name="max_budget" placeholder="Max">
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="fixed_price" name="fixed_price" value="1" checked>
                                            <label class="form-check-label" for="fixed_price">
                                                Prix fixes uniquement
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <label for="rating" class="form-label">Évaluation minimum</label>
                                    <select class="form-select" id="rating" name="min_rating">
                                        <option value="">Toutes les évaluations</option>
                                        <option value="5">5 étoiles</option>
                                        <option value="4">4 étoiles et plus</option>
                                        <option value="3">3 étoiles et plus</option>
                                        <option value="2">2 étoiles et plus</option>
                                        <option value="1">1 étoile et plus</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="availability" class="form-label">Disponibilité</label>
                                    <input type="date" class="form-control" id="availability" name="availability_date">
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary w-100">
                                        <i class="fas fa-search me-2"></i>Rechercher
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-5">
            <div class="col-12">
                <h3 class="text-center mb-4">Parcourir par catégorie</h3>
                <div class="row">
                    @foreach($categories as $category)
                        <div class="col-md-2 mb-4">
                            <a href="{{ route('categories.show', $category->slug) }}" class="text-decoration-none">
                                <div class="card category-card text-center h-100">
                                    <div class="card-body">
                                        <div class="category-icon">
                                            <i class="fas {{ $category->icon }}"></i>
                                        </div>
                                        <h5 class="card-title">{{ $category->name }}</h5>
                                    </div>
                                </div>
                            </a>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <div class="row mt-5">
            <div class="col-12">
                <h3 class="text-center mb-4">Parcourir par type d'événement</h3>
                <div class="row">
                    @foreach($eventTypes as $eventType)
                        <div class="col-md-3 col-lg-2 mb-4">
                            <a href="{{ route('event-types.show', $eventType->slug) }}" class="text-decoration-none">
                                <div class="event-type-card">
                                    <div class="event-type-icon">
                                        <i class="fas {{ $eventType->icon }}"></i>
                                    </div>
                                    <h5>{{ $eventType->name }}</h5>
                                </div>
                            </a>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>

        <div class="row mt-5">
            <div class="col-12">
                <h3 class="text-center mb-4">Parcourir par lieu</h3>
                <div class="row">
                    @foreach($locations as $location)
                        <div class="col-md-3 mb-4">
                            <a href="{{ route('locations.show', $location->slug) }}" class="text-decoration-none">
                                <div class="card h-100 border-0 shadow-sm text-center">
                                    <div class="card-body">
                                        <div class="mb-3">
                                            <i class="fas fa-map-marker-alt fa-3x text-primary"></i>
                                        </div>
                                        <h5 class="card-title">{{ $location->name }}</h5>
                                    </div>
                                </div>
                            </a>
                        </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
