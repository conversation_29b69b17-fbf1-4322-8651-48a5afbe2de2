@extends('ClientsView.layouts.app')

@section('title', $eventType->name . ' - Prestataires pour ' . $eventType->name)

@section('content')
<section class="search-header py-5 bg-primary text-white">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-8">
                <h1 class="mb-2">Prestataires pour {{ $eventType->name }}</h1>
                <p class="lead mb-0">Trouvez les meilleurs prestataires spécialisés dans les événements de type {{ $eventType->name }}</p>
            </div>
            <div class="col-md-4 text-md-end">
                <div class="d-inline-block bg-white text-primary p-3 rounded-circle">
                    <i class="fas {{ $eventType->icon }} fa-2x"></i>
                </div>
            </div>
        </div>
    </div>
</section>

<section class="py-5">
    <div class="container">
        <div class="row">
            <!-- Sidebar Filters -->
            <div class="col-lg-3 mb-4 mb-lg-0">
                <div class="card border-0 shadow-sm">
                    <div class="card-body">
                        <h5 class="card-title mb-4">Filtres</h5>

                        <form action="{{ route('search.results') }}" method="GET">
                            <input type="hidden" name="event_type_id" value="{{ $eventType->id }}">

                            <div class="mb-4">
                                <label class="form-label fw-bold">Catégories</label>
                                <div class="list-group list-group-flush">
                                    @foreach($categories as $category)
                                        <a href="{{ route('categories.show', $category->slug) }}" class="list-group-item list-group-item-action border-0 ps-0">
                                            <i class="fas {{ $category->icon }} me-2 text-primary"></i>
                                            {{ $category->name }}
                                        </a>
                                    @endforeach
                                </div>
                            </div>

                            <div class="mb-4">
                                <label class="form-label fw-bold">Types d'événements</label>
                                <div class="list-group list-group-flush">
                                    @foreach($eventTypes as $type)
                                        <a href="{{ route('event-types.show', $type->slug) }}" class="list-group-item list-group-item-action border-0 ps-0 {{ $type->id == $eventType->id ? 'active bg-primary text-white' : '' }}">
                                            <i class="fas {{ $type->icon }} me-2 {{ $type->id == $eventType->id ? 'text-white' : 'text-primary' }}"></i>
                                            {{ $type->name }}
                                        </a>
                                    @endforeach
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="location" class="form-label fw-bold">Lieu</label>
                                <select class="form-select" id="location" name="location_id">
                                    <option value="">Tous les lieux</option>
                                    @foreach($locations as $location)
                                        <option value="{{ $location->id }}">{{ $location->name }}</option>
                                    @endforeach
                                </select>
                            </div>

                            <div class="mb-4">
                                <label class="form-label fw-bold">Budget</label>
                                <div class="row g-2">
                                    <div class="col-6">
                                        <input type="number" class="form-control" name="min_budget" placeholder="Min">
                                    </div>
                                    <div class="col-6">
                                        <input type="number" class="form-control" name="max_budget" placeholder="Max">
                                    </div>
                                </div>
                            </div>

                            <div class="mb-4">
                                <label for="rating" class="form-label fw-bold">Évaluation minimum</label>
                                <select class="form-select" id="rating" name="min_rating">
                                    <option value="">Toutes les évaluations</option>
                                    <option value="5">5 étoiles</option>
                                    <option value="4">4 étoiles et plus</option>
                                    <option value="3">3 étoiles et plus</option>
                                    <option value="2">2 étoiles et plus</option>
                                    <option value="1">1 étoile et plus</option>
                                </select>
                            </div>

                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-filter me-2"></i>Filtrer
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Search Results -->
            <div class="col-lg-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <p class="mb-0">{{ $providers->total() }} prestataires trouvés</p>
                    <div class="dropdown">
                        <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="sortDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            Trier par
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="sortDropdown">
                            <li><a class="dropdown-item" href="#">Pertinence</a></li>
                            <li><a class="dropdown-item" href="#">Évaluation (plus élevée d'abord)</a></li>
                            <li><a class="dropdown-item" href="#">Prix (croissant)</a></li>
                            <li><a class="dropdown-item" href="#">Prix (décroissant)</a></li>
                        </ul>
                    </div>
                </div>

                @if($providers->count() > 0)
                    <div class="row">
                        @foreach($providers as $provider)
                            <div class="col-md-6 col-lg-4 mb-4">
                                <div class="card h-100 border-0 shadow-sm">
                                    @if($provider->gallery->where('is_featured', true)->first())
                                        <img src="{{ $provider->gallery->where('is_featured', true)->first()->getImageUrl() }}" class="card-img-top" alt="{{ $provider->business_name }}">
                                    @else
                                        <div class="bg-primary bg-opacity-10 text-primary text-center py-5">
                                            <i class="fas fa-building fa-3x"></i>
                                        </div>
                                    @endif
                                    <div class="card-body">
                                        <h5 class="card-title">
                                            {{ $provider->business_name }}
                                            @if(!$provider->is_verified)
                                                <span class="badge bg-warning ms-1" title="Prestataire non vérifié">Non vérifié</span>
                                            @endif
                                            @if(!$provider->is_active)
                                                <span class="badge bg-danger ms-1" title="Prestataire inactif">Inactif</span>
                                            @endif
                                        </h5>
                                        <p class="card-text small text-muted">
                                            @if($provider->categories->where('pivot.is_primary', true)->first())
                                                <i class="fas fa-tag me-1"></i>{{ $provider->categories->where('pivot.is_primary', true)->first()->name }}
                                            @endif
                                        </p>
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div class="rating-stars">
                                                @for($i = 1; $i <= 5; $i++)
                                                    @if($i <= round($provider->average_rating))
                                                        <i class="fas fa-star text-warning"></i>
                                                    @else
                                                        <i class="far fa-star text-warning"></i>
                                                    @endif
                                                @endfor
                                                <span class="small text-muted ms-1">({{ $provider->reviews_count ?? 0 }})</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="card-footer bg-transparent border-0">
                                        <a href="{{ route('providers.show', $provider->id) }}" class="btn btn-primary w-100">
                                            <i class="fas fa-eye me-1"></i>Voir le profil
                                        </a>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>

                    <div class="d-flex justify-content-center mt-4">
                        {{ $providers->links() }}
                    </div>
                @else
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>Aucun prestataire trouvé pour ce type d'événement. Essayez d'élargir votre recherche.
                    </div>
                @endif
            </div>
        </div>
    </div>
</section>
@endsection
