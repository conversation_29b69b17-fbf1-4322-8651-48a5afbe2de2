@extends('ClientsView.layouts.app')

@section('title', 'Résultats de recherche')

@section('content')
<div class="container py-5">
    <div class="row">
        <!-- Sidebar de filtres -->
        <div class="col-lg-3 mb-4">
            <div class="card shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Filtres</h5>
                </div>
                <div class="card-body">
                    <form action="{{ route('search.results') }}" method="GET">
                        @if($keyword)
                            <input type="hidden" name="keyword" value="{{ $keyword }}">
                        @endif

                        <!-- Catégories -->
                        <div class="mb-4">
                            <h6 class="fw-bold">Catégories</h6>
                            <select name="category_id" class="form-select form-select-sm">
                                <option value="">Toutes les catégories</option>
                                @foreach($categories as $category)
                                    <option value="{{ $category->id }}" {{ $categoryId == $category->id ? 'selected' : '' }}>
                                        {{ $category->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Budget -->
                        <div class="mb-4">
                            <h6 class="fw-bold">Budget</h6>
                            <select name="budget_range" class="form-select form-select-sm mb-3">
                                <option value="">Tous les budgets</option>
                                <option value="0-50000" {{ $budgetRange == '0-50000' ? 'selected' : '' }}>Moins de 50 000 Ar</option>
                                <option value="50000-100000" {{ $budgetRange == '50000-100000' ? 'selected' : '' }}>50 000 - 100 000 Ar</option>
                                <option value="100000-250000" {{ $budgetRange == '100000-250000' ? 'selected' : '' }}>100 000 - 250 000 Ar</option>
                                <option value="250000-500000" {{ $budgetRange == '250000-500000' ? 'selected' : '' }}>250 000 - 500 000 Ar</option>
                                <option value="500000-1000000" {{ $budgetRange == '500000-1000000' ? 'selected' : '' }}>500 000 - 1 000 000 Ar</option>
                                <option value="1000000-2500000" {{ $budgetRange == '1000000-2500000' ? 'selected' : '' }}>1 000 000 - 2 500 000 Ar</option>
                                <option value="2500000-5000000" {{ $budgetRange == '2500000-5000000' ? 'selected' : '' }}>2 500 000 - 5 000 000 Ar</option>
                                <option value="5000000+" {{ $budgetRange == '5000000+' ? 'selected' : '' }}>Plus de 5 000 000 Ar</option>
                            </select>

                            <div class="row g-2 mb-2">
                                <div class="col-6">
                                    <input type="number" class="form-control form-control-sm" name="min_budget" placeholder="Min" value="{{ $minBudget ?? '' }}">
                                </div>
                                <div class="col-6">
                                    <input type="number" class="form-control form-control-sm" name="max_budget" placeholder="Max" value="{{ $maxBudget ?? '' }}">
                                </div>
                            </div>

                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="fixed_price" name="fixed_price" value="1" {{ isset($fixedPrice) && $fixedPrice ? 'checked' : '' }}>
                                <label class="form-check-label" for="fixed_price">
                                    Prix fixes uniquement
                                </label>
                            </div>
                        </div>

                        <!-- Lieu -->
                        <div class="mb-4">
                            <h6 class="fw-bold">Lieu</h6>
                            <select name="location_id" class="form-select form-select-sm">
                                <option value="">Tous les lieux</option>
                                @foreach($locations as $location)
                                    <option value="{{ $location->id }}" {{ $locationId == $location->id ? 'selected' : '' }}>
                                        {{ $location->name }}
                                    </option>
                                @endforeach
                            </select>
                        </div>

                        <!-- Évaluation -->
                        <div class="mb-4">
                            <h6 class="fw-bold">Évaluation minimum</h6>
                            <div class="d-flex flex-column">
                                @for($i = 5; $i >= 1; $i--)
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="min_rating" id="rating{{ $i }}" value="{{ $i }}" {{ $minRating == $i ? 'checked' : '' }}>
                                        <label class="form-check-label" for="rating{{ $i }}">
                                            @for($j = 1; $j <= 5; $j++)
                                                @if($j <= $i)
                                                    <i class="fas fa-star text-warning"></i>
                                                @else
                                                    <i class="far fa-star text-warning"></i>
                                                @endif
                                            @endfor
                                            @if($i == 5)
                                                (5 étoiles uniquement)
                                            @else
                                                ({{ $i }}+ étoiles)
                                            @endif
                                        </label>
                                    </div>
                                @endfor
                                <div class="form-check">
                                    <input class="form-check-input" type="radio" name="min_rating" id="rating0" value="" {{ !$minRating ? 'checked' : '' }}>
                                    <label class="form-check-label" for="rating0">
                                        Toutes les évaluations
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Disponibilité -->
                        <div class="mb-4">
                            <h6 class="fw-bold">Disponibilité</h6>
                            <input type="date" class="form-control form-control-sm" name="availability_date" value="{{ $availabilityDate ?? '' }}">
                        </div>

                        <button type="submit" class="btn btn-primary w-100">
                            <i class="fas fa-filter me-1"></i>Appliquer les filtres
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Résultats de recherche -->
        <div class="col-lg-9">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h4 class="mb-0">{{ $providers->total() }} résultat(s) trouvé(s)</h4>
                <div class="d-flex align-items-center">
                    <label for="sort" class="me-2">Trier par:</label>
                    <select id="sort" class="form-select form-select-sm" style="width: auto;">
                        <option value="relevance" {{ $sort == 'relevance' || !$sort ? 'selected' : '' }}>Pertinence</option>
                        <option value="price_asc" {{ $sort == 'price_asc' ? 'selected' : '' }}>Prix (croissant)</option>
                        <option value="price_desc" {{ $sort == 'price_desc' ? 'selected' : '' }}>Prix (décroissant)</option>
                        <option value="rating" {{ $sort == 'rating' ? 'selected' : '' }}>Évaluation</option>
                    </select>
                </div>
            </div>

            <!-- Filtres actifs -->
            @if($keyword || $categoryId || $budgetRange || $minBudget || $maxBudget || $locationId || $minRating || $availabilityDate || $negotiable)
                <div class="mb-4">
                    <h6 class="mb-2">Filtres actifs:</h6>
                    <div class="d-flex flex-wrap gap-2">
                        @if($keyword)
                            <div class="badge bg-primary p-2">
                                Mot-clé: {{ $keyword }}
                                <a href="{{ route('search.results', array_merge(request()->except('keyword'), ['page' => 1])) }}" class="text-white ms-1">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        @endif

                        @if($categoryId)
                            <div class="badge bg-primary p-2">
                                Catégorie: {{ $categories->where('id', $categoryId)->first()->name }}
                                <a href="{{ route('search.results', array_merge(request()->except('category_id'), ['page' => 1])) }}" class="text-white ms-1">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        @endif

                        @if($budgetRange)
                            <div class="badge bg-primary p-2">
                                Budget:
                                @if($budgetRange == '0-50000')
                                    Moins de 50 000 Ar
                                @elseif($budgetRange == '5000000+')
                                    Plus de 5 000 000 Ar
                                @else
                                    @php
                                        list($min, $max) = explode('-', $budgetRange);
                                        echo number_format($min, 0, ',', ' ') . ' - ' . number_format($max, 0, ',', ' ') . ' Ar';
                                    @endphp
                                @endif
                                <a href="{{ route('search.results', array_merge(request()->except('budget_range'), ['page' => 1])) }}" class="text-white ms-1">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        @endif

                        @if($minBudget && $maxBudget)
                            <div class="badge bg-primary p-2">
                                Budget personnalisé: {{ number_format($minBudget, 0, ',', ' ') }} - {{ number_format($maxBudget, 0, ',', ' ') }} Ar
                                <a href="{{ route('search.results', array_merge(request()->except(['min_budget', 'max_budget']), ['page' => 1])) }}" class="text-white ms-1">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        @elseif($minBudget)
                            <div class="badge bg-primary p-2">
                                Budget min: {{ number_format($minBudget, 0, ',', ' ') }} Ar
                                <a href="{{ route('search.results', array_merge(request()->except('min_budget'), ['page' => 1])) }}" class="text-white ms-1">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        @elseif($maxBudget)
                            <div class="badge bg-primary p-2">
                                Budget max: {{ number_format($maxBudget, 0, ',', ' ') }} Ar
                                <a href="{{ route('search.results', array_merge(request()->except('max_budget'), ['page' => 1])) }}" class="text-white ms-1">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        @endif

                        @if($negotiable)
                            <div class="badge bg-primary p-2">
                                Prix négociables uniquement
                                <a href="{{ route('search.results', array_merge(request()->except('negotiable'), ['page' => 1])) }}" class="text-white ms-1">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        @endif

                        @if($locationId)
                            <div class="badge bg-primary p-2">
                                Lieu: {{ $locations->where('id', $locationId)->first()->name }}
                                <a href="{{ route('search.results', array_merge(request()->except('location_id'), ['page' => 1])) }}" class="text-white ms-1">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        @endif

                        @if($minRating)
                            <div class="badge bg-primary p-2">
                                Évaluation: {{ $minRating }}+ étoiles
                                <a href="{{ route('search.results', array_merge(request()->except('min_rating'), ['page' => 1])) }}" class="text-white ms-1">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        @endif

                        @if($availabilityDate)
                            <div class="badge bg-primary p-2">
                                Disponible le: {{ \Carbon\Carbon::parse($availabilityDate)->format('d/m/Y') }}
                                <a href="{{ route('search.results', array_merge(request()->except('availability_date'), ['page' => 1])) }}" class="text-white ms-1">
                                    <i class="fas fa-times"></i>
                                </a>
                            </div>
                        @endif

                        <a href="{{ route('search.results') }}" class="btn btn-sm btn-outline-secondary">
                            <i class="fas fa-times me-1"></i>Effacer tous les filtres
                        </a>
                    </div>
                </div>
            @endif

            <!-- Recommandations basées sur le budget -->
            @if($minBudget || $maxBudget)
                <div class="card border-0 shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0">Services recommandés pour votre budget</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @php
                                $budgetServices = collect();
                                foreach($providers as $provider) {
                                    foreach($provider->services as $service) {
                                        $price = $service->getRealPrice();
                                        if ($price > 0) {
                                            if ((!$minBudget || $price >= $minBudget) && (!$maxBudget || $price <= $maxBudget)) {
                                                $budgetServices->push([
                                                    'provider' => $provider,
                                                    'service' => $service,
                                                    'price' => $price
                                                ]);
                                            }
                                        }
                                    }
                                }

                                // Trier par prix (croissant)
                                $budgetServices = $budgetServices->sortBy('price')->take(6);
                            @endphp

                            @if($budgetServices->count() > 0)
                                @foreach($budgetServices as $item)
                                    <div class="col-md-6 col-lg-4 mb-3">
                                        <div class="card h-100 border shadow-sm">
                                            <div class="card-body">
                                                <div class="d-flex justify-content-between align-items-start mb-2">
                                                    <h6 class="card-title mb-0">{{ $item['service']->title }}</h6>
                                                    <span class="badge bg-primary">{{ number_format($item['price'], 0, ',', ' ') }} Ar</span>
                                                </div>
                                                <p class="small text-muted mb-2">
                                                    <i class="fas fa-building me-1"></i>{{ $item['provider']->business_name }}
                                                </p>
                                                <p class="small mb-3">{{ Str::limit($item['service']->description, 60) }}</p>
                                                <div class="d-grid">
                                                    <a href="{{ route('providers.service', ['providerId' => $item['provider']->id, 'serviceId' => $item['service']->id]) }}" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye me-1"></i>Voir les détails
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            @else
                                <div class="col-12">
                                    <div class="alert alert-info mb-0">
                                        <i class="fas fa-info-circle me-2"></i>Aucun service ne correspond exactement à votre budget. Essayez d'ajuster votre fourchette de prix.
                                    </div>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            @endif

            <!-- Liste des prestataires -->
            @if($providers->count() > 0)
                <div class="row">
                    @foreach($providers as $provider)
                        <div class="col-md-6 mb-4">
                            <div class="card h-100 shadow-sm hover-shadow">
                                <div class="row g-0">
                                    <div class="col-md-4">
                                        @if($provider->gallery->where('is_featured', true)->first())
                                            <img src="{{ $provider->gallery->where('is_featured', true)->first()->getImageUrl() }}"
                                                class="img-fluid rounded-start h-100" style="object-fit: cover;"
                                                alt="{{ $provider->business_name }}">
                                        @else
                                            <div class="bg-light d-flex align-items-center justify-content-center h-100 rounded-start">
                                                <i class="fas fa-building fa-3x text-secondary"></i>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="col-md-8">
                                        <div class="card-body">
                                            <div class="d-flex justify-content-between align-items-start">
                                                <h5 class="card-title mb-1">
                                                    {{ $provider->business_name }}
                                                    @if(!$provider->is_verified)
                                                        <span class="badge bg-warning ms-1" title="Prestataire non vérifié">Non vérifié</span>
                                                    @endif
                                                    @if(!$provider->is_active)
                                                        <span class="badge bg-danger ms-1" title="Prestataire inactif">Inactif</span>
                                                    @endif
                                                </h5>
                                                <span class="badge bg-primary">
                                                    @if($provider->categories->where('pivot.is_primary', true)->first())
                                                        {{ $provider->categories->where('pivot.is_primary', true)->first()->name }}
                                                    @else
                                                        {{ $provider->categories->first()->name ?? 'Non catégorisé' }}
                                                    @endif
                                                </span>
                                            </div>

                                            <div class="mb-2">
                                                <div class="text-warning">
                                                    @for($i = 1; $i <= 5; $i++)
                                                        @if($i <= round($provider->average_rating))
                                                            <i class="fas fa-star"></i>
                                                        @else
                                                            <i class="far fa-star"></i>
                                                        @endif
                                                    @endfor
                                                    <span class="text-muted ms-1">({{ $provider->reviews_count }})</span>
                                                </div>
                                            </div>

                                            <p class="card-text small text-muted mb-2">
                                                <i class="fas fa-map-marker-alt me-1"></i>
                                                @php
                                                    try {
                                                        $serviceAreas = $provider->getServiceAreasArray();
                                                        $areasText = count($serviceAreas) > 0 ? implode(', ', $serviceAreas) : 'Non spécifié';
                                                    } catch (\Exception $e) {
                                                        $areasText = 'Non spécifié';
                                                    }
                                                @endphp
                                                {{ $areasText }}
                                            </p>

                                            <!-- Affichage des services et prix -->
                                            <div class="mb-3">
                                                @if($provider->services->count() > 0)
                                                    <!-- Affichage des services avec détails spécifiques -->
                                                    <div class="service-highlights mb-2">
                                                        @php
                                                            $primaryCategory = $provider->categories->where('pivot.is_primary', true)->first()
                                                                ?? $provider->categories->first();
                                                            $categoryName = $primaryCategory ? $primaryCategory->name : '';

                                                            // Récupérer les services de la catégorie principale
                                                            $categoryServices = $provider->services->filter(function($service) use ($primaryCategory) {
                                                                return $service->category_id == $primaryCategory->id;
                                                            });

                                                            // Prendre le premier service pour afficher des détails
                                                            $firstService = $categoryServices->first() ?? $provider->services->first();
                                                            $options = $firstService ? ($firstService->options ?? []) : [];
                                                        @endphp

                                                        @if($categoryName == 'Traiteur' && !empty($options))
                                                            @if(isset($options['menu_items']) && is_array($options['menu_items']))
                                                                <span class="badge bg-info me-1">{{ count($options['menu_items']) }} plats</span>
                                                            @endif
                                                            @if(isset($options['includes_service']) && $options['includes_service'])
                                                                <span class="badge bg-info me-1">Service inclus</span>
                                                            @endif
                                                        @elseif($categoryName == 'Musique et Animation' && !empty($options))
                                                            @if(isset($options['artist_type']))
                                                                <span class="badge bg-info me-1">{{ $options['artist_type'] }}</span>
                                                            @endif
                                                            @if(isset($options['duration']))
                                                                <span class="badge bg-info me-1">{{ $options['duration'] }}h</span>
                                                            @endif
                                                        @elseif($categoryName == 'Transport' && !empty($options))
                                                            @if(isset($options['vehicle_type']))
                                                                <span class="badge bg-info me-1">{{ $options['vehicle_type'] }}</span>
                                                            @endif
                                                            @if(isset($options['seats']))
                                                                <span class="badge bg-info me-1">{{ $options['seats'] }} places</span>
                                                            @endif
                                                        @elseif($categoryName == 'Lieu de Réception' && !empty($options))
                                                            @if(isset($options['capacity']))
                                                                <span class="badge bg-info me-1">{{ $options['capacity'] }} pers.</span>
                                                            @endif
                                                            @if(isset($options['indoor']) && $options['indoor'] && isset($options['outdoor']) && $options['outdoor'])
                                                                <span class="badge bg-info me-1">Intérieur/Extérieur</span>
                                                            @elseif(isset($options['indoor']) && $options['indoor'])
                                                                <span class="badge bg-info me-1">Intérieur</span>
                                                            @elseif(isset($options['outdoor']) && $options['outdoor'])
                                                                <span class="badge bg-info me-1">Extérieur</span>
                                                            @endif
                                                        @endif
                                                    </div>

                                                    <!-- Affichage des prix -->
                                                    <p class="card-text mb-0">
                                                        <span class="fw-bold">Prix:</span>
                                                        @php
                                                            // Récupérer les prix réels des services
                                                            $prices = $provider->services->map(function($service) {
                                                                return $service->getRealPrice();
                                                            })->filter(function($price) {
                                                                return $price > 0;
                                                            });

                                                            $minPrice = $prices->count() > 0 ? $prices->min() : 0;
                                                            $maxPrice = $prices->count() > 0 ? $prices->max() : 0;
                                                        @endphp

                                                        @if($prices->count() == 0)
                                                            <span class="text-muted">Sur devis</span>
                                                        @elseif($minPrice == $maxPrice)
                                                            <span class="text-success fw-bold">{{ number_format($minPrice, 0, ',', ' ') }} Ar</span>
                                                            <i class="fas fa-tag text-primary ms-1" data-bs-toggle="tooltip" title="Prix fixe"></i>
                                                        @else
                                                            <span class="text-success fw-bold">{{ number_format($minPrice, 0, ',', ' ') }} - {{ number_format($maxPrice, 0, ',', ' ') }} Ar</span>
                                                            <i class="fas fa-tag text-primary ms-1" data-bs-toggle="tooltip" title="Prix fixe"></i>
                                                        @endif
                                                    </p>
                                                @else
                                                    <p class="card-text text-muted mb-0">Prix non spécifié</p>
                                                @endif
                                            </div>

                                            <a href="{{ route('providers.show', $provider->id) }}" class="btn btn-sm btn-primary">
                                                <i class="fas fa-eye me-1"></i>Voir le profil
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="d-flex justify-content-center mt-4">
                    {{ $providers->appends(request()->except('page'))->links('ClientsView.search.pagination') }}
                </div>
            @else
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>Aucun prestataire ne correspond à vos critères de recherche. Essayez d'élargir vos filtres.
                </div>
            @endif
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Tri des résultats
        const sortSelect = document.getElementById('sort');
        if (sortSelect) {
            sortSelect.addEventListener('change', function() {
                const currentUrl = new URL(window.location.href);
                currentUrl.searchParams.set('sort', this.value);
                currentUrl.searchParams.set('page', 1);
                window.location.href = currentUrl.toString();
            });
        }

        // Initialiser les tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>
@endsection
