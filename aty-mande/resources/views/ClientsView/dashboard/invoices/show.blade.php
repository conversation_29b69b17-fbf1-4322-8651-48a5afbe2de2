@extends('ClientsView.layouts.dashboard')

@section('title', 'Détails de la facture')

@section('header-title', 'Détails de la facture')

@section('content')
    @if (session('status'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('status') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-md-8">
            <div class="dashboard-card mb-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-invoice-dollar me-2 text-primary"></i>
                        Facture #{{ $invoice->invoice_number }}
                    </h5>
                    <span class="badge bg-{{ 
                        $invoice->status === 'paid' ? 'success' : 
                        ($invoice->status === 'partially_paid' ? 'info' : 
                        ($invoice->status === 'overdue' ? 'danger' : 'warning')) 
                    }} fs-6">
                        {{ $invoice->status === 'paid' ? 'Payée' : 
                           ($invoice->status === 'partially_paid' ? 'Partiellement payée' : 
                           ($invoice->status === 'overdue' ? 'En retard' : 'En attente')) 
                        }}
                    </span>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="fw-bold mb-2">Facturé à</h6>
                        <p class="mb-1">{{ auth()->user()->name }}</p>
                        <p class="mb-1">{{ auth()->user()->email }}</p>
                        <p class="mb-0">{{ auth()->user()->phone ?? 'Téléphone non spécifié' }}</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <h6 class="fw-bold mb-2">Prestataire</h6>
                        <p class="mb-1">{{ $invoice->provider->business_name }}</p>
                        <p class="mb-1">{{ $invoice->provider->business_email ?? $invoice->provider->user->email }}</p>
                        <p class="mb-0">{{ $invoice->provider->business_phone ?? $invoice->provider->user->phone ?? 'Téléphone non spécifié' }}</p>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <p class="mb-1"><strong>Date d'émission:</strong> {{ $invoice->created_at->format('d/m/Y') }}</p>
                        <p class="mb-0"><strong>Date d'échéance:</strong> {{ $invoice->due_date->format('d/m/Y') }}</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <p class="mb-1"><strong>Réservation:</strong> {{ $invoice->booking->booking_number }}</p>
                        <p class="mb-0"><strong>Service:</strong> {{ $invoice->booking->providerService->title }}</p>
                    </div>
                </div>

                <div class="mb-4">
                    <h6 class="fw-bold mb-3">Détails de la facture</h6>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>Description</th>
                                    <th class="text-end">Montant</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>Montant de base</td>
                                    <td class="text-end">{{ number_format($invoice->subtotal_amount, 0, ',', ' ') }} Ar</td>
                                </tr>
                                
                                @if($invoice->tax_amount > 0)
                                    <tr>
                                        <td>TVA</td>
                                        <td class="text-end">{{ number_format($invoice->tax_amount, 0, ',', ' ') }} Ar</td>
                                    </tr>
                                @endif
                                
                                @if($invoice->discount_amount > 0)
                                    <tr>
                                        <td>Remise</td>
                                        <td class="text-end">-{{ number_format($invoice->discount_amount, 0, ',', ' ') }} Ar</td>
                                    </tr>
                                @endif
                                
                                <tr>
                                    <td><strong>Sous-total</strong></td>
                                    <td class="text-end"><strong>{{ number_format($invoice->subtotal_amount + $invoice->tax_amount - $invoice->discount_amount, 0, ',', ' ') }} Ar</strong></td>
                                </tr>
                                
                                <tr class="table-light">
                                    <td><strong>Total</strong></td>
                                    <td class="text-end"><strong>{{ number_format($invoice->total_amount, 0, ',', ' ') }} Ar</strong></td>
                                </tr>
                                
                                <tr>
                                    <td>Montant payé</td>
                                    <td class="text-end">{{ number_format($invoice->paid_amount, 0, ',', ' ') }} Ar</td>
                                </tr>
                                
                                <tr class="{{ $invoice->due_amount > 0 ? 'table-warning' : 'table-success' }}">
                                    <td><strong>Solde restant</strong></td>
                                    <td class="text-end"><strong>{{ number_format($invoice->due_amount, 0, ',', ' ') }} Ar</strong></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                @if($invoice->notes)
                    <div class="mb-4">
                        <h6 class="fw-bold mb-2">Notes / Conditions</h6>
                        <div class="card bg-light">
                            <div class="card-body">
                                <p class="mb-0">{{ $invoice->notes }}</p>
                            </div>
                        </div>
                    </div>
                @endif

                <div class="d-flex justify-content-between">
                    <a href="{{ route('client.invoices') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                    </a>
                    
                    <div>
                        <a href="{{ route('client.invoices.download', $invoice->id) }}" class="btn btn-outline-primary me-2">
                            <i class="fas fa-download me-2"></i>Télécharger
                        </a>
                        
                        @if($invoice->due_amount > 0)
                            <a href="{{ route('client.payments.show', $invoice->booking_id) }}" class="btn btn-success">
                                <i class="fas fa-credit-card me-2"></i>Payer
                            </a>
                        @endif
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="dashboard-card mb-4">
                <h5 class="card-title mb-3">
                    <i class="fas fa-qrcode me-2 text-primary"></i>
                    Code QR
                </h5>
                
                <div class="text-center py-3">
                    @if($invoice->qr_code)
                        <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data={{ urlencode(json_encode([
                            'invoice_number' => $invoice->invoice_number,
                            'amount' => $invoice->total_amount,
                            'client' => auth()->user()->name,
                            'provider' => $invoice->provider->business_name,
                            'date' => $invoice->created_at->format('Y-m-d'),
                        ])) }}" alt="QR Code" class="img-fluid mb-3" style="max-width: 200px;">
                    @else
                        <div class="bg-light rounded p-5 mb-3">
                            <i class="fas fa-qrcode fa-5x text-muted"></i>
                        </div>
                    @endif
                    <p class="small text-muted mb-0">Scannez ce code QR pour accéder rapidement aux détails de la facture.</p>
                </div>
            </div>

            <div class="dashboard-card mb-4">
                <h5 class="card-title mb-3">
                    <i class="fas fa-history me-2 text-primary"></i>
                    Historique des paiements
                </h5>
                
                @if(count($invoice->payments) > 0)
                    <div class="timeline">
                        @foreach($invoice->payments as $payment)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <div class="d-flex justify-content-between">
                                        <h6 class="mb-0">{{ number_format($payment->amount, 0, ',', ' ') }} Ar</h6>
                                        <span class="badge bg-success">{{ $payment->status }}</span>
                                    </div>
                                    <p class="small text-muted mb-1">{{ $payment->paymentMethod->name }}</p>
                                    <p class="small text-muted mb-0">{{ $payment->paid_at->format('d/m/Y H:i') }}</p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <img src="https://cdn-icons-png.flaticon.com/512/6598/6598519.png" alt="No payments" style="width: 80px; opacity: 0.5;" class="mb-3">
                        <p class="text-muted">Aucun paiement effectué</p>
                    </div>
                @endif
            </div>

            <div class="dashboard-card mb-4">
                <h5 class="card-title mb-3">
                    <i class="fas fa-info-circle me-2 text-primary"></i>
                    Informations sur la réservation
                </h5>
                
                <div class="mb-3">
                    <p class="text-muted mb-1">Service réservé</p>
                    <h6>{{ $invoice->booking->providerService->title }}</h6>
                    <p class="small text-muted">
                        <i class="fas fa-tag me-1"></i>
                        {{ $invoice->booking->providerService->category->name }}
                    </p>
                </div>
                
                <div class="mb-3">
                    <p class="text-muted mb-1">Date de l'événement</p>
                    <h6>{{ $invoice->booking->event_date ? $invoice->booking->event_date->format('d/m/Y') : 'Non spécifiée' }}</h6>
                    @if($invoice->booking->event_start_time)
                        <p class="small text-muted">
                            <i class="fas fa-clock me-1"></i>
                            {{ \Carbon\Carbon::parse($invoice->booking->event_start_time)->format('H:i') }}
                            @if($invoice->booking->event_end_time)
                                - {{ \Carbon\Carbon::parse($invoice->booking->event_end_time)->format('H:i') }}
                            @endif
                        </p>
                    @endif
                </div>
                
                <div class="mb-0">
                    <p class="text-muted mb-1">Lieu de l'événement</p>
                    <h6>{{ $invoice->booking->event_location ?: 'Non spécifié' }}</h6>
                </div>
                
                <hr>
                
                <div class="d-grid">
                    <a href="{{ route('client.bookings.show', $invoice->booking_id) }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye me-2"></i>Voir les détails de la réservation
                    </a>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    
    .timeline:before {
        content: '';
        position: absolute;
        left: 10px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #e9ecef;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }
    
    .timeline-marker {
        position: absolute;
        left: -30px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #6c757d;
        border: 3px solid #fff;
        box-shadow: 0 0 0 2px #e9ecef;
    }
    
    .timeline-content {
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }
</style>
@endpush
