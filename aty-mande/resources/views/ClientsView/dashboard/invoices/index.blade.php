@extends('ClientsView.layouts.dashboard')

@section('title', 'Mes factures')

@section('header-title', 'Mes factures')

@push('styles')
<style>
    .invoice-card {
        transition: all 0.3s ease;
        border-radius: 12px;
        overflow: hidden;
        margin-bottom: 1.5rem;
        border: none;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }
    
    .invoice-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }
    
    .invoice-card .card-header {
        padding: 1.25rem 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .invoice-card .card-body {
        padding: 1.5rem;
    }
    
    .invoice-card .card-footer {
        padding: 1.25rem 1.5rem;
        background-color: transparent;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
    }
    
    .invoice-number {
        font-family: monospace;
        font-size: 0.9rem;
        padding: 0.35rem 0.75rem;
        border-radius: 6px;
        background-color: #f3f4f6;
        color: #1f2937;
    }
    
    .invoice-info-item {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }
    
    .invoice-info-item:last-child {
        margin-bottom: 0;
    }
    
    .invoice-info-icon {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1rem;
    }
    
    .invoice-info-content {
        flex: 1;
    }
    
    .invoice-info-label {
        font-size: 0.85rem;
        color: #6b7280;
        margin-bottom: 0.25rem;
    }
    
    .invoice-info-value {
        font-weight: 500;
        margin: 0;
    }
    
    .invoice-status {
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-weight: 500;
        font-size: 0.85rem;
    }
    
    .invoice-status i {
        margin-right: 0.5rem;
    }
    
    .invoice-actions {
        display: flex;
        gap: 0.5rem;
    }
    
    .provider-avatar {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        overflow: hidden;
        margin-right: 1rem;
    }
    
    .provider-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }
    
    .provider-avatar-placeholder {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 1.25rem;
        margin-right: 1rem;
    }
    
    .filters-container {
        background-color: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        margin-bottom: 1.5rem;
    }
    
    .empty-state {
        text-align: center;
        padding: 3rem 1.5rem;
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }
    
    .empty-state img {
        width: 150px;
        margin-bottom: 1.5rem;
        opacity: 0.7;
    }
    
    .empty-state h4 {
        margin-bottom: 1rem;
        font-weight: 600;
    }
    
    .empty-state p {
        color: #6b7280;
        margin-bottom: 1.5rem;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .payment-progress {
        height: 8px;
        background-color: #e5e7eb;
        border-radius: 4px;
        overflow: hidden;
        margin-top: 0.5rem;
    }
    
    .payment-progress-bar {
        height: 100%;
        border-radius: 4px;
    }
</style>
@endpush

@section('content')
<div class="filters-container">
    <div class="row align-items-center">
        <div class="col-lg-8 mb-3 mb-lg-0">
            <form action="{{ route('client.invoices') }}" method="GET" id="filter-form">
                <div class="row">
                    <div class="col-md-6 mb-3 mb-md-0">
                        <div class="input-group">
                            <input type="text" class="form-control" name="search" placeholder="Rechercher..." value="{{ request('search') }}" id="search-filter">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <select class="form-select" name="status" id="status-filter">
                            <option value="">Tous les statuts</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>En attente</option>
                            <option value="partially_paid" {{ request('status') == 'partially_paid' ? 'selected' : '' }}>Partiellement payées</option>
                            <option value="paid" {{ request('status') == 'paid' ? 'selected' : '' }}>Payées</option>
                            <option value="overdue" {{ request('status') == 'overdue' ? 'selected' : '' }}>En retard</option>
                        </select>
                    </div>
                </div>
            </form>
        </div>
        
        <div class="col-lg-4 text-lg-end">
            <div class="dropdown">
                <button class="btn btn-primary dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                    <i class="fas fa-download me-2"></i>Exporter
                </button>
                <ul class="dropdown-menu" aria-labelledby="exportDropdown">
                    <li><a class="dropdown-item" href="{{ route('client.invoices.export', ['format' => 'pdf']) }}"><i class="fas fa-file-pdf me-2 text-danger"></i>Exporter en PDF</a></li>
                    <li><a class="dropdown-item" href="{{ route('client.invoices.export', ['format' => 'csv']) }}"><i class="fas fa-file-csv me-2 text-success"></i>Exporter en CSV</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>

@if (session('status'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>{{ session('status') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

@if (session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

@if(count($invoices) > 0)
    <div class="row">
        @foreach($invoices as $invoice)
            <div class="col-lg-6 mb-4">
                <div class="card invoice-card">
                    <div class="card-header bg-{{ 
                        $invoice->status === 'paid' ? 'success' : 
                        ($invoice->status === 'partially_paid' ? 'info' : 
                        ($invoice->status === 'overdue' ? 'danger' : 'warning')) 
                    }} text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                @if($invoice->provider->logo)
                                    <div class="provider-avatar">
                                        <img src="{{ asset('storage/' . $invoice->provider->logo) }}" alt="{{ $invoice->provider->business_name }}">
                                    </div>
                                @else
                                    <div class="provider-avatar-placeholder bg-white text-{{ 
                                        $invoice->status === 'paid' ? 'success' : 
                                        ($invoice->status === 'partially_paid' ? 'info' : 
                                        ($invoice->status === 'overdue' ? 'danger' : 'warning')) 
                                    }}">
                                        {{ substr($invoice->provider->business_name, 0, 1) }}
                                    </div>
                                @endif
                                <div>
                                    <h5 class="mb-1">{{ $invoice->provider->business_name }}</h5>
                                    <p class="mb-0 opacity-75">{{ $invoice->booking->providerService->title }}</p>
                                </div>
                            </div>
                            <span class="invoice-number bg-white text-{{ 
                                $invoice->status === 'paid' ? 'success' : 
                                ($invoice->status === 'partially_paid' ? 'info' : 
                                ($invoice->status === 'overdue' ? 'danger' : 'warning')) 
                            }}">
                                {{ $invoice->invoice_number }}
                            </span>
                        </div>
                    </div>
                    
                    <div class="card-body">
                        <div class="invoice-info-item">
                            <div class="invoice-info-icon bg-light text-primary">
                                <i class="fas fa-bookmark"></i>
                            </div>
                            <div class="invoice-info-content">
                                <p class="invoice-info-label">Réservation</p>
                                <h6 class="invoice-info-value">{{ $invoice->booking->booking_number }}</h6>
                            </div>
                        </div>
                        
                        <div class="invoice-info-item">
                            <div class="invoice-info-icon bg-light text-primary">
                                <i class="fas fa-calendar-alt"></i>
                            </div>
                            <div class="invoice-info-content">
                                <p class="invoice-info-label">Dates</p>
                                <h6 class="invoice-info-value">
                                    Émise le {{ $invoice->created_at->format('d/m/Y') }}
                                    <span class="text-muted small d-block">Échéance le {{ $invoice->due_date->format('d/m/Y') }}</span>
                                </h6>
                            </div>
                        </div>
                        
                        <div class="invoice-info-item">
                            <div class="invoice-info-icon bg-light text-primary">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="invoice-info-content">
                                <p class="invoice-info-label">Montant</p>
                                <h6 class="invoice-info-value">
                                    {{ number_format($invoice->total_amount, 0, ',', ' ') }} Ar
                                    @if($invoice->paid_amount > 0)
                                        <span class="text-muted small d-block">
                                            Payé: {{ number_format($invoice->paid_amount, 0, ',', ' ') }} Ar 
                                            ({{ round(($invoice->paid_amount / $invoice->total_amount) * 100) }}%)
                                        </span>
                                        <div class="payment-progress">
                                            <div class="payment-progress-bar bg-{{ 
                                                $invoice->status === 'paid' ? 'success' : 
                                                ($invoice->status === 'partially_paid' ? 'info' : 
                                                ($invoice->status === 'overdue' ? 'danger' : 'warning')) 
                                            }}" style="width: {{ ($invoice->paid_amount / $invoice->total_amount) * 100 }}%"></div>
                                        </div>
                                    @endif
                                </h6>
                            </div>
                        </div>
                        
                        <div class="invoice-info-item">
                            <div class="invoice-info-icon bg-light text-primary">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div class="invoice-info-content">
                                <p class="invoice-info-label">Date de l'événement</p>
                                <h6 class="invoice-info-value">
                                    {{ $invoice->booking->event_date ? $invoice->booking->event_date->format('d/m/Y') : 'Non spécifiée' }}
                                </h6>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-footer d-flex justify-content-between align-items-center">
                        <div>
                            <span class="invoice-status bg-{{ 
                                $invoice->status === 'paid' ? 'success' : 
                                ($invoice->status === 'partially_paid' ? 'info' : 
                                ($invoice->status === 'overdue' ? 'danger' : 'warning')) 
                            }} bg-opacity-10 text-{{ 
                                $invoice->status === 'paid' ? 'success' : 
                                ($invoice->status === 'partially_paid' ? 'info' : 
                                ($invoice->status === 'overdue' ? 'danger' : 'warning')) 
                            }}">
                                <i class="fas fa-{{ 
                                    $invoice->status === 'paid' ? 'check-circle' : 
                                    ($invoice->status === 'partially_paid' ? 'circle-half-stroke' : 
                                    ($invoice->status === 'overdue' ? 'exclamation-circle' : 'clock')) 
                                }}"></i>
                                {{ $invoice->status === 'paid' ? 'Payée' : 
                                   ($invoice->status === 'partially_paid' ? 'Partiellement payée' : 
                                   ($invoice->status === 'overdue' ? 'En retard' : 'En attente')) 
                                }}
                            </span>
                        </div>
                        
                        <div class="invoice-actions">
                            <a href="{{ route('client.invoices.show', $invoice->id) }}" class="btn btn-primary">
                                <i class="fas fa-eye me-1"></i>Détails
                            </a>
                            
                            <a href="{{ route('client.invoices.download', $invoice->id) }}" class="btn btn-outline-secondary">
                                <i class="fas fa-download me-1"></i>PDF
                            </a>
                            
                            @if($invoice->status !== 'paid')
                                <a href="{{ route('client.payments.show', $invoice->booking_id) }}" class="btn btn-success">
                                    <i class="fas fa-credit-card me-1"></i>Payer
                                </a>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
    
    <div class="d-flex justify-content-center mt-4">
        {{ $invoices->links() }}
    </div>
@else
    <div class="empty-state">
        <img src="{{ asset('images/empty-invoices.svg') }}" alt="Aucune facture">
        <h4>Aucune facture trouvée</h4>
        <p>Vous n'avez pas encore de factures. Les factures seront générées automatiquement lorsque vous effectuerez des réservations.</p>
        <a href="{{ route('search') }}" class="btn btn-primary btn-lg">
            <i class="fas fa-search me-2"></i>Découvrir des prestataires
        </a>
    </div>
@endif
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-submit form when status filter changes
        const statusFilter = document.getElementById('status-filter');
        
        statusFilter.addEventListener('change', function() {
            document.getElementById('filter-form').submit();
        });
        
        // Submit form when pressing Enter in search field
        const searchFilter = document.getElementById('search-filter');
        
        searchFilter.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('filter-form').submit();
            }
        });
    });
</script>
@endpush
