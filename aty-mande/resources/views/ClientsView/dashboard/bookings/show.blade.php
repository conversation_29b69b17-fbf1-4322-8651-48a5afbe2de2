@extends('ClientsView.layouts.dashboard')

@section('title', 'Détails de la réservation')

@section('header-title', 'Détails de la réservation')

@section('content')
    @if (session('status'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('status') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-md-8">
            <!-- Booking Details -->
            <div class="dashboard-card mb-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bookmark me-2 text-primary"></i>
                        Réservation #{{ $booking->booking_number }}
                    </h5>
                    <div>
                        <span class="badge bg-{{
                            $booking->status === 'pending' ? 'warning' :
                            ($booking->status === 'confirmed' ? 'success' :
                            ($booking->status === 'completed' ? 'info' :
                            ($booking->status === 'cancelled' ? 'danger' : 'secondary')))
                        }} fs-6">
                            {{ $booking->status === 'pending' ? 'En attente' :
                               ($booking->status === 'confirmed' ? 'Confirmée' :
                               ($booking->status === 'completed' ? 'Terminée' :
                               ($booking->status === 'cancelled' ? 'Annulée' : 'Inconnue')))
                            }}
                        </span>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Prestataire</p>
                        <div class="d-flex align-items-center">
                            @if($booking->provider->logo)
                                <img src="{{ asset('storage/' . $booking->provider->logo) }}" alt="{{ $booking->provider->business_name }}" class="rounded-circle me-2" style="width: 40px; height: 40px; object-fit: cover;">
                            @else
                                <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;">
                                    {{ substr($booking->provider->business_name, 0, 1) }}
                                </div>
                            @endif
                            <div>
                                <h6 class="mb-0">{{ $booking->provider->business_name }}</h6>
                                <a href="{{ route('providers.show', $booking->provider->id) }}" class="text-decoration-none small">
                                    <i class="fas fa-external-link-alt me-1"></i>Voir le profil
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Service réservé</p>
                        @if($booking->providerService)
                            <h6>{{ $booking->providerService->title }}</h6>
                            <p class="small text-muted">
                                <i class="fas fa-tag me-1"></i>
                                @if($booking->providerService->category)
                                    {{ $booking->providerService->category->name }}
                                @else
                                    Catégorie non spécifiée
                                @endif
                            </p>
                        @else
                            <h6>Service non disponible</h6>
                            <p class="small text-muted">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                Ce service n'est plus disponible ou a été supprimé
                            </p>
                        @endif
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Date de l'événement</p>
                        <h6>{{ $booking->event_date ? $booking->event_date->format('d/m/Y') : 'Non spécifiée' }}</h6>
                        @if($booking->event_start_time)
                            <p class="small text-muted">
                                <i class="fas fa-clock me-1"></i>
                                {{ \Carbon\Carbon::parse($booking->event_start_time)->format('H:i') }}
                                @if($booking->event_end_time)
                                    - {{ \Carbon\Carbon::parse($booking->event_end_time)->format('H:i') }}
                                @endif
                            </p>
                        @endif
                    </div>
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Lieu de l'événement</p>
                        <h6>{{ $booking->event_location ?: 'Non spécifié' }}</h6>
                    </div>
                </div>

                <div class="mb-4">
                    <p class="text-muted mb-1">Description</p>
                    <div class="p-3 bg-light rounded">
                        {{ $booking->description ?: 'Aucune description fournie.' }}
                    </div>
                </div>

                <div class="mb-4">
                    <h6 class="fw-bold mb-3">Détails du paiement</h6>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tbody>
                                <tr>
                                    <td>Montant total</td>
                                    <td class="text-end fw-bold">{{ number_format($booking->total_amount, 0, ',', ' ') }} Ar</td>
                                </tr>
                                <tr>
                                    <td>Statut du paiement</td>
                                    <td class="text-end">
                                        <span class="badge bg-{{ $booking->payment_status === 'paid' ? 'success' : 'warning' }}">
                                            {{ $booking->payment_status === 'paid' ? 'Payé' : 'En attente' }}
                                        </span>
                                    </td>
                                </tr>
                                @if($booking->payment_date)
                                    <tr>
                                        <td>Date de paiement</td>
                                        <td class="text-end">{{ $booking->payment_date->format('d/m/Y') }}</td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="{{ route('client.bookings') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                    </a>

                    <div>
                        @if($booking->payment_status !== 'paid')
                            <a href="{{ route('client.payments.show', $booking->id) }}" class="btn btn-success me-2">
                                <i class="fas fa-credit-card me-2"></i>Effectuer un paiement
                            </a>
                        @endif

                        @if($booking->invoices()->exists())
                            <a href="{{ route('client.invoices.show', $booking->invoices()->latest()->first()->id) }}" class="btn btn-outline-primary me-2">
                                <i class="fas fa-file-invoice me-2"></i>Voir la facture
                            </a>
                        @endif

                        @if($booking->status === 'pending')
                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#cancelBookingModal">
                                <i class="fas fa-times me-2"></i>Annuler la réservation
                            </button>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Messages -->
            <div class="dashboard-card mb-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-comments me-2 text-primary"></i>
                        Messages
                    </h5>
                </div>

                <div class="messages-container mb-4" style="max-height: 400px; overflow-y: auto;">
                    @if(isset($messages) && count($messages) > 0)
                        @foreach($messages as $message)
                            <div class="message-item d-flex mb-3 {{ $message->sender_id === Auth::id() ? 'justify-content-end' : '' }}">
                                <div class="message-content {{ $message->sender_id === Auth::id() ? 'bg-primary text-white' : 'bg-light' }}" style="max-width: 80%; border-radius: 10px; padding: 10px 15px;">
                                    <div class="message-text">
                                        {{ $message->message }}
                                    </div>
                                    <div class="message-meta d-flex justify-content-between align-items-center mt-2">
                                        <small class="{{ $message->sender_id === Auth::id() ? 'text-white-50' : 'text-muted' }}">
                                            {{ $message->sender_id === Auth::id() ? 'Vous' : $booking->provider->business_name }}
                                        </small>
                                        <small class="{{ $message->sender_id === Auth::id() ? 'text-white-50' : 'text-muted' }}">
                                            {{ $message->created_at->format('d/m/Y H:i') }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <img src="https://cdn-icons-png.flaticon.com/512/6598/6598519.png" alt="No messages" style="width: 80px; opacity: 0.5;" class="mb-3">
                            <p class="text-muted">Aucun message pour le moment</p>
                        </div>
                    @endif
                </div>

                @if(in_array($booking->status, ['pending', 'confirmed']))
                    <form action="{{ route('client.bookings.message', $booking->id) }}" method="POST">
                        @csrf
                        <div class="input-group">
                            <input type="text" class="form-control" name="message" placeholder="Tapez votre message..." required>
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </form>
                @endif
            </div>
        </div>

        <div class="col-md-4">
            <!-- Provider Info -->
            <div class="dashboard-card mb-4">
                <div class="text-center mb-4">
                    @if($booking->provider->logo)
                        <img src="{{ asset('storage/' . $booking->provider->logo) }}" alt="{{ $booking->provider->business_name }}" class="rounded-circle mb-3" style="width: 100px; height: 100px; object-fit: cover;">
                    @else
                        <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 100px; height: 100px; font-size: 2.5rem;">
                            {{ substr($booking->provider->business_name, 0, 1) }}
                        </div>
                    @endif
                    <h5>{{ $booking->provider->business_name }}</h5>
                    <p class="text-muted">
                        <i class="fas fa-map-marker-alt me-1"></i>
                        {{ $booking->provider->city }}, {{ $booking->provider->country }}
                    </p>
                </div>

                <div class="mb-3">
                    <div class="d-flex align-items-center mb-2">
                        <div class="bg-light rounded-circle p-2 me-3">
                            <i class="fas fa-phone text-primary"></i>
                        </div>
                        <div>{{ $booking->provider->business_phone ?: $booking->provider->user->phone }}</div>
                    </div>
                    <div class="d-flex align-items-center mb-2">
                        <div class="bg-light rounded-circle p-2 me-3">
                            <i class="fas fa-envelope text-primary"></i>
                        </div>
                        <div>{{ $booking->provider->business_email ?: $booking->provider->user->email }}</div>
                    </div>
                    @if($booking->provider->website)
                        <div class="d-flex align-items-center">
                            <div class="bg-light rounded-circle p-2 me-3">
                                <i class="fas fa-globe text-primary"></i>
                            </div>
                            <div>
                                <a href="{{ $booking->provider->website }}" target="_blank" class="text-decoration-none">
                                    {{ $booking->provider->website }}
                                </a>
                            </div>
                        </div>
                    @endif
                </div>

                <div class="d-grid gap-2">
                    <a href="{{ route('providers.show', $booking->provider->id) }}" class="btn btn-outline-primary">
                        <i class="fas fa-user me-2"></i>Voir le profil complet
                    </a>
                </div>
            </div>

            <!-- Timeline -->
            <div class="dashboard-card mb-4">
                <h5 class="card-title mb-3">
                    <i class="fas fa-history me-2 text-primary"></i>
                    Historique
                </h5>

                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="mb-0">Réservation créée</h6>
                            <p class="small text-muted mb-0">{{ $booking->created_at->format('d/m/Y H:i') }}</p>
                        </div>
                    </div>

                    @if($booking->status === 'confirmed' || $booking->status === 'completed' || $booking->status === 'cancelled')
                        <div class="timeline-item">
                            <div class="timeline-marker {{ $booking->status === 'cancelled' ? 'bg-danger' : 'bg-success' }}"></div>
                            <div class="timeline-content">
                                <h6 class="mb-0">{{ $booking->status === 'cancelled' ? 'Réservation annulée' : 'Réservation confirmée' }}</h6>
                                <p class="small text-muted mb-0">{{ $booking->updated_at->format('d/m/Y H:i') }}</p>
                            </div>
                        </div>
                    @endif

                    @if($booking->status === 'completed')
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="mb-0">Service terminé</h6>
                                <p class="small text-muted mb-0">{{ $booking->completed_at ? $booking->completed_at->format('d/m/Y H:i') : $booking->updated_at->format('d/m/Y H:i') }}</p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Cancel Booking Modal -->
    @if($booking->status === 'pending')
        <div class="modal fade" id="cancelBookingModal" tabindex="-1" aria-labelledby="cancelBookingModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title" id="cancelBookingModalLabel">
                            <i class="fas fa-times-circle me-2"></i>Annuler la réservation
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form action="{{ route('client.bookings.cancel', $booking->id) }}" method="POST">
                        @csrf
                        <div class="modal-body">
                            <div class="alert alert-warning">
                                <div class="d-flex">
                                    <div class="me-3">
                                        <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                                    </div>
                                    <div>
                                        <h5 class="alert-heading">Confirmation d'annulation</h5>
                                        <p>Êtes-vous sûr de vouloir annuler cette réservation ? Cette action est irréversible.</p>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="cancellation_reason" class="form-label">Raison de l'annulation</label>
                                <textarea class="form-control" id="cancellation_reason" name="cancellation_reason" rows="3" placeholder="Expliquez pourquoi vous annulez cette réservation..." required></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-times me-2"></i>Confirmer l'annulation
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif
@endsection

@push('styles')
<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }

    .timeline:before {
        content: '';
        position: absolute;
        left: 10px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #e9ecef;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }

    .timeline-marker {
        position: absolute;
        left: -30px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #6c757d;
        border: 3px solid #fff;
        box-shadow: 0 0 0 2px #e9ecef;
    }

    .timeline-content {
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }
</style>
@endpush
