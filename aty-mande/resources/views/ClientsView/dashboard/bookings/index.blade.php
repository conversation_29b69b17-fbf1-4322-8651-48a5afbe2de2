@extends('ClientsView.layouts.dashboard')

@section('title', 'Mes réservations')

@section('header-title', 'Mes réservations')

@push('styles')
<style>
    .booking-card {
        transition: all 0.3s ease;
        border-radius: 12px;
        overflow: hidden;
        margin-bottom: 1.5rem;
        border: none;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .booking-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }

    .booking-card .card-header {
        padding: 1.25rem 1.5rem;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
    }

    .booking-card .card-body {
        padding: 1.5rem;
    }

    .booking-card .card-footer {
        padding: 1.25rem 1.5rem;
        background-color: transparent;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
    }

    .booking-number {
        font-family: monospace;
        font-size: 0.9rem;
        padding: 0.35rem 0.75rem;
        border-radius: 6px;
        background-color: #f3f4f6;
        color: #1f2937;
    }

    .booking-date {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        margin-right: 1rem;
    }

    .booking-date-day {
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1;
    }

    .booking-date-month {
        font-size: 0.75rem;
        text-transform: uppercase;
        margin-top: 0.25rem;
    }

    .booking-info-item {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .booking-info-item:last-child {
        margin-bottom: 0;
    }

    .booking-info-icon {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1rem;
    }

    .booking-info-content {
        flex: 1;
    }

    .booking-info-label {
        font-size: 0.85rem;
        color: #6b7280;
        margin-bottom: 0.25rem;
    }

    .booking-info-value {
        font-weight: 500;
        margin: 0;
    }

    .booking-status {
        display: inline-flex;
        align-items: center;
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-weight: 500;
        font-size: 0.85rem;
    }

    .booking-status i {
        margin-right: 0.5rem;
    }

    .booking-actions {
        display: flex;
        gap: 0.5rem;
    }

    .provider-avatar {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        overflow: hidden;
        margin-right: 1rem;
    }

    .provider-avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
    }

    .provider-avatar-placeholder {
        width: 50px;
        height: 50px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-weight: 600;
        font-size: 1.25rem;
        margin-right: 1rem;
    }

    .filters-container {
        background-color: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        margin-bottom: 1.5rem;
    }

    .empty-state {
        text-align: center;
        padding: 3rem 1.5rem;
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .empty-state img {
        width: 150px;
        margin-bottom: 1.5rem;
        opacity: 0.7;
    }

    .empty-state h4 {
        margin-bottom: 1rem;
        font-weight: 600;
    }

    .empty-state p {
        color: #6b7280;
        margin-bottom: 1.5rem;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }
</style>
@endpush

@section('content')
<div class="filters-container">
    <div class="row align-items-center">
        <div class="col-lg-8 mb-3 mb-lg-0">
            <form action="{{ route('client.bookings') }}" method="GET" id="filter-form">
                <div class="row">
                    <div class="col-md-6 mb-3 mb-md-0">
                        <div class="input-group">
                            <input type="text" class="form-control" name="search" placeholder="Rechercher..." value="{{ request('search') }}" id="search-filter">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>

                    <div class="col-md-6">
                        <select class="form-select" name="status" id="status-filter">
                            <option value="">Tous les statuts</option>
                            <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>En attente</option>
                            <option value="confirmed" {{ request('status') == 'confirmed' ? 'selected' : '' }}>Confirmées</option>
                            <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Terminées</option>
                            <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Annulées</option>
                        </select>
                    </div>
                </div>
            </form>
        </div>

        <div class="col-lg-4 text-lg-end">
            <a href="{{ route('search') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Nouvelle réservation
            </a>
        </div>
    </div>
</div>

@if (session('status'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>{{ session('status') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

@if (session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

@if(count($bookings) > 0)
    <div class="row">
        @foreach($bookings as $booking)
            <div class="col-lg-6 mb-4">
                <div class="card booking-card">
                    <div class="card-header bg-{{
                        $booking->status === 'pending' ? 'warning' :
                        ($booking->status === 'confirmed' ? 'success' :
                        ($booking->status === 'completed' ? 'info' : 'secondary'))
                    }} text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                @if($booking->event_date)
                                    <div class="booking-date">
                                        <span class="booking-date-day">{{ \Carbon\Carbon::parse($booking->event_date)->format('d') }}</span>
                                        <span class="booking-date-month">{{ \Carbon\Carbon::parse($booking->event_date)->format('M') }}</span>
                                    </div>
                                @endif
                                <div>
                                    <h5 class="mb-1">
                                        @if($booking->providerService)
                                            {{ Str::limit($booking->providerService->title, 30) }}
                                        @else
                                            Service non spécifié
                                        @endif
                                    </h5>
                                    <p class="mb-0 opacity-75">
                                        {{ $booking->event_date ? $booking->event_date->format('d/m/Y') : 'Date non spécifiée' }}
                                        @if($booking->event_start_time)
                                            • {{ \Carbon\Carbon::parse($booking->event_start_time)->format('H:i') }}
                                            @if($booking->event_end_time)
                                                - {{ \Carbon\Carbon::parse($booking->event_end_time)->format('H:i') }}
                                            @endif
                                        @endif
                                    </p>
                                </div>
                            </div>
                            <span class="booking-number">
                                #{{ $booking->booking_number }}
                            </span>
                        </div>
                    </div>

                    <div class="card-body">
                        <div class="booking-info-item">
                            <div class="booking-info-icon bg-light text-primary">
                                <i class="fas fa-building"></i>
                            </div>
                            <div class="booking-info-content">
                                <p class="booking-info-label">Prestataire</p>
                                <div class="d-flex align-items-center">
                                    @if($booking->provider->logo)
                                        <img src="{{ asset('storage/' . $booking->provider->logo) }}" alt="{{ $booking->provider->business_name }}" class="rounded-circle me-2" style="width: 24px; height: 24px; object-fit: cover;">
                                    @else
                                        <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-2" style="width: 24px; height: 24px; font-size: 0.8rem;">
                                            {{ substr($booking->provider->business_name, 0, 1) }}
                                        </div>
                                    @endif
                                    <h6 class="booking-info-value mb-0">{{ $booking->provider->business_name }}</h6>
                                </div>
                            </div>
                        </div>

                        <div class="booking-info-item">
                            <div class="booking-info-icon bg-light text-primary">
                                <i class="fas fa-tag"></i>
                            </div>
                            <div class="booking-info-content">
                                <p class="booking-info-label">Catégorie</p>
                                <h6 class="booking-info-value">
                                    @if($booking->providerService && $booking->providerService->category)
                                        {{ $booking->providerService->category->name }}
                                    @else
                                        Catégorie non spécifiée
                                    @endif
                                </h6>
                            </div>
                        </div>

                        <div class="booking-info-item">
                            <div class="booking-info-icon bg-light text-primary">
                                <i class="fas fa-money-bill-wave"></i>
                            </div>
                            <div class="booking-info-content">
                                <p class="booking-info-label">Montant total</p>
                                <h6 class="booking-info-value">{{ number_format($booking->total_amount, 0, ',', ' ') }} Ar</h6>
                            </div>
                        </div>

                        <div class="booking-info-item">
                            <div class="booking-info-icon bg-light text-primary">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div class="booking-info-content">
                                <p class="booking-info-label">Date de réservation</p>
                                <h6 class="booking-info-value">{{ $booking->created_at->format('d/m/Y') }}</h6>
                            </div>
                        </div>
                    </div>

                    <div class="card-footer d-flex justify-content-between align-items-center">
                        <div>
                            <span class="booking-status bg-{{
                                $booking->status === 'pending' ? 'warning' :
                                ($booking->status === 'confirmed' ? 'success' :
                                ($booking->status === 'completed' ? 'info' : 'secondary'))
                            }} bg-opacity-10 text-{{
                                $booking->status === 'pending' ? 'warning' :
                                ($booking->status === 'confirmed' ? 'success' :
                                ($booking->status === 'completed' ? 'info' : 'secondary'))
                            }}">
                                <i class="fas fa-{{
                                    $booking->status === 'pending' ? 'clock' :
                                    ($booking->status === 'confirmed' ? 'check-circle' :
                                    ($booking->status === 'completed' ? 'calendar-check' : 'ban'))
                                }}"></i>
                                {{ $booking->status === 'pending' ? 'En attente' :
                                   ($booking->status === 'confirmed' ? 'Confirmée' :
                                   ($booking->status === 'completed' ? 'Terminée' : 'Annulée'))
                                }}
                            </span>
                        </div>

                        <div class="booking-actions">
                            <a href="{{ route('client.bookings.show', $booking->id) }}" class="btn btn-primary">
                                <i class="fas fa-eye me-1"></i>Détails
                            </a>

                            @if($booking->status === 'pending')
                                <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#cancelModal{{ $booking->id }}">
                                    <i class="fas fa-times me-1"></i>Annuler
                                </button>
                            @endif
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cancel Modal -->
            @if($booking->status === 'pending')
                <div class="modal fade" id="cancelModal{{ $booking->id }}" tabindex="-1" aria-labelledby="cancelModalLabel{{ $booking->id }}" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="cancelModalLabel{{ $booking->id }}">Annuler la réservation</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <form action="{{ route('client.bookings.cancel', $booking->id) }}" method="POST">
                                @csrf
                                <div class="modal-body">
                                    <div class="text-center mb-4">
                                        <i class="fas fa-exclamation-triangle text-warning fa-3x mb-3"></i>
                                        <h5>Êtes-vous sûr de vouloir annuler cette réservation ?</h5>
                                        <p class="text-muted">Cette action est irréversible et le prestataire sera notifié de l'annulation.</p>
                                    </div>

                                    <div class="alert alert-secondary">
                                        <div class="d-flex align-items-center mb-2">
                                            <strong class="me-2">Prestataire:</strong> {{ $booking->provider->business_name }}
                                        </div>
                                        <div class="d-flex align-items-center mb-2">
                                            <strong class="me-2">Service:</strong>
                                            @if($booking->providerService)
                                                {{ $booking->providerService->title }}
                                            @else
                                                Service non spécifié
                                            @endif
                                        </div>
                                        <div class="d-flex align-items-center mb-2">
                                            <strong class="me-2">Date:</strong> {{ $booking->event_date ? $booking->event_date->format('d/m/Y') : 'Non spécifiée' }}
                                        </div>
                                        <div class="d-flex align-items-center">
                                            <strong class="me-2">Montant:</strong> {{ number_format($booking->total_amount, 0, ',', ' ') }} Ar
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <label for="cancellation_reason" class="form-label">Raison de l'annulation</label>
                                        <textarea class="form-control" id="cancellation_reason" name="cancellation_reason" rows="3" placeholder="Expliquez pourquoi vous annulez cette réservation..." required></textarea>
                                    </div>
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Retour</button>
                                    <button type="submit" class="btn btn-danger">
                                        <i class="fas fa-times me-2"></i>Confirmer l'annulation
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            @endif
        @endforeach
    </div>

    <div class="d-flex justify-content-center mt-4">
        {{ $bookings->links() }}
    </div>
@else
    <div class="empty-state">
        <img src="{{ asset('images/empty-bookings.svg') }}" alt="Aucune réservation">
        <h4>Aucune réservation trouvée</h4>
        <p>Vous n'avez pas encore de réservations. Parcourez nos prestataires pour trouver des services pour votre événement.</p>
        <a href="{{ route('search') }}" class="btn btn-primary btn-lg">
            <i class="fas fa-search me-2"></i>Découvrir des prestataires
        </a>
    </div>
@endif
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Auto-submit form when status filter changes
        const statusFilter = document.getElementById('status-filter');

        statusFilter.addEventListener('change', function() {
            document.getElementById('filter-form').submit();
        });

        // Submit form when pressing Enter in search field
        const searchFilter = document.getElementById('search-filter');

        searchFilter.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('filter-form').submit();
            }
        });
    });
</script>
@endpush
