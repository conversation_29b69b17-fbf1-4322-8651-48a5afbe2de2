@extends('ClientsView.layouts.dashboard')

@section('title', 'Tableau de bord')

@section('header-title', 'Tableau de bord')

@push('styles')
<style>
    .welcome-banner {
        background: linear-gradient(135deg, #4361ee 0%, #3a56d4 100%);
        border-radius: 0.5rem;
        padding: 2rem;
        margin-bottom: 1.5rem;
        position: relative;
        overflow: hidden;
    }

    .welcome-banner::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 300px;
        height: 100%;
        background-image: url("{{ asset('images/pattern.svg') }}");
        background-repeat: no-repeat;
        background-position: right center;
        opacity: 0.1;
    }

    .welcome-title {
        color: white;
        font-weight: 600;
        margin-bottom: 0.5rem;
    }

    .welcome-text {
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 1.5rem;
        max-width: 600px;
    }

    .task-item {
        display: flex;
        align-items: center;
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: white;
        margin-bottom: 0.75rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .task-item:hover {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    .task-checkbox {
        margin-right: 1rem;
    }

    .task-content {
        flex: 1;
    }

    .task-title {
        font-weight: 500;
        margin-bottom: 0.25rem;
    }

    .task-meta {
        display: flex;
        align-items: center;
        gap: 1rem;
        font-size: 0.85rem;
        color: #6b7280;
    }

    .task-date, .task-category {
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .task-actions {
        display: flex;
        gap: 0.5rem;
    }

    .event-progress-card {
        height: 100%;
    }

    .progress-item {
        margin-bottom: 1.25rem;
    }

    .progress-item:last-child {
        margin-bottom: 0;
    }

    .progress-title {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    .progress-bar-container {
        height: 8px;
        background-color: #e5e7eb;
        border-radius: 4px;
        overflow: hidden;
    }

    .progress-bar-fill {
        height: 100%;
        border-radius: 4px;
    }

    .upcoming-event-card {
        border-left: 4px solid #4361ee;
    }

    .event-date-badge {
        width: 50px;
        height: 50px;
        background-color: #f3f4f6;
        border-radius: 0.5rem;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
    }

    .event-date-day {
        font-size: 1.25rem;
        font-weight: 700;
        line-height: 1;
        color: #111827;
    }

    .event-date-month {
        font-size: 0.75rem;
        color: #6b7280;
        text-transform: uppercase;
    }

    /* Styles pour les prestataires recommandés */
    .provider-card {
        transition: all 0.3s ease;
    }

    .provider-card:hover {
        background-color: #f9fafb;
        transform: translateY(-2px);
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }

    .avatar-placeholder {
        font-weight: 600;
    }

    /* Styles pour l'activité récente */
    .activity-timeline {
        position: relative;
    }

    .activity-item {
        position: relative;
    }

    .icon-circle {
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.9rem;
    }

    .activity-content {
        flex: 1;
    }
</style>
@endpush

@section('content')
<div class="welcome-banner">
    <h1 class="welcome-title">Bienvenue, {{ Auth::user()->name }} !</h1>
    <p class="welcome-text">Voici un aperçu de vos activités sur Planifeo. Gérez vos événements et organisez vos réservations en un seul endroit.</p>
    <a href="{{ route('client.events.create') }}" class="btn btn-light">
        <i class="fas fa-plus me-2"></i>Créer un nouvel événement
    </a>
</div>

<div class="row">
    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card">
            <div class="stat-card">
                <div class="stat-icon bg-primary">
                    <i class="fas fa-calendar-alt text-white"></i>
                </div>
                <div class="stat-content">
                    <h3 class="stat-value">{{ $stats['events'] ?? 0 }}</h3>
                    <p class="stat-label">Événements</p>
                </div>
            </div>
        </div>
    </div>



    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card">
            <div class="stat-card">
                <div class="stat-icon bg-success">
                    <i class="fas fa-bookmark text-white"></i>
                </div>
                <div class="stat-content">
                    <h3 class="stat-value">{{ $stats['bookings'] ?? 0 }}</h3>
                    <p class="stat-label">Réservations</p>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-6 col-lg-3 mb-4">
        <div class="card">
            <div class="stat-card">
                <div class="stat-icon bg-warning">
                    <i class="fas fa-file-invoice-dollar text-white"></i>
                </div>
                <div class="stat-content">
                    <h3 class="stat-value">{{ $stats['invoices'] ?? 0 }}</h3>
                    <p class="stat-label">Factures</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Prestataires recommandés</h5>
            </div>
            <div class="card-body">
                <div class="recommended-providers">
                    @if(isset($recommendedProviders) && count($recommendedProviders) > 0)
                        @foreach($recommendedProviders as $provider)
                            <div class="provider-card d-flex align-items-center mb-3 p-3 border rounded">
                                <div class="provider-avatar me-3">
                                    @if($provider->logo)
                                        <img src="{{ asset('storage/' . $provider->logo) }}" alt="{{ $provider->name }}" class="rounded-circle" width="50" height="50">
                                    @else
                                        <div class="avatar-placeholder rounded-circle bg-primary text-white d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;">
                                            {{ substr($provider->name, 0, 1) }}
                                        </div>
                                    @endif
                                </div>
                                <div class="provider-info flex-grow-1">
                                    <h6 class="mb-1">{{ $provider->name }}</h6>
                                    <p class="mb-0 text-muted small">
                                        <i class="fas fa-tag me-1"></i>{{ $provider->category }}
                                        <span class="mx-2">|</span>
                                        <i class="fas fa-star me-1 text-warning"></i>{{ number_format($provider->rating, 1) }}
                                    </p>
                                </div>
                                <a href="{{ route('providers.show', $provider->id) }}" class="btn btn-sm btn-outline-primary">Voir</a>
                            </div>
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <img src="{{ asset('images/empty-providers.svg') }}" alt="Aucun prestataire" class="mb-3" style="width: 120px; opacity: 0.5;">
                            <h6>Aucun prestataire recommandé</h6>
                            <p class="text-muted">Découvrez des prestataires pour vos événements</p>
                            <a href="{{ route('search') }}" class="btn btn-primary">
                                <i class="fas fa-search me-2"></i>Trouver des prestataires
                            </a>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Activité récente</h5>
            </div>
            <div class="card-body">
                <div class="activity-timeline">
                    @if(isset($recentActivities) && count($recentActivities) > 0)
                        @foreach($recentActivities as $activity)
                            <div class="activity-item d-flex mb-3">
                                <div class="activity-icon me-3">
                                    <div class="icon-circle bg-{{ $activity->type_color }} text-white">
                                        <i class="fas fa-{{ $activity->icon }}"></i>
                                    </div>
                                </div>
                                <div class="activity-content">
                                    <p class="mb-1">{{ $activity->description }}</p>
                                    <small class="text-muted">{{ $activity->created_at->diffForHumans() }}</small>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <img src="{{ asset('images/empty-activity.svg') }}" alt="Aucune activité" class="mb-3" style="width: 120px; opacity: 0.5;">
                            <h6>Aucune activité récente</h6>
                            <p class="text-muted">Vos activités récentes apparaîtront ici</p>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">Événements à venir</h5>
                <a href="{{ route('client.events') }}" class="btn btn-sm btn-link">Voir tout</a>
            </div>
            <div class="card-body">
                @if(isset($upcomingEvents) && count($upcomingEvents) > 0)
                    @foreach($upcomingEvents as $event)
                        <div class="card mb-3 upcoming-event-card">
                            <div class="card-body d-flex align-items-center">
                                <div class="event-date-badge">
                                    <span class="event-date-day">{{ $event->date->format('d') }}</span>
                                    <span class="event-date-month">{{ $event->date->format('M') }}</span>
                                </div>
                                <div>
                                    <h6 class="mb-1">{{ $event->name }}</h6>
                                    <p class="text-muted mb-0 small">
                                        <i class="fas fa-map-marker-alt me-1"></i>{{ $event->location }}
                                    </p>
                                </div>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="text-center py-4">
                        <img src="{{ asset('images/empty-events.svg') }}" alt="Aucun événement" class="mb-3" style="width: 120px; opacity: 0.5;">
                        <h6>Aucun événement à venir</h6>
                        <p class="text-muted">Planifiez votre prochain événement dès maintenant</p>
                        <a href="{{ route('client.events.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>Créer un événement
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>


</div>

@endsection

@push('scripts')
<script>
    // Scripts spécifiques au tableau de bord
    document.addEventListener('DOMContentLoaded', function() {
        // Initialiser les tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function(tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });
</script>
@endpush
