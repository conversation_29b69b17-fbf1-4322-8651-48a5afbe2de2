@extends('ClientsView.layouts.dashboard')

@section('title', 'Paiement de réservation')

@section('header-title', 'Paiement de réservation')

@section('content')
    @if (session('status'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('status') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if (session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            {{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-md-8">
            <div class="dashboard-card mb-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-credit-card me-2 text-primary"></i>
                        Paiement de la réservation #{{ $booking->booking_number }}
                    </h5>
                    <span class="badge bg-{{ $booking->isPaid() ? 'success' : 'warning' }} fs-6">
                        {{ $booking->isPaid() ? 'Payé' : 'En attente de paiement' }}
                    </span>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Prestataire</p>
                        <div class="d-flex align-items-center">
                            @if($booking->provider->logo)
                                <img src="{{ asset('storage/' . $booking->provider->logo) }}" alt="{{ $booking->provider->business_name }}" class="rounded-circle me-2" style="width: 40px; height: 40px; object-fit: cover;">
                            @else
                                <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;">
                                    {{ substr($booking->provider->business_name, 0, 1) }}
                                </div>
                            @endif
                            <div>
                                <h6 class="mb-0">{{ $booking->provider->business_name }}</h6>
                                <a href="{{ route('providers.show', $booking->provider->id) }}" class="text-decoration-none small">
                                    <i class="fas fa-external-link-alt me-1"></i>Voir le profil
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Service réservé</p>
                        <h6>{{ $booking->providerService->title }}</h6>
                        <p class="small text-muted">
                            <i class="fas fa-tag me-1"></i>
                            {{ $booking->providerService->category->name }}
                        </p>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Date de l'événement</p>
                        <h6>{{ $booking->event_date ? $booking->event_date->format('d/m/Y') : 'Non spécifiée' }}</h6>
                        @if($booking->event_start_time)
                            <p class="small text-muted">
                                <i class="fas fa-clock me-1"></i>
                                {{ \Carbon\Carbon::parse($booking->event_start_time)->format('H:i') }}
                                @if($booking->event_end_time)
                                    - {{ \Carbon\Carbon::parse($booking->event_end_time)->format('H:i') }}
                                @endif
                            </p>
                        @endif
                    </div>
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Lieu de l'événement</p>
                        <h6>{{ $booking->event_location ?: 'Non spécifié' }}</h6>
                    </div>
                </div>

                <div class="mb-4">
                    <h6 class="fw-bold mb-3">Détails du paiement</h6>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tbody>
                                <tr>
                                    <td>Montant total</td>
                                    <td class="text-end fw-bold">{{ number_format($booking->total_amount, 0, ',', ' ') }} Ar</td>
                                </tr>
                                <tr>
                                    <td>Montant payé</td>
                                    <td class="text-end">{{ number_format($booking->paid_amount, 0, ',', ' ') }} Ar</td>
                                </tr>
                                <tr class="table-light">
                                    <td><strong>Solde restant</strong></td>
                                    <td class="text-end fw-bold">{{ number_format($booking->total_amount - $booking->paid_amount, 0, ',', ' ') }} Ar</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

                @if($booking->total_amount > $booking->paid_amount)
                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Choisissez votre méthode de paiement</h6>
                        
                        <ul class="nav nav-tabs" id="paymentTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="mobile-money-tab" data-bs-toggle="tab" data-bs-target="#mobile-money" type="button" role="tab" aria-controls="mobile-money" aria-selected="true">
                                    <i class="fas fa-mobile-alt me-2"></i>Mobile Money
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="bank-card-tab" data-bs-toggle="tab" data-bs-target="#bank-card" type="button" role="tab" aria-controls="bank-card" aria-selected="false">
                                    <i class="fas fa-credit-card me-2"></i>Carte bancaire
                                </button>
                            </li>
                        </ul>
                        
                        <div class="tab-content p-4 border border-top-0 rounded-bottom" id="paymentTabsContent">
                            <!-- Mobile Money Tab -->
                            <div class="tab-pane fade show active" id="mobile-money" role="tabpanel" aria-labelledby="mobile-money-tab">
                                <div class="row mb-4">
                                    @foreach($mobileMoneyMethods as $method)
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100 payment-method-card" data-method-id="{{ $method->id }}">
                                                <div class="card-body text-center">
                                                    @if($method->logo)
                                                        <img src="{{ asset('storage/' . $method->logo) }}" alt="{{ $method->name }}" class="img-fluid mb-3" style="height: 60px;">
                                                    @else
                                                        <div class="bg-light rounded p-3 mb-3">
                                                            <i class="fas fa-mobile-alt fa-3x text-primary"></i>
                                                        </div>
                                                    @endif
                                                    <h6 class="card-title">{{ $method->name }}</h6>
                                                    <p class="card-text small text-muted">{{ $method->description }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                                
                                <form action="{{ route('client.payments.mobile-money', $booking->id) }}" method="POST" id="mobileMoneyForm">
                                    @csrf
                                    <input type="hidden" name="payment_method_id" id="mobileMoneyMethodId">
                                    
                                    <div class="mb-3">
                                        <label for="transaction_id" class="form-label">Numéro de transaction</label>
                                        <input type="text" class="form-control" id="transaction_id" name="transaction_id" required>
                                        <div class="form-text">Entrez le numéro de transaction que vous avez reçu après avoir effectué le paiement.</div>
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="mobile_money_amount" class="form-label">Montant</label>
                                        <div class="input-group">
                                            <input type="number" class="form-control" id="mobile_money_amount" name="amount" min="1" max="{{ $booking->total_amount - $booking->paid_amount }}" value="{{ $booking->total_amount - $booking->paid_amount }}" required>
                                            <span class="input-group-text">Ar</span>
                                        </div>
                                    </div>
                                    
                                    <div class="alert alert-info payment-instructions d-none mb-3">
                                        <h6 class="alert-heading"><i class="fas fa-info-circle me-2"></i>Instructions de paiement</h6>
                                        <p class="mb-0" id="paymentInstructions"></p>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-check me-2"></i>Confirmer le paiement
                                        </button>
                                    </div>
                                </form>
                            </div>
                            
                            <!-- Bank Card Tab -->
                            <div class="tab-pane fade" id="bank-card" role="tabpanel" aria-labelledby="bank-card-tab">
                                <div class="row mb-4">
                                    @foreach($bankCardMethods as $method)
                                        <div class="col-md-4 mb-3">
                                            <div class="card h-100 payment-method-card-bank" data-method-id="{{ $method->id }}">
                                                <div class="card-body text-center">
                                                    @if($method->logo)
                                                        <img src="{{ asset('storage/' . $method->logo) }}" alt="{{ $method->name }}" class="img-fluid mb-3" style="height: 60px;">
                                                    @else
                                                        <div class="bg-light rounded p-3 mb-3">
                                                            <i class="fas fa-credit-card fa-3x text-primary"></i>
                                                        </div>
                                                    @endif
                                                    <h6 class="card-title">{{ $method->name }}</h6>
                                                    <p class="card-text small text-muted">{{ $method->description }}</p>
                                                </div>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                                
                                <form action="{{ route('client.payments.bank-card', $booking->id) }}" method="POST" id="bankCardForm">
                                    @csrf
                                    <input type="hidden" name="payment_method_id" id="bankCardMethodId">
                                    
                                    <div class="mb-3">
                                        <label for="card_number" class="form-label">Numéro de carte</label>
                                        <input type="text" class="form-control" id="card_number" name="card_number" placeholder="1234 5678 9012 3456" required>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="card_holder" class="form-label">Titulaire de la carte</label>
                                            <input type="text" class="form-control" id="card_holder" name="card_holder" placeholder="NOM PRÉNOM" required>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="expiry_month" class="form-label">Mois d'expiration</label>
                                            <select class="form-select" id="expiry_month" name="expiry_month" required>
                                                @for($i = 1; $i <= 12; $i++)
                                                    <option value="{{ $i }}">{{ sprintf('%02d', $i) }}</option>
                                                @endfor
                                            </select>
                                        </div>
                                        <div class="col-md-3">
                                            <label for="expiry_year" class="form-label">Année d'expiration</label>
                                            <select class="form-select" id="expiry_year" name="expiry_year" required>
                                                @for($i = date('Y'); $i <= date('Y') + 10; $i++)
                                                    <option value="{{ $i }}">{{ $i }}</option>
                                                @endfor
                                            </select>
                                        </div>
                                    </div>
                                    
                                    <div class="row mb-3">
                                        <div class="col-md-6">
                                            <label for="cvv" class="form-label">Code de sécurité (CVV)</label>
                                            <input type="text" class="form-control" id="cvv" name="cvv" placeholder="123" required>
                                        </div>
                                        <div class="col-md-6">
                                            <label for="bank_card_amount" class="form-label">Montant</label>
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="bank_card_amount" name="amount" min="1" max="{{ $booking->total_amount - $booking->paid_amount }}" value="{{ $booking->total_amount - $booking->paid_amount }}" required>
                                                <span class="input-group-text">Ar</span>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="d-grid">
                                        <button type="submit" class="btn btn-primary">
                                            <i class="fas fa-check me-2"></i>Procéder au paiement
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                @else
                    <div class="alert alert-success mb-4">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-check-circle fa-2x text-success"></i>
                            </div>
                            <div>
                                <h5 class="alert-heading">Paiement complet</h5>
                                <p class="mb-0">Vous avez entièrement payé cette réservation. Merci pour votre confiance !</p>
                            </div>
                        </div>
                    </div>
                @endif

                <div class="d-flex justify-content-between">
                    <a href="{{ route('client.bookings.show', $booking->id) }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour à la réservation
                    </a>
                    
                    @if($booking->invoices()->exists())
                        <a href="{{ route('client.invoices.show', $booking->invoices()->latest()->first()->id) }}" class="btn btn-outline-primary">
                            <i class="fas fa-file-invoice me-2"></i>Voir la facture
                        </a>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="dashboard-card mb-4">
                <h5 class="card-title mb-3">
                    <i class="fas fa-history me-2 text-primary"></i>
                    Historique des paiements
                </h5>
                
                @if(count($booking->payments) > 0)
                    <div class="timeline">
                        @foreach($booking->payments as $payment)
                            <div class="timeline-item">
                                <div class="timeline-marker bg-success"></div>
                                <div class="timeline-content">
                                    <div class="d-flex justify-content-between">
                                        <h6 class="mb-0">{{ number_format($payment->amount, 0, ',', ' ') }} Ar</h6>
                                        <span class="badge bg-success">{{ $payment->status }}</span>
                                    </div>
                                    <p class="small text-muted mb-1">{{ $payment->paymentMethod->name }}</p>
                                    <p class="small text-muted mb-0">{{ $payment->paid_at->format('d/m/Y H:i') }}</p>
                                </div>
                            </div>
                        @endforeach
                    </div>
                @else
                    <div class="text-center py-4">
                        <img src="https://cdn-icons-png.flaticon.com/512/6598/6598519.png" alt="No payments" style="width: 80px; opacity: 0.5;" class="mb-3">
                        <p class="text-muted">Aucun paiement effectué</p>
                    </div>
                @endif
            </div>

            <div class="dashboard-card mb-4">
                <h5 class="card-title mb-3">
                    <i class="fas fa-shield-alt me-2 text-primary"></i>
                    Paiement sécurisé
                </h5>
                
                <div class="text-center py-3">
                    <i class="fas fa-lock fa-3x text-success mb-3"></i>
                    <h6>Vos paiements sont sécurisés</h6>
                    <p class="small text-muted mb-0">Toutes les transactions sont cryptées et sécurisées.</p>
                </div>
                
                <hr>
                
                <div class="d-flex justify-content-center">
                    <i class="fab fa-cc-visa fa-2x text-primary mx-2"></i>
                    <i class="fab fa-cc-mastercard fa-2x text-primary mx-2"></i>
                    <i class="fas fa-mobile-alt fa-2x text-primary mx-2"></i>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('styles')
<style>
    .payment-method-card, .payment-method-card-bank {
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
    }
    
    .payment-method-card:hover, .payment-method-card-bank:hover {
        border-color: #dee2e6;
        transform: translateY(-5px);
    }
    
    .payment-method-card.selected, .payment-method-card-bank.selected {
        border-color: #0d6efd;
        background-color: rgba(13, 110, 253, 0.05);
    }
    
    .timeline {
        position: relative;
        padding-left: 30px;
    }
    
    .timeline:before {
        content: '';
        position: absolute;
        left: 10px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #e9ecef;
    }
    
    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }
    
    .timeline-marker {
        position: absolute;
        left: -30px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #6c757d;
        border: 3px solid #fff;
        box-shadow: 0 0 0 2px #e9ecef;
    }
    
    .timeline-content {
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }
</style>
@endpush

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Mobile Money payment method selection
        const mobileMoneyCards = document.querySelectorAll('.payment-method-card');
        const mobileMoneyMethodIdInput = document.getElementById('mobileMoneyMethodId');
        const paymentInstructions = document.getElementById('paymentInstructions');
        const instructionsAlert = document.querySelector('.payment-instructions');
        
        mobileMoneyCards.forEach(card => {
            card.addEventListener('click', function() {
                // Remove selected class from all cards
                mobileMoneyCards.forEach(c => c.classList.remove('selected'));
                
                // Add selected class to clicked card
                this.classList.add('selected');
                
                // Set the payment method ID
                const methodId = this.dataset.methodId;
                mobileMoneyMethodIdInput.value = methodId;
                
                // Show payment instructions
                instructionsAlert.classList.remove('d-none');
                
                // Set payment instructions based on the selected method
                const methodName = this.querySelector('.card-title').textContent;
                
                @foreach($mobileMoneyMethods as $method)
                    if (methodId == {{ $method->id }}) {
                        const config = @json(json_decode($method->config, true));
                        paymentInstructions.innerHTML = `
                            1. Composez *{{ substr($method->provider, 0, 3) }}# sur votre téléphone<br>
                            2. Sélectionnez "Payer un marchand"<br>
                            3. Entrez le numéro marchand: ${config.merchant_number}<br>
                            4. Entrez le montant: ${document.getElementById('mobile_money_amount').value} Ar<br>
                            5. Confirmez avec votre code PIN<br>
                            6. Entrez le numéro de transaction reçu par SMS dans le champ ci-dessus
                        `;
                    }
                @endforeach
            });
        });
        
        // Bank Card payment method selection
        const bankCardCards = document.querySelectorAll('.payment-method-card-bank');
        const bankCardMethodIdInput = document.getElementById('bankCardMethodId');
        
        bankCardCards.forEach(card => {
            card.addEventListener('click', function() {
                // Remove selected class from all cards
                bankCardCards.forEach(c => c.classList.remove('selected'));
                
                // Add selected class to clicked card
                this.classList.add('selected');
                
                // Set the payment method ID
                bankCardMethodIdInput.value = this.dataset.methodId;
            });
        });
        
        // Format card number input
        const cardNumberInput = document.getElementById('card_number');
        if (cardNumberInput) {
            cardNumberInput.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                if (value.length > 16) {
                    value = value.substr(0, 16);
                }
                
                // Add spaces every 4 digits
                let formattedValue = '';
                for (let i = 0; i < value.length; i++) {
                    if (i > 0 && i % 4 === 0) {
                        formattedValue += ' ';
                    }
                    formattedValue += value[i];
                }
                
                e.target.value = formattedValue;
            });
        }
        
        // Format CVV input
        const cvvInput = document.getElementById('cvv');
        if (cvvInput) {
            cvvInput.addEventListener('input', function(e) {
                let value = e.target.value.replace(/\D/g, '');
                if (value.length > 4) {
                    value = value.substr(0, 4);
                }
                e.target.value = value;
            });
        }
        
        // Validate forms before submission
        const mobileMoneyForm = document.getElementById('mobileMoneyForm');
        if (mobileMoneyForm) {
            mobileMoneyForm.addEventListener('submit', function(e) {
                if (!mobileMoneyMethodIdInput.value) {
                    e.preventDefault();
                    alert('Veuillez sélectionner une méthode de paiement.');
                }
            });
        }
        
        const bankCardForm = document.getElementById('bankCardForm');
        if (bankCardForm) {
            bankCardForm.addEventListener('submit', function(e) {
                if (!bankCardMethodIdInput.value) {
                    e.preventDefault();
                    alert('Veuillez sélectionner une méthode de paiement.');
                }
            });
        }
    });
</script>
@endpush
