@extends('ClientsView.layouts.dashboard')

@section('title', $event->title)

@section('header-title', 'Détails de l\'événement')

@push('styles')
<style>
    .event-header {
        background: linear-gradient(135deg,
            {{ $event->status == 'planning' ? '#4361ee, #3a56d4' :
              ($event->status == 'confirmed' ? '#0dcaf0, #0aa2c0' :
              ($event->status == 'completed' ? '#10b981, #0e9f6e' : '#6c757d, #5a6268')) }});
        border-radius: 12px;
        padding: 2rem;
        margin-bottom: 1.5rem;
        position: relative;
        overflow: hidden;
        color: white;
    }

    .event-header::before {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 300px;
        height: 100%;
        background-image: url("{{ asset('images/pattern.svg') }}");
        background-repeat: no-repeat;
        background-position: right center;
        opacity: 0.1;
    }

    .event-type-badge {
        position: absolute;
        top: 1.5rem;
        right: 1.5rem;
        padding: 0.5rem 1rem;
        border-radius: 50px;
        font-size: 0.85rem;
        font-weight: 500;
        background-color: rgba(255, 255, 255, 0.2);
        backdrop-filter: blur(5px);
    }

    .event-info-card {
        height: 100%;
        border-radius: 12px;
        overflow: hidden;
        border: none;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        transition: all 0.3s ease;
    }

    .event-info-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 15px rgba(0, 0, 0, 0.1);
    }

    .event-info-item {
        display: flex;
        align-items: center;
        margin-bottom: 1rem;
    }

    .event-info-item:last-child {
        margin-bottom: 0;
    }

    .event-info-icon {
        width: 40px;
        height: 40px;
        border-radius: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 1rem;
        font-size: 1.1rem;
    }

    .event-info-content {
        flex: 1;
    }

    .event-info-label {
        font-size: 0.85rem;
        color: #6b7280;
        margin-bottom: 0.25rem;
    }

    .event-info-value {
        font-weight: 500;
        margin: 0;
    }

    .progress-card {
        border-radius: 12px;
        overflow: hidden;
        border: none;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        margin-bottom: 1.5rem;
    }

    .progress-item {
        margin-bottom: 1.5rem;
    }

    .progress-item:last-child {
        margin-bottom: 0;
    }

    .progress-title {
        display: flex;
        justify-content: space-between;
        margin-bottom: 0.5rem;
        font-weight: 500;
    }

    .progress-bar-container {
        height: 10px;
        background-color: #e5e7eb;
        border-radius: 5px;
        overflow: hidden;
    }

    .progress-bar-fill {
        height: 100%;
        border-radius: 5px;
    }

    .task-item {
        display: flex;
        align-items: flex-start;
        padding: 1rem;
        border-radius: 0.5rem;
        background-color: white;
        margin-bottom: 0.75rem;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        transition: all 0.3s ease;
    }

    .task-item:hover {
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
    }

    .task-checkbox {
        margin-right: 1rem;
        margin-top: 0.25rem;
    }

    .task-content {
        flex: 1;
    }

    .task-title {
        font-weight: 500;
        margin-bottom: 0.25rem;
    }

    .task-meta {
        display: flex;
        align-items: center;
        gap: 1rem;
        font-size: 0.85rem;
        color: #6b7280;
    }

    .task-date, .task-category, .task-budget {
        display: flex;
        align-items: center;
        gap: 0.25rem;
    }

    .task-actions {
        display: flex;
        gap: 0.5rem;
    }

    .budget-chart-container {
        position: relative;
        height: 250px;
        margin-bottom: 1.5rem;
    }

    .budget-legend {
        display: flex;
        flex-wrap: wrap;
        gap: 1rem;
        margin-top: 1rem;
    }

    .budget-legend-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.85rem;
    }

    .budget-legend-color {
        width: 12px;
        height: 12px;
        border-radius: 3px;
    }

    .budget-summary {
        display: flex;
        justify-content: space-between;
        margin-top: 1.5rem;
        padding-top: 1.5rem;
        border-top: 1px solid #e5e7eb;
    }

    .budget-summary-item {
        text-align: center;
    }

    .budget-summary-value {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 0.25rem;
    }

    .budget-summary-label {
        font-size: 0.85rem;
        color: #6b7280;
    }

    .action-card {
        border-radius: 12px;
        overflow: hidden;
        border: none;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        margin-bottom: 1.5rem;
    }

    .action-btn {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        padding: 0.75rem 1rem;
        border-radius: 0.5rem;
        transition: all 0.3s ease;
        margin-bottom: 0.75rem;
    }

    .action-btn:last-child {
        margin-bottom: 0;
    }

    .action-btn:hover {
        transform: translateX(5px);
    }

    .action-icon {
        width: 36px;
        height: 36px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1rem;
    }

    .action-content {
        flex: 1;
    }

    .action-title {
        font-weight: 500;
        margin-bottom: 0.25rem;
    }

    .action-description {
        font-size: 0.85rem;
        color: #6b7280;
        margin: 0;
    }
</style>
@endpush

@section('content')
<div class="container-fluid">
    <!-- Breadcrumb et boutons d'action -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb mb-0">
                <li class="breadcrumb-item"><a href="{{ route('client.events') }}">Mes événements</a></li>
                <li class="breadcrumb-item active" aria-current="page">{{ $event->title }}</li>
            </ol>
        </nav>

        <div class="btn-group">
            <a href="{{ route('client.events.edit', $event->id) }}" class="btn btn-outline-primary">
                <i class="fas fa-edit me-2"></i>Modifier
            </a>
            <button type="button" class="btn btn-outline-danger" data-bs-toggle="modal" data-bs-target="#deleteEventModal">
                <i class="fas fa-trash-alt me-2"></i>Supprimer
            </button>
        </div>
    </div>

    @if(session('status'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>{{ session('status') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    @if(session('error'))
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <!-- En-tête de l'événement -->
    <div class="event-header">
        <span class="event-type-badge">{{ $event->eventType->name }}</span>
        <h2 class="mb-3">{{ $event->title }}</h2>
        <div class="row">
            <div class="col-md-6">
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-calendar-day me-2"></i>
                    <span>{{ \Carbon\Carbon::parse($event->date)->format('d/m/Y') }}</span>
                    @if($event->start_time)
                        <span class="ms-2">
                            {{ \Carbon\Carbon::parse($event->start_time)->format('H:i') }}
                            @if($event->end_time)
                                - {{ \Carbon\Carbon::parse($event->end_time)->format('H:i') }}
                            @endif
                        </span>
                    @endif
                </div>
                <div class="d-flex align-items-center mb-2">
                    <i class="fas fa-map-marker-alt me-2"></i>
                    <span>{{ $event->location }}</span>
                </div>
            </div>
            <div class="col-md-6">
                @if($event->guest_count)
                    <div class="d-flex align-items-center mb-2">
                        <i class="fas fa-users me-2"></i>
                        <span>{{ $event->guest_count }} invités</span>
                    </div>
                @endif
                @if($event->budget)
                    <div class="d-flex align-items-center">
                        <i class="fas fa-money-bill-wave me-2"></i>
                        <span>Budget: {{ number_format($event->budget, 0, ',', ' ') }} Ar</span>
                    </div>
                @endif
            </div>
        </div>

        @php
            $progressPercentage = $event->getOverallProgressPercentage();
        @endphp
        <div class="mt-4">
            <div class="d-flex justify-content-between mb-2">
                <span>Progression globale</span>
                <span>{{ $progressPercentage }}%</span>
            </div>
            <div class="progress-bar-container">
                <div class="progress-bar-fill bg-white" style="width: {{ $progressPercentage }}%"></div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <!-- Informations détaillées -->
            <div class="card event-info-card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Informations détaillées</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">Informations générales</h6>
                            <div class="event-info-item">
                                <div class="event-info-icon bg-light text-primary">
                                    <i class="fas fa-calendar-day"></i>
                                </div>
                                <div class="event-info-content">
                                    <p class="event-info-label">Date de l'événement</p>
                                    <h6 class="event-info-value">{{ \Carbon\Carbon::parse($event->date)->format('d/m/Y') }}</h6>
                                </div>
                            </div>

                            @if($event->start_time)
                                <div class="event-info-item">
                                    <div class="event-info-icon bg-light text-primary">
                                        <i class="fas fa-clock"></i>
                                    </div>
                                    <div class="event-info-content">
                                        <p class="event-info-label">Horaires</p>
                                        <h6 class="event-info-value">
                                            {{ \Carbon\Carbon::parse($event->start_time)->format('H:i') }}
                                            @if($event->end_time)
                                                - {{ \Carbon\Carbon::parse($event->end_time)->format('H:i') }}
                                            @endif
                                        </h6>
                                    </div>
                                </div>
                            @endif

                            <div class="event-info-item">
                                <div class="event-info-icon bg-light text-primary">
                                    <i class="fas fa-map-marker-alt"></i>
                                </div>
                                <div class="event-info-content">
                                    <p class="event-info-label">Lieu</p>
                                    <h6 class="event-info-value">{{ $event->location }}</h6>
                                </div>
                            </div>

                            @if($event->guest_count)
                                <div class="event-info-item">
                                    <div class="event-info-icon bg-light text-primary">
                                        <i class="fas fa-users"></i>
                                    </div>
                                    <div class="event-info-content">
                                        <p class="event-info-label">Nombre d'invités</p>
                                        <h6 class="event-info-value">{{ $event->guest_count }} personnes</h6>
                                    </div>
                                </div>
                            @endif
                        </div>

                        <div class="col-md-6">
                            <h6 class="fw-bold mb-3">Adresse complète</h6>
                            @if($event->address || $event->city || $event->state || $event->postal_code || $event->country)
                                <div class="event-info-item">
                                    <div class="event-info-icon bg-light text-primary">
                                        <i class="fas fa-building"></i>
                                    </div>
                                    <div class="event-info-content">
                                        <p class="event-info-label">Adresse</p>
                                        <h6 class="event-info-value">
                                            @if($event->address)
                                                {{ $event->address }}<br>
                                            @endif
                                            @if($event->city || $event->postal_code)
                                                {{ $event->postal_code }} {{ $event->city }}<br>
                                            @endif
                                            @if($event->state)
                                                {{ $event->state }}<br>
                                            @endif
                                            @if($event->country)
                                                {{ $event->country }}
                                            @endif
                                        </h6>
                                    </div>
                                </div>
                            @else
                                <p class="text-muted">Aucune adresse détaillée fournie</p>
                            @endif

                            <div class="event-info-item">
                                <div class="event-info-icon bg-{{ $event->status == 'planning' ? 'primary' : ($event->status == 'confirmed' ? 'info' : ($event->status == 'completed' ? 'success' : 'secondary')) }} text-white">
                                    <i class="fas fa-tag"></i>
                                </div>
                                <div class="event-info-content">
                                    <p class="event-info-label">Statut</p>
                                    <h6 class="event-info-value">
                                        {{ $event->status == 'planning' ? 'En planification' : ($event->status == 'confirmed' ? 'Confirmé' : ($event->status == 'completed' ? 'Terminé' : 'Annulé')) }}
                                    </h6>
                                </div>
                            </div>

                            <div class="event-info-item">
                                <div class="event-info-icon bg-light text-primary">
                                    <i class="fas fa-calendar-check"></i>
                                </div>
                                <div class="event-info-content">
                                    <p class="event-info-label">Dates importantes</p>
                                    <h6 class="event-info-value">
                                        Créé le {{ $event->created_at->format('d/m/Y') }}<br>
                                        <span class="small text-muted">Dernière mise à jour le {{ $event->updated_at->format('d/m/Y') }}</span>
                                    </h6>
                                </div>
                            </div>
                        </div>
                    </div>

                    @if($event->description)
                        <div class="mt-4">
                            <h6 class="fw-bold mb-2">Description</h6>
                            <p>{{ $event->description }}</p>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Répartition du budget -->
            <div class="card event-info-card mb-4">
                <div class="card-header">
                    <h5 class="card-title mb-0">Répartition du budget</h5>
                </div>
                <div class="card-body">
                    @if($event->budget)
                        <div class="budget-chart-container">
                            <canvas id="budgetDistributionChart"></canvas>
                        </div>

                        <div class="budget-legend">
                            <div class="budget-legend-item">
                                <div class="budget-legend-color" style="background-color: #4361ee;"></div>
                                <span>Lieu</span>
                            </div>
                            <div class="budget-legend-item">
                                <div class="budget-legend-color" style="background-color: #f72585;"></div>
                                <span>Traiteur</span>
                            </div>
                            <div class="budget-legend-item">
                                <div class="budget-legend-color" style="background-color: #10b981;"></div>
                                <span>Décoration</span>
                            </div>
                            <div class="budget-legend-item">
                                <div class="budget-legend-color" style="background-color: #3b82f6;"></div>
                                <span>Musique</span>
                            </div>
                            <div class="budget-legend-item">
                                <div class="budget-legend-color" style="background-color: #f59e0b;"></div>
                                <span>Transport</span>
                            </div>
                            <div class="budget-legend-item">
                                <div class="budget-legend-color" style="background-color: #6b7280;"></div>
                                <span>Autres</span>
                            </div>
                        </div>

                        <div class="budget-summary">
                            <div class="budget-summary-item">
                                <div class="budget-summary-value">{{ number_format($event->budget, 0, ',', ' ') }} Ar</div>
                                <div class="budget-summary-label">Budget total</div>
                            </div>
                            <div class="budget-summary-item">
                                <div class="budget-summary-value">{{ number_format($event->getBudgetUsed(), 0, ',', ' ') }} Ar</div>
                                <div class="budget-summary-label">Dépensé</div>
                            </div>
                            <div class="budget-summary-item">
                                <div class="budget-summary-value {{ $event->getRemainingBudget() < 0 ? 'text-danger' : 'text-success' }}">
                                    {{ number_format($event->getRemainingBudget(), 0, ',', ' ') }} Ar
                                </div>
                                <div class="budget-summary-label">Restant</div>
                            </div>
                            <div class="budget-summary-item">
                                <div class="budget-summary-value">{{ $event->getBudgetPercentage() }}%</div>
                                <div class="budget-summary-label">Utilisé</div>
                            </div>
                        </div>
                    @else
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>Aucun budget n'a été défini pour cet événement.
                            <a href="{{ route('client.events.edit', $event->id) }}" class="alert-link">Définir un budget</a>
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <!-- Progression de l'événement -->
            <div class="card progress-card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-line me-2"></i>Progression
                    </h5>
                </div>
                <div class="card-body">
                    <div class="progress-item">
                        <div class="progress-title">
                            <span>Progression globale</span>
                            <span>{{ $progressPercentage }}%</span>
                        </div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill {{ $progressPercentage < 30 ? 'bg-danger' : ($progressPercentage < 70 ? 'bg-warning' : 'bg-success') }}" style="width: {{ $progressPercentage }}%"></div>
                        </div>
                    </div>

                    <div class="progress-item">
                        <div class="progress-title">
                            <span>Tâches complétées</span>
                            <span>{{ $event->getTasksCompletionPercentage() }}%</span>
                        </div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill {{ $event->getTasksCompletionPercentage() < 30 ? 'bg-danger' : ($event->getTasksCompletionPercentage() < 70 ? 'bg-warning' : 'bg-success') }}" style="width: {{ $event->getTasksCompletionPercentage() }}%"></div>
                        </div>
                        <div class="small text-muted mt-1">
                            {{ $event->tasks()->where('status', 'completed')->count() }} sur {{ $event->tasks()->count() }} tâches complétées
                        </div>
                    </div>

                    @if($event->budget)
                        <div class="progress-item">
                            <div class="progress-title">
                                <span>Budget utilisé</span>
                                <span>{{ $event->getBudgetPercentage() }}%</span>
                            </div>
                            <div class="progress-bar-container">
                                <div class="progress-bar-fill {{ $event->getBudgetPercentage() > 90 ? 'bg-danger' : ($event->getBudgetPercentage() > 70 ? 'bg-warning' : 'bg-success') }}" style="width: {{ $event->getBudgetPercentage() }}%"></div>
                            </div>
                        </div>
                    @endif

                    <div class="progress-item">
                        <div class="progress-title">
                            <span>Temps restant</span>
                            @php
                                $daysRemaining = now()->diffInDays($event->date, false);
                            @endphp
                        </div>
                        @if($daysRemaining < 0)
                            <div class="alert alert-secondary mb-0">
                                <i class="fas fa-calendar-check me-2"></i>Événement passé ({{ abs($daysRemaining) }} jours)
                            </div>
                        @elseif($daysRemaining == 0)
                            <div class="alert alert-danger mb-0">
                                <i class="fas fa-exclamation-circle me-2"></i>C'est aujourd'hui!
                            </div>
                        @elseif($daysRemaining <= 7)
                            <div class="alert alert-warning mb-0">
                                <i class="fas fa-hourglass-half me-2"></i>{{ $daysRemaining }} jour(s) restant(s)
                            </div>
                        @else
                            <div class="alert alert-info mb-0">
                                <i class="fas fa-calendar-alt me-2"></i>{{ $daysRemaining }} jours restants
                            </div>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Actions rapides -->
            <div class="card action-card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Actions rapides</h5>
                </div>
                <div class="card-body">
                    <a href="{{ route('search') }}?event_id={{ $event->id }}" class="action-btn btn btn-light">
                        <div class="action-icon bg-primary text-white">
                            <i class="fas fa-search"></i>
                        </div>
                        <div class="action-content">
                            <h6 class="action-title">Rechercher des prestataires</h6>
                            <p class="action-description">Trouvez des prestataires pour votre événement</p>
                        </div>
                    </a>

                    <a href="{{ route('client.events.service-suggestions', $event->id) }}" class="action-btn btn btn-light">
                        <div class="action-icon bg-info text-white">
                            <i class="fas fa-lightbulb"></i>
                        </div>
                        <div class="action-content">
                            <h6 class="action-title">Suggestions de services</h6>
                            <p class="action-description">Découvrez des services recommandés</p>
                        </div>
                    </a>

                    <a href="{{ route('client.events.edit', $event->id) }}" class="action-btn btn btn-light">
                        <div class="action-icon bg-secondary text-white">
                            <i class="fas fa-edit"></i>
                        </div>
                        <div class="action-content">
                            <h6 class="action-title">Modifier l'événement</h6>
                            <p class="action-description">Mettre à jour les informations</p>
                        </div>
                    </a>

                    @if($event->status == 'planning' || $event->status == 'confirmed')
                        <button type="button" class="action-btn btn btn-light" data-bs-toggle="modal" data-bs-target="#completeEventModal">
                            <div class="action-icon bg-success text-white">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="action-content">
                                <h6 class="action-title">Marquer comme terminé</h6>
                                <p class="action-description">Finaliser cet événement</p>
                            </div>
                        </button>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialisation du graphique de répartition du budget
        const budgetChartEl = document.getElementById('budgetDistributionChart');
        if (budgetChartEl) {
            const ctx = budgetChartEl.getContext('2d');

            // Récupération des données de budget par catégorie
            @php
                $budgetData = [
                    'venue' => $event->tasks()->where('category', 'venue')->sum('budget'),
                    'food' => $event->tasks()->where('category', 'food')->sum('budget'),
                    'decoration' => $event->tasks()->where('category', 'decoration')->sum('budget'),
                    'music' => $event->tasks()->where('category', 'music')->sum('budget'),
                    'transportation' => $event->tasks()->where('category', 'transportation')->sum('budget'),
                    'other' => $event->tasks()->whereNotIn('category', ['venue', 'food', 'decoration', 'music', 'transportation'])->sum('budget')
                ];

                $totalBudgetAllocated = array_sum($budgetData);
                $unallocatedBudget = max(0, $event->budget - $totalBudgetAllocated);

                if ($unallocatedBudget > 0) {
                    $budgetData['unallocated'] = $unallocatedBudget;
                }
            @endphp

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: [
                        'Lieu',
                        'Traiteur',
                        'Décoration',
                        'Musique',
                        'Transport',
                        'Autres'
                        @if($unallocatedBudget > 0)
                        , 'Non alloué'
                        @endif
                    ],
                    datasets: [{
                        data: [
                            {{ $budgetData['venue'] }},
                            {{ $budgetData['food'] }},
                            {{ $budgetData['decoration'] }},
                            {{ $budgetData['music'] }},
                            {{ $budgetData['transportation'] }},
                            {{ $budgetData['other'] }}
                            @if($unallocatedBudget > 0)
                            , {{ $unallocatedBudget }}
                            @endif
                        ],
                        backgroundColor: [
                            '#4361ee',
                            '#f72585',
                            '#10b981',
                            '#3b82f6',
                            '#f59e0b',
                            '#6b7280'
                            @if($unallocatedBudget > 0)
                            , '#e5e7eb'
                            @endif
                        ],
                        borderWidth: 0,
                        hoverOffset: 5
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    cutout: '70%',
                    plugins: {
                        legend: {
                            display: false
                        },
                        tooltip: {
                            callbacks: {
                                label: function(context) {
                                    const label = context.label || '';
                                    const value = context.raw || 0;
                                    const percentage = Math.round((value / {{ $event->budget ?: 1 }}) * 100);
                                    return `${label}: ${value.toLocaleString('fr-FR')} Ar (${percentage}%)`;
                                }
                            }
                        }
                    }
                }
            });
        }
    });
</script>
@endpush
