@extends('ClientsView.layouts.dashboard')

@section('title', 'Suggestions de services pour votre événement')

@section('header-title', 'Suggestions de services pour votre événement')

@section('content')
<div class="container-fluid">
    @if (session('status'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('status') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="row mb-4">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('client.events') }}">Mes événements</a></li>
                    <li class="breadcrumb-item"><a href="{{ route('client.events.show', $event->id) }}">{{ $event->title }}</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Suggestions de services</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card border-0 shadow-sm">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>
                        Services recommandés pour votre {{ $event->eventType->name }}
                    </h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <div class="d-flex">
                            <div class="me-3">
                                <i class="fas fa-info-circle fa-2x text-info"></i>
                            </div>
                            <div>
                                <h5 class="alert-heading">Comment ça fonctionne ?</h5>
                                <p class="mb-0">Nous avons sélectionné des services qui correspondent à votre type d'événement. Sélectionnez ceux qui vous intéressent et nous les ajouterons comme tâches à votre événement.</p>
                            </div>
                        </div>
                    </div>

                    <form action="{{ route('client.events.service-tasks', $event->id) }}" method="POST">
                        @csrf
                        
                        @if($suggestedCategories->isEmpty())
                            <div class="alert alert-warning">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                Aucune suggestion de service n'est disponible pour ce type d'événement.
                            </div>
                        @else
                            <div class="row mb-3">
                                <div class="col-md-12 text-end">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>Ajouter les services sélectionnés comme tâches
                                    </button>
                                </div>
                            </div>

                            <div class="accordion" id="suggestedServicesAccordion">
                                @foreach($suggestedCategories as $index => $category)
                                    <div class="accordion-item">
                                        <h2 class="accordion-header" id="heading{{ $category->id }}">
                                            <button class="accordion-button {{ $index > 0 ? 'collapsed' : '' }}" type="button" data-bs-toggle="collapse" data-bs-target="#collapse{{ $category->id }}" aria-expanded="{{ $index === 0 ? 'true' : 'false' }}" aria-controls="collapse{{ $category->id }}">
                                                <div class="d-flex align-items-center w-100">
                                                    <i class="fas fa-{{ $category->icon }} me-2"></i>
                                                    <span class="fw-bold">{{ $category->name }}</span>
                                                    @if($category->pivot->is_required)
                                                        <span class="badge bg-danger ms-2">Recommandé</span>
                                                    @endif
                                                    <span class="ms-auto small text-muted">{{ isset($categoryProviders[$category->id]) ? count($categoryProviders[$category->id]) : 0 }} prestataires</span>
                                                </div>
                                            </button>
                                        </h2>
                                        <div id="collapse{{ $category->id }}" class="accordion-collapse collapse {{ $index === 0 ? 'show' : '' }}" aria-labelledby="heading{{ $category->id }}" data-bs-parent="#suggestedServicesAccordion">
                                            <div class="accordion-body">
                                                <p class="text-muted mb-3">{{ $category->pivot->description }}</p>
                                                
                                                @if(isset($categoryProviders[$category->id]) && $categoryProviders[$category->id]->isNotEmpty())
                                                    <div class="row">
                                                        @foreach($categoryProviders[$category->id] as $provider)
                                                            <div class="col-md-6 mb-4">
                                                                <div class="card h-100">
                                                                    <div class="card-header bg-light">
                                                                        <div class="d-flex align-items-center">
                                                                            <div class="provider-logo me-3">
                                                                                @if($provider->logo_url)
                                                                                    <img src="{{ asset('storage/' . $provider->logo_url) }}" alt="{{ $provider->business_name }}" class="rounded-circle" width="40" height="40">
                                                                                @else
                                                                                    <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                                                                        {{ substr($provider->business_name, 0, 1) }}
                                                                                    </div>
                                                                                @endif
                                                                            </div>
                                                                            <div>
                                                                                <h6 class="mb-0">{{ $provider->business_name }}</h6>
                                                                                <div class="mt-1">
                                                                                    @if($provider->reviews->count() > 0)
                                                                                        <div class="rating small">
                                                                                            @php
                                                                                                $avgRating = $provider->reviews->avg('rating');
                                                                                            @endphp
                                                                                            @for($i = 1; $i <= 5; $i++)
                                                                                                @if($i <= $avgRating)
                                                                                                    <i class="fas fa-star text-warning"></i>
                                                                                                @elseif($i - 0.5 <= $avgRating)
                                                                                                    <i class="fas fa-star-half-alt text-warning"></i>
                                                                                                @else
                                                                                                    <i class="far fa-star text-warning"></i>
                                                                                                @endif
                                                                                            @endfor
                                                                                            <span class="text-muted">({{ $provider->reviews->count() }})</span>
                                                                                        </div>
                                                                                    @else
                                                                                        <span class="text-muted small">Aucun avis</span>
                                                                                    @endif
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                    <div class="card-body">
                                                                        @if($provider->services->isNotEmpty())
                                                                            <div class="list-group">
                                                                                @foreach($provider->services as $service)
                                                                                    <div class="list-group-item list-group-item-action">
                                                                                        <div class="d-flex w-100 justify-content-between align-items-center">
                                                                                            <div class="form-check">
                                                                                                <input class="form-check-input" type="checkbox" name="selected_services[]" value="{{ $service->id }}" id="service{{ $service->id }}">
                                                                                                <label class="form-check-label" for="service{{ $service->id }}">
                                                                                                    {{ $service->title }}
                                                                                                </label>
                                                                                            </div>
                                                                                            <span class="badge bg-primary">{{ $service->getDisplayPrice() }}</span>
                                                                                        </div>
                                                                                        <p class="mb-1 small text-muted">{{ Str::limit($service->description, 100) }}</p>
                                                                                        <div class="mt-2">
                                                                                            <a href="{{ route('providers.service', ['providerId' => $provider->id, 'serviceId' => $service->id]) }}" class="btn btn-sm btn-outline-primary" target="_blank">
                                                                                                <i class="fas fa-eye me-1"></i>Voir détails
                                                                                            </a>
                                                                                            <a href="{{ route('providers.quote', ['id' => $provider->id, 'serviceId' => $service->id]) }}" class="btn btn-sm btn-outline-secondary" target="_blank">
                                                                                                <i class="fas fa-file-invoice me-1"></i>Demander un devis
                                                                                            </a>
                                                                                        </div>
                                                                                    </div>
                                                                                @endforeach
                                                                            </div>
                                                                        @else
                                                                            <p class="text-muted">Aucun service disponible pour ce prestataire.</p>
                                                                        @endif
                                                                    </div>
                                                                    <div class="card-footer bg-white">
                                                                        <a href="{{ route('providers.show', $provider->id) }}" class="btn btn-sm btn-outline-primary w-100" target="_blank">
                                                                            <i class="fas fa-user me-1"></i>Voir le profil complet
                                                                        </a>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        @endforeach
                                                    </div>
                                                @else
                                                    <div class="alert alert-warning">
                                                        <i class="fas fa-exclamation-triangle me-2"></i>
                                                        Aucun prestataire disponible pour cette catégorie.
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                            
                            <div class="row mt-4">
                                <div class="col-md-12 d-flex justify-content-between">
                                    <a href="{{ route('client.events.show', $event->id) }}" class="btn btn-outline-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>Retour à l'événement
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-plus me-2"></i>Ajouter les services sélectionnés comme tâches
                                    </button>
                                </div>
                            </div>
                        @endif
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Sélectionner tous les checkboxes
        const selectAllBtns = document.querySelectorAll('.select-all-btn');
        
        selectAllBtns.forEach(btn => {
            btn.addEventListener('click', function() {
                const categoryId = this.dataset.categoryId;
                const checkboxes = document.querySelectorAll(`input[data-category="${categoryId}"]`);
                const selectAll = this.dataset.action === 'select';
                
                checkboxes.forEach(checkbox => {
                    checkbox.checked = selectAll;
                });
                
                this.dataset.action = selectAll ? 'deselect' : 'select';
                this.innerHTML = selectAll ? 
                    '<i class="fas fa-times-circle me-1"></i>Désélectionner tout' : 
                    '<i class="fas fa-check-circle me-1"></i>Sélectionner tout';
            });
        });
    });
</script>
@endsection
