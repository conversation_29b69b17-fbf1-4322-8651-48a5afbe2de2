@extends('ClientsView.layouts.dashboard')

@section('title', 'Mes événements')

@section('header-title', 'Mes événements')

@push('styles')
<style>
    .event-card {
        transition: all 0.3s ease;
        height: 100%;
    }
    
    .event-card:hover {
        transform: translateY(-5px);
    }
    
    .event-card .card-header {
        border-bottom: none;
        padding: 1.25rem 1.5rem;
    }
    
    .event-card .card-body {
        padding: 1.5rem;
    }
    
    .event-card .card-footer {
        background-color: transparent;
        border-top: 1px solid rgba(0, 0, 0, 0.05);
        padding: 1.25rem 1.5rem;
    }
    
    .event-date {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        width: 60px;
        height: 60px;
        background-color: rgba(255, 255, 255, 0.2);
        border-radius: 12px;
        margin-right: 1rem;
    }
    
    .event-date-day {
        font-size: 1.5rem;
        font-weight: 700;
        line-height: 1;
    }
    
    .event-date-month {
        font-size: 0.75rem;
        text-transform: uppercase;
        margin-top: 0.25rem;
    }
    
    .event-info-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.75rem;
    }
    
    .event-info-item:last-child {
        margin-bottom: 0;
    }
    
    .event-info-icon {
        width: 32px;
        height: 32px;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 0.75rem;
        font-size: 0.9rem;
    }
    
    .event-progress {
        height: 6px;
        border-radius: 3px;
        background-color: rgba(255, 255, 255, 0.2);
        margin-top: 1rem;
    }
    
    .event-progress-bar {
        height: 100%;
        border-radius: 3px;
        background-color: #fff;
    }
    
    .event-actions {
        display: flex;
        gap: 0.5rem;
    }
    
    .event-type-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        padding: 0.35rem 0.75rem;
        border-radius: 50px;
        font-size: 0.75rem;
        font-weight: 500;
    }
    
    .event-empty-state {
        text-align: center;
        padding: 3rem 1rem;
        background-color: white;
        border-radius: 12px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
    }
    
    .event-empty-state img {
        width: 150px;
        margin-bottom: 1.5rem;
        opacity: 0.7;
    }
    
    .event-empty-state h4 {
        margin-bottom: 1rem;
        font-weight: 600;
    }
    
    .event-empty-state p {
        color: #6b7280;
        margin-bottom: 1.5rem;
        max-width: 500px;
        margin-left: auto;
        margin-right: auto;
    }
    
    .filters-container {
        background-color: white;
        border-radius: 12px;
        padding: 1rem;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        margin-bottom: 1.5rem;
    }
</style>
@endpush

@section('content')
<div class="filters-container mb-4">
    <div class="row align-items-center">
        <div class="col-lg-4 mb-3 mb-lg-0">
            <div class="d-flex">
                <a href="{{ route('client.events.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-2"></i>Nouvel événement
                </a>
            </div>
        </div>
        
        <div class="col-lg-4 mb-3 mb-lg-0">
            <form action="{{ route('client.events') }}" method="GET">
                <div class="input-group">
                    <input type="text" name="search" class="form-control" placeholder="Rechercher un événement..." value="{{ request('search') }}">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
            </form>
        </div>
        
        <div class="col-lg-4">
            <div class="d-flex justify-content-lg-end">
                <div class="dropdown me-2">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="dateFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-calendar me-1"></i>
                        @if(request('date'))
                            @if(request('date') == 'upcoming')
                                À venir
                            @elseif(request('date') == 'past')
                                Passés
                            @elseif(request('date') == 'today')
                                Aujourd'hui
                            @elseif(request('date') == 'week')
                                Cette semaine
                            @elseif(request('date') == 'month')
                                Ce mois
                            @elseif(request('date') == 'year')
                                Cette année
                            @endif
                        @else
                            Tous
                        @endif
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="dateFilterDropdown">
                        <li><a class="dropdown-item {{ !request('date') ? 'active' : '' }}" href="{{ route('client.events', array_merge(request()->except('date'), ['page' => 1])) }}">Tous</a></li>
                        <li><a class="dropdown-item {{ request('date') == 'upcoming' ? 'active' : '' }}" href="{{ route('client.events', array_merge(request()->except('date'), ['date' => 'upcoming', 'page' => 1])) }}">À venir</a></li>
                        <li><a class="dropdown-item {{ request('date') == 'past' ? 'active' : '' }}" href="{{ route('client.events', array_merge(request()->except('date'), ['date' => 'past', 'page' => 1])) }}">Passés</a></li>
                        <li><a class="dropdown-item {{ request('date') == 'today' ? 'active' : '' }}" href="{{ route('client.events', array_merge(request()->except('date'), ['date' => 'today', 'page' => 1])) }}">Aujourd'hui</a></li>
                        <li><a class="dropdown-item {{ request('date') == 'week' ? 'active' : '' }}" href="{{ route('client.events', array_merge(request()->except('date'), ['date' => 'week', 'page' => 1])) }}">Cette semaine</a></li>
                        <li><a class="dropdown-item {{ request('date') == 'month' ? 'active' : '' }}" href="{{ route('client.events', array_merge(request()->except('date'), ['date' => 'month', 'page' => 1])) }}">Ce mois</a></li>
                        <li><a class="dropdown-item {{ request('date') == 'year' ? 'active' : '' }}" href="{{ route('client.events', array_merge(request()->except('date'), ['date' => 'year', 'page' => 1])) }}">Cette année</a></li>
                    </ul>
                </div>
                
                <div class="dropdown">
                    <button class="btn btn-outline-secondary dropdown-toggle" type="button" id="statusFilterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-filter me-1"></i>
                        @if(request('status'))
                            @if(request('status') == 'planning')
                                En planification
                            @elseif(request('status') == 'confirmed')
                                Confirmés
                            @elseif(request('status') == 'completed')
                                Terminés
                            @elseif(request('status') == 'cancelled')
                                Annulés
                            @endif
                        @else
                            Tous les statuts
                        @endif
                    </button>
                    <ul class="dropdown-menu" aria-labelledby="statusFilterDropdown">
                        <li><a class="dropdown-item {{ !request('status') ? 'active' : '' }}" href="{{ route('client.events', array_merge(request()->except('status'), ['page' => 1])) }}">Tous les statuts</a></li>
                        <li><a class="dropdown-item {{ request('status') == 'planning' ? 'active' : '' }}" href="{{ route('client.events', array_merge(request()->except('status'), ['status' => 'planning', 'page' => 1])) }}">En planification</a></li>
                        <li><a class="dropdown-item {{ request('status') == 'confirmed' ? 'active' : '' }}" href="{{ route('client.events', array_merge(request()->except('status'), ['status' => 'confirmed', 'page' => 1])) }}">Confirmés</a></li>
                        <li><a class="dropdown-item {{ request('status') == 'completed' ? 'active' : '' }}" href="{{ route('client.events', array_merge(request()->except('status'), ['status' => 'completed', 'page' => 1])) }}">Terminés</a></li>
                        <li><a class="dropdown-item {{ request('status') == 'cancelled' ? 'active' : '' }}" href="{{ route('client.events', array_merge(request()->except('status'), ['status' => 'cancelled', 'page' => 1])) }}">Annulés</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

@if(session('status'))
    <div class="alert alert-success alert-dismissible fade show" role="alert">
        <i class="fas fa-check-circle me-2"></i>{{ session('status') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

@if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        <i class="fas fa-exclamation-circle me-2"></i>{{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

@if($events->count() > 0)
    <div class="row">
        @foreach($events as $event)
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card event-card">
                    <div class="card-header bg-{{ $event->status == 'planning' ? 'primary' : ($event->status == 'confirmed' ? 'info' : ($event->status == 'completed' ? 'success' : 'secondary')) }} text-white position-relative">
                        <div class="d-flex align-items-center">
                            <div class="event-date">
                                <span class="event-date-day">{{ \Carbon\Carbon::parse($event->date)->format('d') }}</span>
                                <span class="event-date-month">{{ \Carbon\Carbon::parse($event->date)->format('M') }}</span>
                            </div>
                            <div>
                                <h5 class="mb-1">{{ $event->title }}</h5>
                                <p class="mb-0 opacity-75">{{ \Carbon\Carbon::parse($event->date)->format('d/m/Y') }}</p>
                            </div>
                        </div>
                        
                        <span class="event-type-badge bg-white text-{{ $event->status == 'planning' ? 'primary' : ($event->status == 'confirmed' ? 'info' : ($event->status == 'completed' ? 'success' : 'secondary')) }}">
                            {{ $event->eventType->name }}
                        </span>
                        
                        <div class="event-progress">
                            <div class="event-progress-bar" style="width: {{ $event->progress ?? 0 }}%"></div>
                        </div>
                    </div>
                    
                    <div class="card-body">
                        <div class="event-info-item">
                            <div class="event-info-icon bg-light text-primary">
                                <i class="fas fa-map-marker-alt"></i>
                            </div>
                            <div>
                                <p class="mb-0">{{ $event->location }}</p>
                            </div>
                        </div>
                        
                        @if($event->guest_count)
                            <div class="event-info-item">
                                <div class="event-info-icon bg-light text-primary">
                                    <i class="fas fa-users"></i>
                                </div>
                                <div>
                                    <p class="mb-0">{{ $event->guest_count }} invités</p>
                                </div>
                            </div>
                        @endif
                        
                        @if($event->budget)
                            <div class="event-info-item">
                                <div class="event-info-icon bg-light text-primary">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div>
                                    <p class="mb-0">{{ number_format($event->budget, 0, ',', ' ') }} Ar</p>
                                </div>
                            </div>
                        @endif
                        
                        <div class="event-info-item">
                            <div class="event-info-icon bg-{{ $event->status == 'planning' ? 'primary' : ($event->status == 'confirmed' ? 'info' : ($event->status == 'completed' ? 'success' : 'secondary')) }} text-white">
                                <i class="fas fa-{{ $event->status == 'planning' ? 'clipboard-list' : ($event->status == 'confirmed' ? 'calendar-check' : ($event->status == 'completed' ? 'check-circle' : 'ban')) }}"></i>
                            </div>
                            <div>
                                <p class="mb-0">{{ $event->status == 'planning' ? 'En planification' : ($event->status == 'confirmed' ? 'Confirmé' : ($event->status == 'completed' ? 'Terminé' : 'Annulé')) }}</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-footer">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <span class="text-muted small">Progression: {{ $event->progress ?? 0 }}%</span>
                            </div>
                            <div class="event-actions">
                                <a href="{{ route('client.events.show', $event->id) }}" class="btn btn-primary">
                                    <i class="fas fa-eye me-1"></i>Détails
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
    
    <div class="d-flex justify-content-center mt-4">
        {{ $events->links() }}
    </div>
@else
    <div class="event-empty-state">
        <img src="{{ asset('images/empty-events.svg') }}" alt="Aucun événement">
        <h4>Aucun événement trouvé</h4>
        <p>Vous n'avez pas encore créé d'événement ou aucun événement ne correspond à vos critères de recherche.</p>
        <a href="{{ route('client.events.create') }}" class="btn btn-primary btn-lg">
            <i class="fas fa-plus me-2"></i>Créer mon premier événement
        </a>
    </div>
@endif
@endsection
