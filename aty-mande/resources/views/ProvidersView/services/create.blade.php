@extends('ProvidersView.layouts.dashboard')

@section('title', 'Ajouter un service')

@section('styles')
<style>
    .menu-item {
        border: 1px solid #dee2e6;
        border-radius: 8px;
        margin-bottom: 15px;
        position: relative;
        transition: all 0.3s ease;
    }

    .menu-item:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    }

    .menu-item .card-header {
        border-radius: 8px 8px 0 0;
        padding: 12px 15px;
    }

    .menu-item .card-body {
        padding: 15px;
    }

    .delete-menu-item {
        transition: all 0.2s ease;
    }

    .delete-menu-item:hover {
        background-color: #dc3545;
        color: white;
    }

    .pricing-options {
        display: none;
    }

    .pricing-options.active {
        display: block;
    }

    .image-preview {
        width: 150px;
        height: 100px;
        object-fit: cover;
        border-radius: 4px;
        margin-top: 10px;
    }

    /* Style pour les onglets de service */
    #serviceTabs .nav-link {
        color: #495057;
        font-weight: 500;
        border-radius: 4px 4px 0 0;
        padding: 12px 20px;
        transition: all 0.3s ease;
        margin-right: 4px;
    }

    /* Style pour les champs invalides */
    .is-invalid {
        border-color: #dc3545;
        background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
        background-repeat: no-repeat;
        background-position: right calc(0.375em + 0.1875rem) center;
        background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
    }

    /* Style pour les badges de type de plat */
    .menu-type-badge {
        font-size: 0.8rem;
        padding: 0.25rem 0.5rem;
    }

    /* Style pour les actions dans le tableau */
    .menu-item-actions {
        white-space: nowrap;
    }

    /* Style pour le tableau des éléments du menu */
    #menu-items-table th, #menu-items-table td {
        vertical-align: middle;
    }

    /* Style pour le prix dans le tableau */
    .menu-item-price {
        font-weight: 600;
        color: #198754;
    }

    #serviceTabs .nav-link:hover {
        background-color: #f0f7ff;
        border-color: #dee2e6 #dee2e6 transparent;
    }

    #serviceTabs .nav-link.active {
        color: #fff;
        background-color: #0d6efd;
        border-color: #0d6efd;
        font-weight: 600;
    }

    /* Style pour les contenus des onglets */
    .tab-pane {
        padding: 20px 0;
    }

    /* Style pour les options spécifiques */
    .service-options {
        padding: 20px;
        border-radius: 8px;
        margin-top: 20px;
        border: 1px solid #e9ecef;
        background-color: #f8f9fa;
        box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    }

    /* Animation pour l'affichage des options */
    .fade-in {
        animation: fadeIn 0.5s;
    }

    @keyframes fadeIn {
        from { opacity: 0; }
        to { opacity: 1; }
    }
</style>
@endsection

@section('content')
<div class="container-fluid">
    <div class="row mb-4">
        <div class="col-md-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{{ route('provider.services') }}">Mes services</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Ajouter un service</li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="card border-0 shadow-sm">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">Ajouter un nouveau service</h5>
        </div>
        <div class="card-body">
            @if($categories->count() == 0)
            <div class="alert alert-warning mb-4">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Attention :</strong> Vous n'avez pas encore sélectionné de catégories de services dans votre profil.
                <a href="{{ route('provider.profile') }}" class="alert-link">Modifiez votre profil</a> pour ajouter des catégories avant de créer un service.
            </div>
            @endif

            <div class="row">
                <div class="col-md-12">
                    <div class="mb-3">
                        <label class="form-label">Catégorie <span class="text-danger">*</span></label>
                        <div class="mt-3">
                            <p class="mb-2"><strong>Sélection du service :</strong> Cliquez sur un onglet pour afficher le formulaire correspondant</p>
                            <ul class="nav nav-tabs" id="serviceTabs" role="tablist">
                                @foreach($categories as $index => $category)
                                    @if($category->name !== 'Forfait Complet')
                                        <li class="nav-item" role="presentation">
                                            <button class="nav-link {{ $index === 0 ? 'active' : '' }}"
                                                id="show_category_{{ $category->id }}"
                                                data-bs-toggle="tab"
                                                data-bs-target="#category_tab_{{ $category->id }}"
                                                type="button"
                                                role="tab"
                                                aria-controls="category_tab_{{ $category->id }}"
                                                aria-selected="{{ $index === 0 ? 'true' : 'false' }}"
                                                data-category-id="{{ $category->id }}"
                                                data-category-name="{{ $category->name }}">
                                                <i class="fas {{ $category->icon ?? 'fa-list' }} me-1"></i> {{ $category->name }}
                                            </button>
                                        </li>
                                    @endif
                                @endforeach
                            </ul>
                        </div>

                        @if($categories->count() > 0 && $allCategories->count() > $categories->count())
                            <div class="form-text mt-2">
                                <i class="fas fa-info-circle me-1"></i>
                                Vous ne trouvez pas la catégorie que vous cherchez ?
                                <a href="{{ route('provider.profile') }}">Modifiez votre profil</a> pour ajouter d'autres catégories.
                            </div>
                        @endif
                    </div>
                </div>
            </div>



                <!-- Conteneur des onglets de service -->
                <div class="tab-content mt-3" id="serviceTabsContent">
                    @foreach($categories as $index => $category)
                        @if($category->name !== 'Forfait Complet')
                            <div class="tab-pane fade {{ $index === 0 ? 'show active' : '' }}" id="category_tab_{{ $category->id }}" role="tabpanel" aria-labelledby="show_category_{{ $category->id }}">
                                <form action="{{ route('provider.services.store') }}" method="POST" enctype="multipart/form-data" id="serviceForm_{{ $category->id }}" class="service-form">
                                    @csrf

                                    <!-- Champ caché pour category_id -->
                                    <input type="hidden" name="category_id" value="{{ $category->id }}">

                                    <!-- Champ pour le prix -->
                                    <div class="mb-3">
                                <label for="min_guests" class="form-label">Prix du service <span class="text-danger">*</span></label>
                                    
                                        <div class="input-group">
                                            <input type="number" class="form-control @error('price') is-invalid @enderror" id="price_{{ $category->id }}" name="price" value="{{ old('price') }}" min="0" step="1">
                                            <span class="input-group-text">Ar</span>
                                        </div>
                                        @error('price')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <div class="form-check mt-2">
                                            <input class="form-check-input" type="checkbox" id="is_negotiable_{{ $category->id }}" name="is_negotiable" {{ old('is_negotiable') ? 'checked' : '' }}>
                                            <label class="form-check-label" for="is_negotiable_{{ $category->id }}">
                                                Prix négociable
                                            </label>
                                        </div>
                                    </div>

                                    <!-- Les erreurs de validation sont maintenant affichées via Simply Toast -->

                                    <!-- Champ pour le titre du service (maintenant éditable) -->
                                    <div class="mb-3">
                                        <label for="title_{{ $category->id }}" class="form-label">Nom du service <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control title-field @error('title') is-invalid @enderror" id="title_{{ $category->id }}" name="title" value="{{ old('title') }}" required>
                                        @error('title')
                                            <div class="invalid-feedback">{{ $message }}</div>
                                        @enderror
                                        <small class="form-text text-muted">Un nom par défaut sera suggéré en fonction du type de service, mais vous pouvez le personnaliser.</small>
                                    </div>
                                @if($category->name === 'Traiteur')
                        <div class="service-options">
                            <h5 class="mb-3">Options de traiteur</h5>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="min_guests" class="form-label">Nombre minimum d'invités <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="min_guests" name="min_guests" value="{{ old('min_guests') }}" min="1" required>
                                </div>
                                <div class="col-md-6">
                                    <label for="max_guests" class="form-label">Nombre maximum d'invités <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="max_guests" name="max_guests" value="{{ old('max_guests') }}" min="1" required>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="base_service_fee" class="form-label">Frais de service de base (Ar)</label>
                                    <input type="number" class="form-control" id="base_service_fee" name="price" value="{{ old('price') }}" min="0">
                                    <small class="form-text text-muted">Frais fixes pour le service (déplacement, personnel, mise en place, etc.). Facultatif si ces frais sont déjà inclus dans le prix des plats. Si aucun prix n'est spécifié (ni ici, ni dans les plats), le service sera affiché comme "Sur devis".</small>
                                </div>
                                <div class="col-md-6">
                                    <label for="additional_services" class="form-label">Services additionnels inclus</label>
                                    <textarea class="form-control" id="additional_services" name="additional_services" rows="2" placeholder="Ex: Service à table, décoration des tables, etc.">{{ old('additional_services') }}</textarea>
                                    <small class="form-text text-muted">Services inclus dans les frais de base (hors nourriture)</small>
                                </div>
                            </div>

                            <div class="card border-primary mb-3">
                                <div class="card-header bg-primary text-white">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <strong>IMPORTANT : Comment fonctionne la tarification</strong>
                                </div>
                                <div class="card-body">
                                    <ul class="mb-0">
                                        <li class="mb-2">Vous avez deux options pour structurer vos prix :</li>
                                        <li class="mb-2"><strong>Option 1 :</strong> Indiquer des frais de service de base + prix des platspar invité</li>
                                        <li class="mb-2"><strong>Option 2 :</strong> Inclure tous vos frais dans le prix des plats (laisser les frais de base à 0)</li>
                                        <li class="mb-2">Le <strong>prix total</strong> pour le client sera : Frais de base + (Prix des plats × Nombre d'invités)</li>
                                    </ul>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Options alimentaires disponibles</label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="vegetarian" name="dietary_options[]" value="vegetarian" {{ in_array('vegetarian', old('dietary_options', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="vegetarian">Végétarien</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="vegan" name="dietary_options[]" value="vegan" {{ in_array('vegan', old('dietary_options', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="vegan">Végétalien</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="gluten_free" name="dietary_options[]" value="gluten_free" {{ in_array('gluten_free', old('dietary_options', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="gluten_free">Sans gluten</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Menu <span class="text-danger">*</span></label>
                                <p class="text-muted small">Ajoutez les différents plats que vous proposez, en précisant le type (entrée, plat principal, dessert, boisson).</p>

                                <div class="card mb-3">
                                    <div class="card-header bg-light">
                                        <h6 class="mb-0">Éléments du menu</h6>
                                    </div>
                                    <div class="card-body">
                                        <!-- Formulaire d'ajout de plat intégré directement -->
                                        <div class="mb-4 p-3 border rounded bg-light">
                                            <h6 class="mb-3">Ajouter un plat au menu</h6>
                                            <div class="row g-3">
                                                <div class="col-md-6">
                                                    <label for="menu_item_name" class="form-label">Nom du plat <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" id="menu_item_name" placeholder="Ex: Salade César">
                                                    <div class="invalid-feedback">Veuillez entrer un nom pour ce plat.</div>
                                                </div>
                                                <div class="col-md-6">
                                                    <label for="menu_item_type" class="form-label">Type <span class="text-danger">*</span></label>
                                                    <select class="form-select" id="menu_item_type">
                                                        <option value="">Sélectionner...</option>
                                                        <option value="entree">Entrée</option>
                                                        <option value="plat_principal">Plat principal</option>
                                                        <option value="dessert">Dessert</option>
                                                        <option value="boisson">Boisson</option>
                                                        <option value="accompagnement">Accompagnement</option>
                                                        <option value="autre">Autre</option>
                                                    </select>
                                                    <div class="invalid-feedback">Veuillez sélectionner un type.</div>
                                                </div>
                                                <div class="col-12">
                                                    <label for="menu_item_description" class="form-label">Description</label>
                                                    <textarea class="form-control" id="menu_item_description" rows="2" placeholder="Ex: Salade romaine, croûtons, parmesan, sauce César"></textarea>
                                                </div>
                                                <div class="col-12">
                                                    <div class="alert alert-danger d-none" id="menu-item-error"></div>
                                                    <div class="d-flex gap-2">
                                                        <button type="button" class="btn btn-primary save-menu-item-btn" id="saveMenuItem" onclick="addMenuItemDirect(this)">
                                                            <i class="fas fa-plus-circle me-1"></i> Ajouter au menu
                                                        </button>
                                                        <button type="button" class="btn btn-outline-secondary d-none cancel-edit-btn" id="cancelEdit" onclick="cancelEditDirect(this)">
                                                            <i class="fas fa-times me-1"></i> Annuler
                                                        </button>

                                                        <script>
                                                            // Fonction directement définie dans le HTML
                                                            function addMenuItemDirect(button) {
                                                                console.log("Bouton Ajouter au menu cliqué (via fonction inline)");

                                                                // Trouver l'onglet actif
                                                                const activeTab = $(button).closest('.tab-pane');
                                                                console.log("Onglet actif trouvé:", activeTab.length > 0);

                                                                // Réinitialiser les messages d'erreur
                                                                activeTab.find('#menu-item-error').addClass('d-none');
                                                                activeTab.find('.is-invalid').removeClass('is-invalid');

                                                                // Récupérer les valeurs du formulaire
                                                                const name = activeTab.find('#menu_item_name').val().trim();
                                                                const type = activeTab.find('#menu_item_type').val();
                                                                const priceValue = activeTab.find('#menu_item_price').val();
                                                                const price = parseFloat(priceValue);
                                                                const description = activeTab.find('#menu_item_description').val().trim();

                                                                console.log("Valeurs du formulaire:", { name, type, priceValue, price, description });

                                                                // Valider les champs requis
                                                                let hasErrors = false;

                                                                if (!name) {
                                                                    activeTab.find('#menu_item_name').addClass('is-invalid');
                                                                    hasErrors = true;
                                                                }

                                                                if (!type) {
                                                                    activeTab.find('#menu_item_type').addClass('is-invalid');
                                                                    hasErrors = true;
                                                                }

                                                                // Le prix est optionnel, mais s'il est fourni, il doit être valide
                                                                // Si le champ est vide, on accepte (prix = 0)
                                                                if (priceValue !== "" && (price < 0 || isNaN(price))) {
                                                                    activeTab.find('#menu_item_price').addClass('is-invalid');
                                                                    hasErrors = true;
                                                                }

                                                                if (hasErrors) {
                                                                    activeTab.find('#menu-item-error').removeClass('d-none').text('Veuillez corriger les erreurs dans le formulaire.');
                                                                    return;
                                                                }

                                                                // Initialiser le tableau des éléments du menu si nécessaire
                                                                if (!window.menuItems) {
                                                                    window.menuItems = [];
                                                                }

                                                                // Ajouter le nouvel élément
                                                                // Si le prix est vide ou NaN, on le met à 0
                                                                const finalPrice = (priceValue === "" || isNaN(price)) ? 0 : price;

                                                                const newItem = {
                                                                    name: name,
                                                                    type: type,
                                                                    description: description
                                                                };

                                                                window.menuItems.push(newItem);

                                                                // Mettre à jour l'affichage directement
                                                                updateMenuDisplay(activeTab, window.menuItems);

                                                                // Réinitialiser le formulaire
                                                                activeTab.find('#menu_item_name').val('');
                                                                activeTab.find('#menu_item_type').val('');
                                                                activeTab.find('#menu_item_price').val('');
                                                                activeTab.find('#menu_item_description').val('');
                                                            }

                                                            // Fonction pour mettre à jour l'affichage des éléments du menu
                                                            function updateMenuDisplay(activeTab, menuItems) {
                                                                console.log("Mise à jour de l'affichage du menu avec", menuItems.length, "éléments");

                                                                // Récupérer les éléments du DOM
                                                                const menuItemsTable = activeTab.find('#menu-items-table');
                                                                const menuItemsTableContainer = activeTab.find('#menu-items-table-container');
                                                                const noMenuItemsMessage = activeTab.find('#no-menu-items-message');
                                                                const menuItemsData = activeTab.find('#menu-items-data');
                                                                const menuWarning = activeTab.find('#menu_warning');

                                                                console.log("Éléments trouvés:", {
                                                                    table: menuItemsTable.length,
                                                                    container: menuItemsTableContainer.length,
                                                                    message: noMenuItemsMessage.length,
                                                                    data: menuItemsData.length,
                                                                    warning: menuWarning.length
                                                                });

                                                                // Vider le tableau et les données cachées
                                                                menuItemsTable.find('tbody').empty();
                                                                menuItemsData.empty();

                                                                // Afficher ou masquer le message "Aucun élément"
                                                                if (menuItems.length === 0) {
                                                                    noMenuItemsMessage.removeClass('d-none');
                                                                    menuItemsTableContainer.addClass('d-none');
                                                                    menuWarning.removeClass('d-none');
                                                                    return;
                                                                }

                                                                // Masquer le message "Aucun élément" et afficher le tableau
                                                                noMenuItemsMessage.addClass('d-none');
                                                                menuItemsTableContainer.removeClass('d-none');
                                                                menuWarning.addClass('d-none');

                                                                // Fonction pour obtenir le libellé du type de plat
                                                                function getTypeLabel(type) {
                                                                    const types = {
                                                                        'entree': 'Entrée',
                                                                        'plat_principal': 'Plat principal',
                                                                        'dessert': 'Dessert',
                                                                        'boisson': 'Boisson',
                                                                        'accompagnement': 'Accompagnement',
                                                                        'autre': 'Autre'
                                                                    };
                                                                    return types[type] || type;
                                                                }

                                                                // Fonction pour obtenir la classe de badge pour le type de plat
                                                                function getTypeBadgeClass(type) {
                                                                    const classes = {
                                                                        'entree': 'bg-info',
                                                                        'plat_principal': 'bg-primary',
                                                                        'dessert': 'bg-success',
                                                                        'boisson': 'bg-warning',
                                                                        'accompagnement': 'bg-secondary',
                                                                        'autre': 'bg-dark'
                                                                    };
                                                                    return classes[type] || 'bg-secondary';
                                                                }

                                                                // Fonction pour formater le prix
                                                                function formatPrice(price) {
                                                                    // Si le prix est 0, afficher "Sur devis"
                                                                    if (price === 0) {
                                                                        return 'Sur devis';
                                                                    }
                                                                    return new Intl.NumberFormat('fr-FR').format(price) + ' Ar';
                                                                }

                                                                // Ajouter les éléments au tableau
                                                                menuItems.forEach((item, index) => {
                                                                    const typeLabel = getTypeLabel(item.type);
                                                                    const typeBadgeClass = getTypeBadgeClass(item.type);

                                                                    // Ajouter la ligne au tableau
                                                                    const row = `
                                                                        <tr>
                                                                            <td>${item.name}</td>
                                                                            <td><span class="badge ${typeBadgeClass} menu-type-badge">${typeLabel}</span></td>
                                                                            <td class="menu-item-price">${formatPrice(item.price)}</td>
                                                                            <td class="menu-item-actions">
                                                                                <button type="button" class="btn btn-sm btn-outline-primary edit-menu-item" onclick="editMenuItemDirect(${index}, this)">
                                                                                    <i class="fas fa-edit"></i>
                                                                                </button>
                                                                                <button type="button" class="btn btn-sm btn-outline-danger delete-menu-item" onclick="deleteMenuItemDirect(${index}, this)">
                                                                                    <i class="fas fa-trash"></i>
                                                                                </button>
                                                                            </td>
                                                                        </tr>
                                                                    `;
                                                                    menuItemsTable.find('tbody').append(row);

                                                                    // Ajouter les champs cachés pour la soumission du formulaire
                                                                    menuItemsData.append(`
                                                                        <input type="hidden" name="menu_items[${index}][name]" value="${item.name}">
                                                                        <input type="hidden" name="menu_items[${index}][type]" value="${item.type}">
                                                                        <input type="hidden" name="menu_items[${index}][price]" value="${item.price}">
                                                                        <input type="hidden" name="menu_items[${index}][description]" value="${item.description || ''}">
                                                                    `);
                                                                });
                                                            }

                                                            // Fonction pour éditer un élément du menu
                                                            function editMenuItemDirect(index, button) {
                                                                console.log("Édition de l'élément", index);

                                                                if (!window.menuItems || !window.menuItems[index]) {
                                                                    console.error("Élément introuvable");
                                                                    return;
                                                                }

                                                                const item = window.menuItems[index];
                                                                const activeTab = $(button).closest('.tab-pane');

                                                                // Remplir le formulaire avec les données de l'élément
                                                                activeTab.find('#menu_item_name').val(item.name);
                                                                activeTab.find('#menu_item_type').val(item.type);
                                                                activeTab.find('#menu_item_price').val(item.price);
                                                                activeTab.find('#menu_item_description').val(item.description || '');

                                                                // Changer le texte du bouton pour indiquer le mode édition
                                                                activeTab.find('#saveMenuItem').html('<i class="fas fa-save me-1"></i> Enregistrer les modifications');

                                                                // Afficher le bouton d'annulation
                                                                activeTab.find('#cancelEdit').removeClass('d-none');

                                                                // Stocker l'index de l'élément en cours d'édition
                                                                window.editingIndex = index;

                                                                // Faire défiler jusqu'au formulaire
                                                                $('html, body').animate({
                                                                    scrollTop: activeTab.find('#menu_item_name').offset().top - 100
                                                                }, 500);
                                                            }

                                                            // Fonction pour supprimer un élément du menu
                                                            function deleteMenuItemDirect(index, button) {
                                                                console.log("Suppression de l'élément", index);

                                                                if (!window.menuItems || !window.menuItems[index]) {
                                                                    console.error("Élément introuvable");
                                                                    return;
                                                                }

                                                                if (confirm('Êtes-vous sûr de vouloir supprimer cet élément du menu ?')) {
                                                                    // Supprimer l'élément du tableau
                                                                    window.menuItems.splice(index, 1);

                                                                    // Mettre à jour l'affichage
                                                                    const activeTab = $(button).closest('.tab-pane');
                                                                    updateMenuDisplay(activeTab, window.menuItems);
                                                                }
                                                            }

                                                            function cancelEditDirect(button) {
                                                                console.log("Bouton Annuler cliqué (via fonction inline)");

                                                                // Trouver l'onglet actif
                                                                const activeTab = $(button).closest('.tab-pane');

                                                                // Réinitialiser le formulaire
                                                                activeTab.find('#menu_item_name').val('');
                                                                activeTab.find('#menu_item_type').val('');
                                                                activeTab.find('#menu_item_price').val('');
                                                                activeTab.find('#menu_item_description').val('');

                                                                // Changer le texte du bouton pour revenir au mode ajout
                                                                activeTab.find('.save-menu-item-btn').html('<i class="fas fa-plus-circle me-1"></i> Ajouter au menu');

                                                                // Cacher le bouton d'annulation
                                                                $(button).addClass('d-none');

                                                                // Réinitialiser l'index d'édition
                                                                window.editingIndex = null;
                                                            }
                                                        </script>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Tableau des éléments du menu -->
                                        <div id="menu-items-container" class="mt-4">
                                            <h6 class="mb-3">Plats ajoutés</h6>
                                            <div class="alert alert-info" id="no-menu-items-message">
                                                <i class="fas fa-info-circle me-2"></i>
                                                Aucun élément dans le menu. Utilisez le formulaire ci-dessus pour ajouter des plats.
                                            </div>
                                            <div class="table-responsive d-none" id="menu-items-table-container">
                                                <table class="table table-hover table-bordered" id="menu-items-table">
                                                    <thead class="table-light">
                                                        <tr>
                                                            <th>Nom du plat</th>
                                                            <th>Type</th>
                                                            <th>Prixpar invité <i class="fas fa-info-circle text-primary" data-bs-toggle="tooltip" title="Prixpar invité, pas pour le total"></i></th>
                                                            <th>Actions</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <!-- Les éléments du menu seront ajoutés ici dynamiquement -->
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>

                                        <div class="alert alert-warning mt-3 d-none" id="menu_warning">
                                            <i class="fas fa-exclamation-triangle me-2"></i>
                                            Vous devez ajouter au moins un élément au menu.
                                        </div>
                                    </div>
                                </div>

                                <!-- Conteneur caché pour stocker les données du menu -->
                                <div id="menu-items-data" class="d-none">
                                    <!-- Les inputs seront ajoutés ici dynamiquement -->
                                </div>
                            </div>
                        </div>
                                @elseif($category->name === 'Photographie')
                                    @php
                                        $serviceId = null;
                                        if ($category->services->count() > 0) {
                                            $service = $category->services->first();
                                            if ($service) {
                                                $serviceId = $service->id;
                                            }
                                        }
                                    @endphp
                        <div class="service-options" data-service-id="{{ $serviceId }}">
                            <h5 class="mb-3">Options de photographie</h5>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="duration" class="form-label">Durée de la prestation (heures) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="duration" name="duration" value="{{ old('duration') }}" min="1" step="0.5" required>
                                    <small class="form-text text-muted">Indiquez la durée standard de votre prestation (ex: 4 heures, 8 heures).</small>
                                </div>
                                <div class="col-md-6">
                                    <label for="photo_price" class="form-label">Prix de la prestation (Ar) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="photo_price" name="price" value="{{ old('price') }}" min="0" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="num_photos" class="form-label">Nombre de photos livrées <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="num_photos" name="num_photos" value="{{ old('num_photos') }}" min="1" required>
                                <small class="form-text text-muted">Nombre approximatif de photos retouchées que vous livrez au client.</small>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Livrables inclus <span class="text-danger">*</span></label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="digital_files" name="deliverables[]" value="digital_files" {{ in_array('digital_files', old('deliverables', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="digital_files">Fichiers numériques</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="printed_photos" name="deliverables[]" value="printed_photos" {{ in_array('printed_photos', old('deliverables', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="printed_photos">Photos imprimées</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="photo_album" name="deliverables[]" value="photo_album" {{ in_array('photo_album', old('deliverables', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="photo_album">Album photo</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Équipement utilisé</label>
                                <textarea class="form-control" id="equipment" name="equipment" rows="3" placeholder="Ex: Canon EOS R5, objectifs 24-70mm f/2.8, 70-200mm f/2.8, flash, trépied, etc.">{{ old('equipment') }}</textarea>
                                <small class="form-text text-muted">Décrivez brièvement votre équipement pour informer vos clients sur la qualité de votre matériel.</small>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Style de photographie</label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="style_traditional" name="photo_styles[]" value="traditional" {{ in_array('traditional', old('photo_styles', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="style_traditional">Traditionnel</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="style_photojournalistic" name="photo_styles[]" value="photojournalistic" {{ in_array('photojournalistic', old('photo_styles', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="style_photojournalistic">Photojournalisme</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="style_artistic" name="photo_styles[]" value="artistic" {{ in_array('artistic', old('photo_styles', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="style_artistic">Artistique</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                @elseif($category->name === 'Vidéographie')
                                    @php
                                        $serviceId = null;
                                        if ($category->services->count() > 0) {
                                            $service = $category->services->first();
                                            if ($service) {
                                                $serviceId = $service->id;
                                            }
                                        }
                                    @endphp
                        <div class="service-options" data-service-id="{{ $serviceId }}">
                            <h5 class="mb-3">Options de vidéographie</h5>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="video_duration" class="form-label">Durée de la prestation (heures) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="video_duration" name="duration" value="{{ old('duration') }}" min="1" step="0.5" required>
                                    <small class="form-text text-muted">Indiquez la durée standard de votre prestation (ex: 4 heures, 8 heures).</small>
                                </div>
                                <div class="col-md-6">
                                    <label for="video_price" class="form-label">Prix de la prestation (Ar) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="video_price" name="price" value="{{ old('price') }}" min="0" required>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="final_video_length" class="form-label">Durée de la vidéo finale (minutes) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="final_video_length" name="final_video_length" value="{{ old('final_video_length') }}" min="1" required>
                                    <small class="form-text text-muted">Durée approximative de la vidéo finale montée.</small>
                                </div>
                                <div class="col-md-6">
                                    <label for="delivery_time" class="form-label">Délai de livraison (jours) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="delivery_time" name="delivery_time" value="{{ old('delivery_time') }}" min="1" required>
                                    <small class="form-text text-muted">Délai approximatif pour livrer la vidéo finale après l'événement.</small>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Formats de vidéo proposés <span class="text-danger">*</span></label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="format_highlight" name="video_formats[]" value="highlight" {{ in_array('highlight', old('video_formats', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="format_highlight">Vidéo highlights (court-métrage)</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="format_documentary" name="video_formats[]" value="documentary" {{ in_array('documentary', old('video_formats', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="format_documentary">Documentaire complet</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="format_raw" name="video_formats[]" value="raw" {{ in_array('raw', old('video_formats', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="format_raw">Rushes bruts</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Équipement utilisé</label>
                                <textarea class="form-control" id="video_equipment" name="equipment" rows="3" placeholder="Ex: Sony A7S III, stabilisateur, drone DJI Mavic 3, microphones, éclairages, etc.">{{ old('equipment') }}</textarea>
                                <small class="form-text text-muted">Décrivez brièvement votre équipement pour informer vos clients sur la qualité de votre matériel.</small>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Services additionnels disponibles</label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="drone" name="video_services[]" value="drone" {{ in_array('drone', old('video_services', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="drone">Prises de vue par drone</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="same_day_edit" name="video_services[]" value="same_day_edit" {{ in_array('same_day_edit', old('video_services', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="same_day_edit">Montage le jour même</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="live_streaming" name="video_services[]" value="live_streaming" {{ in_array('live_streaming', old('video_services', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="live_streaming">Diffusion en direct</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                                @elseif($category->name === 'Espaces de Réception')
                                    @php
                                        $serviceId = null;
                                        if ($category->services->count() > 0) {
                                            $service = $category->services->first();
                                            if ($service) {
                                                $serviceId = $service->id;
                                            }
                                        }
                                    @endphp
                        <div class="service-options" data-service-id="{{ $serviceId }}">
                            <h5 class="mb-3">Options d'espace de réception</h5>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="venue_capacity" class="form-label">Capacité maximale (personnes) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="venue_capacity" name="capacity" value="{{ old('capacity') }}" min="1" required>
                                    <small class="form-text text-muted">Nombre maximum de personnes que l'espace peut accueillir.</small>
                                </div>
                                <div class="col-md-6">
                                    <label for="venue_price" class="form-label">Prix de location (Ar) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="venue_price" name="price" value="{{ old('price') }}" min="0" required>
                                    <small class="form-text text-muted">Prix pour une journée complète de location.</small>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="venue_size" class="form-label">Superficie (m²)</label>
                                <input type="number" class="form-control" id="venue_size" name="venue_size" value="{{ old('venue_size') }}" min="1">
                                <small class="form-text text-muted">Superficie totale de l'espace en mètres carrés.</small>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Type d'espace <span class="text-danger">*</span></label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input venue-type-radio" type="radio" id="venue_type_indoor" name="venue_type" value="indoor" {{ old('venue_type') == 'indoor' ? 'checked' : '' }}>
                                            <label class="form-check-label" for="venue_type_indoor">Intérieur</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="radio" id="venue_type_outdoor" name="venue_type" value="outdoor" {{ old('venue_type') == 'outdoor' ? 'checked' : '' }}>
                                            <label class="form-check-label" for="venue_type_outdoor">Extérieur</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="radio" id="venue_type_both" name="venue_type" value="both" {{ old('venue_type') == 'both' ? 'checked' : '' }}>
                                            <label class="form-check-label" for="venue_type_both">Mixte (intérieur et extérieur)</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Équipements inclus</label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="amenity_tables" name="amenities[]" value="tables" {{ in_array('tables', old('amenities', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="amenity_tables">Tables et chaises</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="amenity_sound" name="amenities[]" value="sound" {{ in_array('sound', old('amenities', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="amenity_sound">Système sonore</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="amenity_lighting" name="amenities[]" value="lighting" {{ in_array('lighting', old('amenities', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="amenity_lighting">Éclairage</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="amenity_parking" name="amenities[]" value="parking" {{ in_array('parking', old('amenities', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="amenity_parking">Parking</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="amenity_kitchen" name="amenities[]" value="kitchen" {{ in_array('kitchen', old('amenities', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="amenity_kitchen">Cuisine</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="amenity_wifi" name="amenities[]" value="wifi" {{ in_array('wifi', old('amenities', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="amenity_wifi">Wi-Fi</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="venue_rules" class="form-label">Règles et restrictions</label>
                                <textarea class="form-control" id="venue_rules" name="venue_rules" rows="3" placeholder="Ex: Pas de musique après 23h, pas d'animaux, etc.">{{ old('venue_rules') }}</textarea>
                                <small class="form-text text-muted">Précisez les règles importantes que les clients doivent connaître.</small>
                            </div>
                        </div>
                                @elseif($category->name === 'Musique & Animation')
                                    @php
                                        $serviceId = null;
                                        if ($category->services->count() > 0) {
                                            $service = $category->services->first();
                                            if ($service) {
                                                $serviceId = $service->id;
                                            }
                                        }
                                    @endphp
                        <div class="service-options" data-service-id="{{ $serviceId }}">
                            <h5 class="mb-3">Options de Musique & Animation</h5>

                            <div class="mb-3">
                                <label class="form-label">Type de service <span class="text-danger">*</span></label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input music-type-radio" type="radio" id="music_type_dj" name="music_type" value="dj" {{ old('music_type') == 'dj' ? 'checked' : '' }}>
                                            <label class="form-check-label" for="music_type_dj">DJ</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="radio" id="music_type_band" name="music_type" value="band" {{ old('music_type') == 'band' ? 'checked' : '' }}>
                                            <label class="form-check-label" for="music_type_band">Orchestre / Groupe</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="radio" id="music_type_artist" name="music_type" value="artist" {{ old('music_type') == 'artist' ? 'checked' : '' }}>
                                            <label class="form-check-label" for="music_type_artist">Artiste solo</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="radio" id="music_type_mc" name="music_type" value="mc" {{ old('music_type') == 'mc' ? 'checked' : '' }}>
                                            <label class="form-check-label" for="music_type_mc">Maître de cérémonie</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="radio" id="music_type_animator" name="music_type" value="animator" {{ old('music_type') == 'animator' ? 'checked' : '' }}>
                                            <label class="form-check-label" for="music_type_animator">Animateur</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="radio" id="music_type_other" name="music_type" value="other" {{ old('music_type') == 'other' ? 'checked' : '' }}>
                                            <label class="form-check-label" for="music_type_other">Autre</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="music_duration" class="form-label">Durée de la prestation (heures) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="music_duration" name="duration" value="{{ old('duration') }}" min="1" step="0.5" required>
                                    <small class="form-text text-muted">Indiquez la durée standard de votre prestation (ex: 4 heures, 6 heures).</small>
                                </div>
                                <div class="col-md-6">
                                    <label for="music_price" class="form-label">Prix de la prestation (Ar) <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="music_price" name="price" value="{{ old('price') }}" min="0" required>
                                    <small class="form-text text-muted">Prix pour la durée standard indiquée.</small>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Genres musicaux proposés</label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="genre_pop" name="music_genres[]" value="pop" {{ in_array('pop', old('music_genres', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="genre_pop">Pop / Variété</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="genre_rock" name="music_genres[]" value="rock" {{ in_array('rock', old('music_genres', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="genre_rock">Rock</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="genre_jazz" name="music_genres[]" value="jazz" {{ in_array('jazz', old('music_genres', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="genre_jazz">Jazz</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="genre_traditional" name="music_genres[]" value="traditional" {{ in_array('traditional', old('music_genres', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="genre_traditional">Musique traditionnelle</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="genre_rnb" name="music_genres[]" value="rnb" {{ in_array('rnb', old('music_genres', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="genre_rnb">R&B / Soul</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="genre_electronic" name="music_genres[]" value="electronic" {{ in_array('electronic', old('music_genres', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="genre_electronic">Électronique</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3" id="band_details" style="display: none;">
                                <label class="form-label">Composition du groupe</label>
                                <textarea class="form-control" id="band_composition" name="band_composition" rows="3" placeholder="Ex: 1 chanteur, 1 guitariste, 1 bassiste, 1 batteur, 1 claviériste">{{ old('band_composition') }}</textarea>
                                <small class="form-text text-muted">Décrivez la composition de votre groupe ou orchestre.</small>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Équipement fourni</label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="equipment_sound" name="music_equipment[]" value="sound_system" {{ in_array('sound_system', old('music_equipment', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="equipment_sound">Système de sonorisation</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="equipment_lighting" name="music_equipment[]" value="lighting" {{ in_array('lighting', old('music_equipment', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="equipment_lighting">Éclairage</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="equipment_microphones" name="music_equipment[]" value="microphones" {{ in_array('microphones', old('music_equipment', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="equipment_microphones">Microphones</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="equipment_instruments" name="music_equipment[]" value="instruments" {{ in_array('instruments', old('music_equipment', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="equipment_instruments">Instruments</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="equipment_dj" name="music_equipment[]" value="dj_equipment" {{ in_array('dj_equipment', old('music_equipment', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="equipment_dj">Équipement DJ</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="equipment_effects" name="music_equipment[]" value="special_effects" {{ in_array('special_effects', old('music_equipment', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="equipment_effects">Effets spéciaux</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="music_repertoire" class="form-label">Répertoire / Spécialités</label>
                                <textarea class="form-control" id="music_repertoire" name="music_repertoire" rows="3" placeholder="Ex: Spécialisé dans les musiques des années 80-90, répertoire international et malgache, etc.">{{ old('music_repertoire') }}</textarea>
                                <small class="form-text text-muted">Décrivez votre répertoire ou vos spécialités musicales.</small>
                            </div>
                        </div>
                                @elseif($category->name === 'Transport')
                                    @php
                                        $serviceId = null;
                                        if ($category->services->count() > 0) {
                                            $service = $category->services->first();
                                            if ($service) {
                                                $serviceId = $service->id;
                                            }
                                        }
                                    @endphp
                        <div class="service-options" data-service-id="{{ $serviceId }}">
                            <h5 class="mb-3">Options de transport</h5>

                            <div class="row mb-3">
                                <div class="col-md-6">
                                    <label for="vehicle_type" class="form-label">Type de véhicule <span class="text-danger">*</span></label>
                                    <select class="form-select" id="vehicle_type" name="vehicle_type" required>
                                        <option value="">Sélectionnez un type</option>
                                        <option value="sedan" {{ old('vehicle_type') == 'sedan' ? 'selected' : '' }}>Berline</option>
                                        <option value="suv" {{ old('vehicle_type') == 'suv' ? 'selected' : '' }}>SUV</option>
                                        <option value="van" {{ old('vehicle_type') == 'van' ? 'selected' : '' }}>Van/Minibus</option>
                                        <option value="bus" {{ old('vehicle_type') == 'bus' ? 'selected' : '' }}>Bus</option>
                                        <option value="limousine" {{ old('vehicle_type') == 'limousine' ? 'selected' : '' }}>Limousine</option>
                                        <option value="other" {{ old('vehicle_type') == 'other' ? 'selected' : '' }}>Autre</option>
                                    </select>
                                </div>
                                <div class="col-md-6">
                                    <label for="num_seats" class="form-label">Nombre de places <span class="text-danger">*</span></label>
                                    <input type="number" class="form-control" id="num_seats" name="num_seats" value="{{ old('num_seats') }}" min="1" required>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="transport_price" class="form-label">Prix (Ar) <span class="text-danger">*</span></label>
                                <input type="number" class="form-control" id="transport_price" name="price" value="{{ old('price') }}" min="0" required>
                                <small class="form-text text-muted">Prix pour le service de transport.</small>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Options incluses</label>
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="cortege_lead" name="transport_options[]" value="cortege_lead" {{ in_array('cortege_lead', old('transport_options', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="cortege_lead">Tête de cortège</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="fuel_included" name="transport_options[]" value="fuel_included" {{ in_array('fuel_included', old('transport_options', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="fuel_included">Carburant inclus</label>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="checkbox" id="decoration" name="transport_options[]" value="decoration" {{ in_array('decoration', old('transport_options', [])) ? 'checked' : '' }}>
                                            <label class="form-check-label" for="decoration">Décoration incluse</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Type de tarification <span class="text-danger">*</span></label>
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input transport-pricing-radio" type="radio" id="pricing_hourly" name="transport_pricing_model" value="hourly" {{ old('transport_pricing_model', 'hourly') == 'hourly' ? 'checked' : '' }}>
                                            <label class="form-check-label" for="pricing_hourly">Tarification horaire</label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check mb-2">
                                            <input class="form-check-input" type="radio" id="pricing_per_trip" name="transport_pricing_model" value="per_trip" {{ old('transport_pricing_model') == 'per_trip' ? 'checked' : '' }}>
                                            <label class="form-check-label" for="pricing_per_trip">Tarification par trajet</label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="vehicle_details" class="form-label">Détails du véhicule</label>
                                <textarea class="form-control" id="vehicle_details" name="vehicle_details" rows="3" placeholder="Marque, modèle, année, couleur, etc.">{{ old('vehicle_details') }}</textarea>
                            </div>
                        </div>
                                @endif

                                <!-- Champs communs pour tous les services -->
                                <hr>

                                <div class="mb-3">
                                    <label for="images_{{ $category->id }}" class="form-label">Images (max 5)</label>
                                    <input type="file" class="form-control images-input @error('images.*') is-invalid @enderror" id="images_{{ $category->id }}" name="images[]" multiple accept="image/*">
                                    <small class="form-text text-muted">Vous pouvez sélectionner jusqu'à 5 images. La première image sera utilisée comme image principale.</small>
                                    @error('images.*')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                    <div id="image_previews_{{ $category->id }}" class="image-previews d-flex flex-wrap gap-2 mt-2"></div>
                                </div>

                                <div class="mb-3">
                                    <label for="description_{{ $category->id }}" class="form-label">Description <span class="text-danger">*</span></label>
                                    <textarea class="form-control description-field @error('description') is-invalid @enderror" id="description_{{ $category->id }}" name="description" rows="4" required>{{ old('description') }}</textarea>
                                    <small class="form-text text-muted">Décrivez votre service en détail pour aider les clients à comprendre ce que vous proposez.</small>
                                    @error('description')
                                        <div class="invalid-feedback">{{ $message }}</div>
                                    @enderror
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="is_featured_{{ $category->id }}" name="is_featured" value="1" {{ old('is_featured') ? 'checked' : '' }}>
                                            <label class="form-check-label" for="is_featured_{{ $category->id }}">
                                                Mettre en avant ce service
                                            </label>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-check mb-3">
                                            <input class="form-check-input" type="checkbox" id="is_active_{{ $category->id }}" name="is_active" value="1" {{ old('is_active', '1') ? 'checked' : '' }}>
                                            <label class="form-check-label" for="is_active_{{ $category->id }}">
                                                Activer ce service
                                            </label>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex justify-content-end mt-4">
                                    <a href="{{ route('provider.services') }}" class="btn btn-secondary me-2">Annuler</a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>Enregistrer le service {{ $category->name }}
                                    </button>
                                </div>
                                </form>
                            </div>
                        @endif
                    @endforeach
                </div>


        </div>
    </div>
</div>


@endsection

@section('head')
@parent
<!-- Définition des fonctions JavaScript globales dans l'en-tête pour qu'elles soient disponibles avant le rendu du HTML -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    // Vérifier si jQuery est correctement chargé
    if (typeof jQuery === 'undefined') {
        console.error('jQuery n\'est pas chargé!');
        alert('jQuery n\'est pas chargé! Veuillez rafraîchir la page.');
    } else {
        console.log('jQuery est chargé, version:', jQuery.fn.jquery);
    }

    // Variables globales
    window.menuItems = [];
    window.editingIndex = null;

    // Fonctions globales pour les gestionnaires d'événements onclick
    function handleSaveMenuItem(button) {
        alert("Bouton Ajouter au menu cliqué (via onclick)");
        console.log("Bouton Ajouter au menu cliqué (via onclick)");

        // Trouver l'onglet actif
        const activeTab = $(button).closest('.tab-pane');
        console.log("Onglet actif trouvé:", activeTab.length > 0);

        // Réinitialiser les messages d'erreur
        activeTab.find('#menu-item-error').addClass('d-none');
        activeTab.find('.is-invalid').removeClass('is-invalid');

        // Récupérer les valeurs du formulaire
        const name = activeTab.find('#menu_item_name').val().trim();
        const type = activeTab.find('#menu_item_type').val();
        const priceValue = activeTab.find('#menu_item_price').val();
        const price = parseFloat(priceValue);
        const description = activeTab.find('#menu_item_description').val().trim();

        console.log("Valeurs du formulaire:", { name, type, priceValue, price, description });

        // Valider les champs requis
        let hasErrors = false;

        if (!name) {
            activeTab.find('#menu_item_name').addClass('is-invalid');
            hasErrors = true;
        }

        if (!type) {
            activeTab.find('#menu_item_type').addClass('is-invalid');
            hasErrors = true;
        }

        if (!priceValue || price <= 0 || isNaN(price)) {
            activeTab.find('#menu_item_price').addClass('is-invalid');
            hasErrors = true;
        }

        if (hasErrors) {
            activeTab.find('#menu-item-error').removeClass('d-none').text('Veuillez corriger les erreurs dans le formulaire.');
            return;
        }

        if (window.editingIndex !== null && window.editingIndex !== undefined) {
            // Mode édition - mettre à jour l'élément existant
            window.updateMenuItem(window.editingIndex, {
                name: name,
                type: type,
                price: price,
                description: description
            });

            // Changer le texte du bouton pour revenir au mode ajout
            $(button).html('<i class="fas fa-plus-circle me-1"></i> Ajouter au menu');

            // Cacher le bouton d'annulation
            activeTab.find('#cancelEdit').addClass('d-none');

            window.editingIndex = null;
        } else {
            // Mode ajout - ajouter un nouvel élément
            window.addMenuItem({
                name: name,
                type: type,
                price: price,
                description: description
            });
        }

        // Réinitialiser le formulaire
        activeTab.find('#menu_item_name').val('');
        activeTab.find('#menu_item_type').val('');
        activeTab.find('#menu_item_price').val('');
        activeTab.find('#menu_item_description').val('');
    }

    function handleCancelEdit(button) {
        console.log("Bouton Annuler cliqué (via onclick)");

        // Trouver l'onglet actif
        const activeTab = $(button).closest('.tab-pane');
        console.log("Onglet actif trouvé pour annulation:", activeTab.length > 0);

        // Réinitialiser le formulaire
        activeTab.find('#menu_item_name').val('');
        activeTab.find('#menu_item_type').val('');
        activeTab.find('#menu_item_price').val('');
        activeTab.find('#menu_item_description').val('');

        // Changer le texte du bouton pour revenir au mode ajout
        activeTab.find('#saveMenuItem').html('<i class="fas fa-plus-circle me-1"></i> Ajouter au menu');

        // Cacher le bouton d'annulation
        $(button).addClass('d-none');

        // Réinitialiser l'index d'édition
        window.editingIndex = null;
    }

    // Fonction pour ajouter un élément au menu
    window.addMenuItem = function(item) {
        console.log("Ajout d'un élément au menu:", item);
        window.menuItems.push(item);
        window.updateMenuItemsDisplay();
    };

    // Fonction pour mettre à jour un élément du menu
    window.updateMenuItem = function(index, item) {
        console.log("Mise à jour de l'élément", index, "avec", item);
        window.menuItems[index] = item;
        window.updateMenuItemsDisplay();
    };

    // Fonction pour supprimer un élément du menu
    window.deleteMenuItem = function(index) {
        if (confirm('Êtes-vous sûr de vouloir supprimer cet élément du menu ?')) {
            window.menuItems.splice(index, 1);
            window.updateMenuItemsDisplay();
        }
    };

    // Fonction pour obtenir le libellé du type de plat
    window.getTypeLabel = function(type) {
        const types = {
            'entree': 'Entrée',
            'plat_principal': 'Plat principal',
            'dessert': 'Dessert',
            'boisson': 'Boisson',
            'accompagnement': 'Accompagnement',
            'autre': 'Autre'
        };
        return types[type] || type;
    };

    // Fonction pour obtenir la classe de badge pour le type de plat
    window.getTypeBadgeClass = function(type) {
        const classes = {
            'entree': 'bg-info',
            'plat_principal': 'bg-primary',
            'dessert': 'bg-success',
            'boisson': 'bg-warning',
            'accompagnement': 'bg-secondary',
            'autre': 'bg-dark'
        };
        return classes[type] || 'bg-secondary';
    };

    // Fonction pour formater le prix
    window.formatPrice = function(price) {
        return new Intl.NumberFormat('fr-MG', { style: 'currency', currency: 'MGA' }).format(price);
    };

    // Fonction pour mettre à jour l'affichage des éléments du menu
    window.updateMenuItemsDisplay = function() {
        // Obtenir les éléments du DOM dans l'onglet actif
        const activeTab = $('.tab-pane.active');
        const menuItemsTable = activeTab.find('#menu-items-table');
        const menuItemsTableContainer = activeTab.find('#menu-items-table-container');
        const noMenuItemsMessage = activeTab.find('#no-menu-items-message');
        const menuItemsData = activeTab.find('#menu-items-data');
        const menuWarning = activeTab.find('#menu_warning');

        console.log("Mise à jour de l'affichage du menu", {
            activeTab: activeTab.length > 0,
            menuItemsTable: menuItemsTable.length > 0,
            menuItemsTableContainer: menuItemsTableContainer.length > 0,
            noMenuItemsMessage: noMenuItemsMessage.length > 0,
            menuItemsData: menuItemsData.length > 0,
            menuWarning: menuWarning.length > 0
        });

        // Vider le tableau et les données cachées
        menuItemsTable.find('tbody').empty();
        menuItemsData.empty();

        // Afficher ou masquer le message "Aucun élément"
        if (window.menuItems.length === 0) {
            noMenuItemsMessage.removeClass('d-none');
            menuItemsTableContainer.addClass('d-none');
            menuWarning.removeClass('d-none');
            return;
        }

        // Masquer le message "Aucun élément" et afficher le tableau
        noMenuItemsMessage.addClass('d-none');
        menuItemsTableContainer.removeClass('d-none');
        menuWarning.addClass('d-none');

        // Ajouter les éléments au tableau
        window.menuItems.forEach((item, index) => {
            const typeLabel = window.getTypeLabel(item.type);
            const typeBadgeClass = window.getTypeBadgeClass(item.type);

            // Ajouter la ligne au tableau
            const row = `
                <tr>
                    <td>${item.name}</td>
                    <td><span class="badge ${typeBadgeClass} menu-type-badge">${typeLabel}</span></td>
                    <td class="menu-item-price">${window.formatPrice(item.price)}</td>
                    <td class="menu-item-actions">
                        <button type="button" class="btn btn-sm btn-outline-primary edit-menu-item" onclick="window.editMenuItem(${index})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-danger delete-menu-item" onclick="window.deleteMenuItem(${index})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
            menuItemsTable.find('tbody').append(row);

            // Ajouter les champs cachés pour la soumission du formulaire
            menuItemsData.append(`
                <input type="hidden" name="menu_items[${index}][name]" value="${item.name}">
                <input type="hidden" name="menu_items[${index}][type]" value="${item.type}">
                <input type="hidden" name="menu_items[${index}][price]" value="${item.price}">
                <input type="hidden" name="menu_items[${index}][description]" value="${item.description || ''}">
            `);
        });
    };

    // Fonction pour éditer un élément du menu
    window.editMenuItem = function(index) {
        const item = window.menuItems[index];

        // Obtenir les éléments du DOM dans l'onglet actif
        const activeTab = $('.tab-pane.active');

        console.log("Édition de l'élément", index, item);

        // Remplir le formulaire principal
        activeTab.find('#menu_item_name').val(item.name);
        activeTab.find('#menu_item_type').val(item.type);
        activeTab.find('#menu_item_price').val(item.price);
        activeTab.find('#menu_item_description').val(item.description || '');

        // Changer le texte du bouton pour indiquer le mode édition
        activeTab.find('#saveMenuItem').html('<i class="fas fa-save me-1"></i> Enregistrer les modifications');

        // Afficher le bouton d'annulation
        activeTab.find('#cancelEdit').removeClass('d-none');

        // Stocker l'index de l'élément en cours d'édition
        window.editingIndex = index;

        // Faire défiler jusqu'au formulaire
        $('html, body').animate({
            scrollTop: activeTab.find('#menu_item_name').offset().top - 100
        }, 500);
    };
</script>
@endsection

@section('scripts')
<!-- Les modals ont été remplacés par un formulaire intégré directement dans la page -->
<script src="{{ asset('js/jquery.repeater.min.js') }}"></script>
<script>
    // Attendre que le document soit complètement chargé
    $(document).ready(function() {
        console.log('Document prêt');

        // Vérifier si les boutons existent
        console.log('Bouton saveMenuItem existe:', $('#saveMenuItem').length > 0);
        console.log('Bouton cancelEdit existe:', $('#cancelEdit').length > 0);

        // Afficher les erreurs de validation avec Simply Toast
        @if ($errors->any())
            @foreach ($errors->all() as $error)
                showErrorMessage("{{ $error }}");
            @endforeach
        @endif
        // Gestion des éléments du menu
        let menuItems = [];

        // Fonction pour obtenir les éléments du DOM dans l'onglet actif
        function getMenuElements() {
            const activeTab = $('.tab-pane.active');
            return {
                menuItemsTable: activeTab.find('#menu-items-table'),
                menuItemsTableContainer: activeTab.find('#menu-items-table-container'),
                noMenuItemsMessage: activeTab.find('#no-menu-items-message'),
                menuItemsData: activeTab.find('#menu-items-data'),
                menuWarning: activeTab.find('#menu_warning')
            };
        }

        // Fonction pour obtenir le libellé du type de plat
        function getTypeLabel(type) {
            const types = {
                'entree': 'Entrée',
                'plat_principal': 'Plat principal',
                'dessert': 'Dessert',
                'boisson': 'Boisson',
                'accompagnement': 'Accompagnement',
                'autre': 'Autre'
            };
            return types[type] || type;
        }

        // Fonction pour obtenir la classe de badge pour le type de plat
        function getTypeBadgeClass(type) {
            const classes = {
                'entree': 'bg-info',
                'plat_principal': 'bg-primary',
                'dessert': 'bg-success',
                'boisson': 'bg-warning',
                'accompagnement': 'bg-secondary',
                'autre': 'bg-dark'
            };
            return classes[type] || 'bg-secondary';
        }

        // Fonction pour formater le prix
        function formatPrice(price) {
            return new Intl.NumberFormat('fr-FR').format(price) + ' Ar';
        }

        // Fonction pour mettre à jour l'affichage des éléments du menu
        function updateMenuItemsDisplay() {
            // Obtenir les éléments du DOM dans l'onglet actif
            const elements = getMenuElements();

            console.log("Mise à jour de l'affichage du menu", elements);

            // Vider le tableau et les données cachées
            elements.menuItemsTable.find('tbody').empty();
            elements.menuItemsData.empty();

            // Afficher ou masquer le message "Aucun élément"
            if (menuItems.length === 0) {
                elements.noMenuItemsMessage.removeClass('d-none');
                elements.menuItemsTableContainer.addClass('d-none');
                elements.menuWarning.removeClass('d-none');
                return;
            }

            // Masquer le message "Aucun élément" et afficher le tableau
            elements.noMenuItemsMessage.addClass('d-none');
            elements.menuItemsTableContainer.removeClass('d-none');
            elements.menuWarning.addClass('d-none');

            // Ajouter les éléments au tableau
            menuItems.forEach((item, index) => {
                const typeLabel = getTypeLabel(item.type);
                const typeBadgeClass = getTypeBadgeClass(item.type);

                // Ajouter la ligne au tableau
                const row = `
                    <tr>
                        <td>${item.name}</td>
                        <td><span class="badge ${typeBadgeClass} menu-type-badge">${typeLabel}</span></td>
                        <td class="menu-item-price">${formatPrice(item.price)}</td>
                        <td class="menu-item-actions">
                            <button type="button" class="btn btn-sm btn-outline-primary edit-menu-item" data-index="${index}">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button type="button" class="btn btn-sm btn-outline-danger delete-menu-item" data-index="${index}">
                                <i class="fas fa-trash"></i>
                            </button>
                        </td>
                    </tr>
                `;
                elements.menuItemsTable.find('tbody').append(row);

                // Ajouter les champs cachés pour la soumission du formulaire
                elements.menuItemsData.append(`
                    <input type="hidden" name="menu_items[${index}][name]" value="${item.name}">
                    <input type="hidden" name="menu_items[${index}][type]" value="${item.type}">
                    <input type="hidden" name="menu_items[${index}][price]" value="${item.price}">
                    <input type="hidden" name="menu_items[${index}][description]" value="${item.description || ''}">
                `);
            });

            // Ajouter les gestionnaires d'événements pour les boutons d'édition et de suppression
            $('.tab-pane.active .edit-menu-item').click(function() {
                const index = $(this).data('index');
                editMenuItem(index);
            });

            $('.tab-pane.active .delete-menu-item').click(function() {
                const index = $(this).data('index');
                deleteMenuItem(index);
            });
        }

        // Fonction pour ajouter un élément au menu (globale pour être accessible depuis les fonctions onclick)
        window.addMenuItem = function(item) {
            console.log("Ajout d'un élément au menu:", item);
            menuItems.push(item);
            updateMenuItemsDisplay();
        }

        // Fonction pour éditer un élément du menu
        function editMenuItem(index) {
            const item = menuItems[index];

            // Obtenir les éléments du DOM dans l'onglet actif
            const activeTab = $('.tab-pane.active');

            console.log("Édition de l'élément", index, item);

            // Remplir le formulaire principal
            activeTab.find('#menu_item_name').val(item.name);
            activeTab.find('#menu_item_type').val(item.type);
            activeTab.find('#menu_item_price').val(item.price);
            activeTab.find('#menu_item_description').val(item.description || '');

            // Changer le texte du bouton pour indiquer le mode édition
            activeTab.find('#saveMenuItem').html('<i class="fas fa-save me-1"></i> Enregistrer les modifications');

            // Afficher le bouton d'annulation
            activeTab.find('#cancelEdit').removeClass('d-none');

            // Stocker l'index de l'élément en cours d'édition
            editingIndex = index;

            // Faire défiler jusqu'au formulaire
            $('html, body').animate({
                scrollTop: activeTab.find('#menu_item_name').offset().top - 100
            }, 500);
        }

        // Fonction pour mettre à jour un élément du menu (globale pour être accessible depuis les fonctions onclick)
        window.updateMenuItem = function(index, item) {
            console.log("Mise à jour de l'élément", index, "avec", item);
            menuItems[index] = item;
            updateMenuItemsDisplay();
        }

        // Fonction pour supprimer un élément du menu
        function deleteMenuItem(index) {
            if (confirm('Êtes-vous sûr de vouloir supprimer cet élément du menu ?')) {
                menuItems.splice(index, 1);
                updateMenuItemsDisplay();
            }
        }

        // Variables pour l'édition (globale pour être accessible depuis les fonctions onclick)
        window.editingIndex = null;

        // Gestionnaire d'événement pour le bouton "Ajouter au menu" avec une approche différente
        $(document).ready(function() {
            // Utiliser une approche directe pour le bouton
            $(document).on('click', '#saveMenuItem', function(e) {
                e.preventDefault(); // Empêcher le comportement par défaut
                alert("Bouton cliqué"); // Vérifier si l'événement est déclenché
                console.log("Bouton Ajouter au menu cliqué");

                // Trouver l'onglet actif d'une manière différente
                const activeTab = $(this).closest('.tab-pane');
                console.log("Onglet actif trouvé:", activeTab.length > 0);

                // Réinitialiser les messages d'erreur
                activeTab.find('#menu-item-error').addClass('d-none');
                activeTab.find('.is-invalid').removeClass('is-invalid');

                // Récupérer les valeurs du formulaire
                const name = activeTab.find('#menu_item_name').val().trim();
                const type = activeTab.find('#menu_item_type').val();
                const priceValue = activeTab.find('#menu_item_price').val();
                const price = parseFloat(priceValue);
                const description = activeTab.find('#menu_item_description').val().trim();

                console.log("Valeurs du formulaire:", { name, type, priceValue, price, description });

                // Valider les champs requis
                let hasErrors = false;

                if (!name) {
                    activeTab.find('#menu_item_name').addClass('is-invalid');
                    hasErrors = true;
                }

                if (!type) {
                    activeTab.find('#menu_item_type').addClass('is-invalid');
                    hasErrors = true;
                }

                if (!priceValue || price <= 0 || isNaN(price)) {
                    activeTab.find('#menu_item_price').addClass('is-invalid');
                    hasErrors = true;
                }

                if (hasErrors) {
                    activeTab.find('#menu-item-error').removeClass('d-none').text('Veuillez corriger les erreurs dans le formulaire.');
                    return;
                }

                if (editingIndex !== null) {
                    // Mode édition - mettre à jour l'élément existant
                    updateMenuItem(editingIndex, {
                        name: name,
                        type: type,
                        price: price,
                        description: description
                    });

                    // Changer le texte du bouton pour revenir au mode ajout
                    $(this).html('<i class="fas fa-plus-circle me-1"></i> Ajouter au menu');

                    // Cacher le bouton d'annulation
                    activeTab.find('#cancelEdit').addClass('d-none');

                    editingIndex = null;
                } else {
                    // Mode ajout - ajouter un nouvel élément
                    addMenuItem({
                        name: name,
                        type: type,
                        price: price,
                        description: description
                    });
                }

                // Réinitialiser le formulaire
                activeTab.find('#menu_item_name').val('');
                activeTab.find('#menu_item_type').val('');
                activeTab.find('#menu_item_price').val('');
                activeTab.find('#menu_item_description').val('');
            });
        });

        // Ajout d'un bouton pour annuler l'édition
        $(document).on('click', '#cancelEdit', function(e) {
            e.preventDefault(); // Empêcher le comportement par défaut
            console.log("Bouton Annuler cliqué");

            // Trouver l'onglet actif d'une manière différente
            const activeTab = $(this).closest('.tab-pane');
            console.log("Onglet actif trouvé pour annulation:", activeTab.length > 0);

            // Réinitialiser le formulaire
            activeTab.find('#menu_item_name').val('');
            activeTab.find('#menu_item_type').val('');
            activeTab.find('#menu_item_price').val('');
            activeTab.find('#menu_item_description').val('');

            // Changer le texte du bouton pour revenir au mode ajout
            activeTab.find('#saveMenuItem').html('<i class="fas fa-plus-circle me-1"></i> Ajouter au menu');

            // Cacher le bouton d'annulation
            $(this).addClass('d-none');

            // Réinitialiser l'index d'édition
            editingIndex = null;
        });

        // Initialiser l'affichage des éléments du menu
        updateMenuItemsDisplay();

        // Fonction pour définir le titre par défaut en fonction du service
        function setDefaultTitle(serviceType, categoryId) {
            let defaultTitle = '';

            switch(serviceType) {
                case 'catering':
                    defaultTitle = 'Service de traiteur';
                    break;
                case 'photography':
                    defaultTitle = 'Service de photographie';
                    break;
                case 'videography':
                    defaultTitle = 'Service de vidéographie';
                    break;
                case 'venue':
                    defaultTitle = 'Location d\'espace de réception';
                    break;
                case 'music_entertainment':
                    defaultTitle = 'Service de musique et animation';
                    break;
                case 'transport':
                    defaultTitle = 'Service de transport';
                    break;
            }

            // Sélectionner le champ de titre spécifique à ce formulaire
            const titleField = $(`#title_${categoryId}`);

            // Ne remplacer que si le champ est vide ou contient un titre par défaut précédent
            if (titleField.val() === '' ||
                titleField.val() === 'Service de traiteur' ||
                titleField.val() === 'Service de photographie' ||
                titleField.val() === 'Service de vidéographie' ||
                titleField.val() === 'Location d\'espace de réception' ||
                titleField.val() === 'Service de musique et animation' ||
                titleField.val() === 'Service de transport') {
                titleField.val(defaultTitle);
            }
        }

        // Gestionnaire d'événements pour les types de musique
        $('#music_type_band, #music_type_artist').on('change', function() {
            if ($('#music_type_band').is(':checked')) {
                $('#band_details').slideDown();
            } else {
                $('#band_details').slideUp();
            }
        });

        // Initialiser les gestionnaires d'événements pour les onglets de catégorie
        $('.nav-link[id^="show_category_"]').each(function() {
            const categoryId = $(this).data('category-id');
            const categoryName = $(this).data('category-name');

            $(this).on('shown.bs.tab', function(e) {
                console.log('Onglet activé:', categoryName, 'ID:', categoryId);

                // Nous n'avons plus besoin de définir service_id car nous utilisons maintenant category_id

                // Définir le titre par défaut en fonction de la catégorie
                if (categoryName.includes('Traiteur')) {
                    setDefaultTitle('catering', categoryId);
                    $(`#serviceForm_${categoryId} .pricing-type-select`).val('per_person');
                } else if (categoryName.includes('Photographie')) {
                    setDefaultTitle('photography', categoryId);
                    $(`#serviceForm_${categoryId} .pricing-type-select`).val('fixed');
                } else if (categoryName.includes('Vidéographie')) {
                    setDefaultTitle('videography', categoryId);
                    $(`#serviceForm_${categoryId} .pricing-type-select`).val('fixed');
                } else if (categoryName.includes('Espaces de Réception')) {
                    setDefaultTitle('venue', categoryId);
                    $(`#serviceForm_${categoryId} .pricing-type-select`).val('fixed');
                } else if (categoryName.includes('Musique & Animation')) {
                    setDefaultTitle('music_entertainment', categoryId);
                    $(`#serviceForm_${categoryId} .pricing-type-select`).val('fixed');

                    // Vérifier si le type de groupe est sélectionné pour afficher les détails
                    if ($('#music_type_band').is(':checked')) {
                        $('#band_details').show();
                    } else {
                        $('#band_details').hide();
                    }
                } else if (categoryName.includes('Transport')) {
                    setDefaultTitle('transport', categoryId);
                    $(`#serviceForm_${categoryId} .pricing-type-select`).val('fixed');
                }
            });
        });

        // Gestionnaire d'événement pour le changement de catégorie
        $('#category_id').change(function() {
            const categoryId = $(this).val();
            const categoryName = $(this).find('option:selected').text();

            console.log('Catégorie sélectionnée:', categoryName);

            // Activer l'onglet correspondant à la catégorie
            $(`#show_category_${categoryId}`).tab('show');
        });

        // Bouton de rafraîchissement
        $('#force_category_change').click(function() {
            const categoryId = $('#category_id').val();
            const categoryName = $('#category_id option:selected').text();

            console.log('Bouton de rafraîchissement cliqué, catégorie:', categoryName);

            if (categoryId && categoryName !== 'Sélectionnez une catégorie') {
                // Activer l'onglet correspondant à la catégorie
                $(`#show_category_${categoryId}`).tab('show');
            } else {
                alert('Veuillez sélectionner une catégorie');
            }
        });

        // Initialiser l'affichage si une catégorie est déjà sélectionnée
        setTimeout(function() {
            // Vérifier s'il y a un onglet actif dans la session (en cas d'erreur de validation)
            @if(session('active_tab'))
                // Activer l'onglet correspondant à la catégorie en session
                $(`#show_category_{{ session('active_tab') }}`).tab('show');
            @else
                const categoryId = $('#category_id').val();
                const categoryName = $('#category_id option:selected').text();

                // Initialiser l'affichage des détails du groupe si nécessaire
                if ($('#music_type_band').is(':checked')) {
                    $('#band_details').show();
                } else {
                    $('#band_details').hide();
                }

                if (categoryId && categoryName !== 'Sélectionnez une catégorie') {
                    console.log('Initialisation avec la catégorie:', categoryName);
                    // Activer l'onglet correspondant à la catégorie
                    $(`#show_category_${categoryId}`).tab('show');
                } else {
                    // Par défaut, afficher le premier onglet disponible
                    $('.nav-link[id^="show_category_"]:first').tab('show');
                }
            @endif

            // Si des erreurs sont présentes, faire défiler jusqu'à la première erreur
            if ($('.alert-danger').length > 0) {
                $('.alert-danger:first')[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }, 500);

        // Gestion des types de tarification pour chaque formulaire
        $('.pricing-type-select').change(function() {
            const pricingType = $(this).val();
            const formId = $(this).closest('form').attr('id');
            const formContainer = $(`#${formId}`);

            // Masquer toutes les options de tarification dans ce formulaire
            formContainer.find('.pricing-options').removeClass('active');

            // Afficher l'option correspondante
            if (pricingType === 'fixed' || pricingType === 'hourly' || pricingType === 'per_person') {
                formContainer.find('.fixed-pricing').addClass('active');
            } else if (pricingType === 'custom') {
                formContainer.find('.custom-pricing').addClass('active');
            }
        });

        // Fonction pour gérer la validation des champs requis en fonction de la catégorie active
        function handleRequiredFields() {
            const activeTab = $('.tab-pane.active').attr('id');
            const categoryName = $('.nav-link.active').data('category-name');

            // Réinitialiser tous les champs required
            $('.venue-type-radio, .music-type-radio, .transport-pricing-radio').prop('required', false);

            // Ajouter l'attribut required en fonction de la catégorie active
            if (categoryName && categoryName.includes('Espaces de Réception')) {
                // Au moins un radio button doit être sélectionné
                $('.venue-type-radio:first').prop('required', true);
            } else if (categoryName && categoryName.includes('Musique & Animation')) {
                $('.music-type-radio:first').prop('required', true);
            } else if (categoryName && categoryName.includes('Transport')) {
                $('.transport-pricing-radio:first').prop('required', true);
            }
        }

        // Fonction pour définir le type de tarification en fonction de la catégorie
        function setPricingType(categoryName) {
            let pricingType = 'fixed'; // Par défaut

            if (categoryName) {
                if (categoryName.includes('Traiteur')) {
                    pricingType = 'per_person';
                } else if (categoryName.includes('Photographie') || categoryName.includes('Vidéographie')) {
                    pricingType = 'fixed';
                } else if (categoryName.includes('Espaces de Réception')) {
                    pricingType = 'fixed';
                } else if (categoryName.includes('Musique & Animation')) {
                    pricingType = 'hourly';
                } else if (categoryName.includes('Transport')) {
                    pricingType = 'fixed';
                }
            }

            // Mettre à jour le champ caché
            $('.tab-pane.active').find('input[name="pricing_type"]').val(pricingType);
        }

        // Appeler la fonction lors du changement d'onglet
        $('.nav-link[id^="show_category_"]').on('shown.bs.tab', function() {
            handleRequiredFields();
            const categoryName = $(this).data('category-name');
            setPricingType(categoryName);
        });

        // Appeler les fonctions au chargement initial
        setTimeout(function() {
            handleRequiredFields();
            const categoryName = $('.nav-link.active').data('category-name');
            setPricingType(categoryName);
        }, 500);

        // Validation des formulaires individuels
        $('.service-form').submit(function(e) {
            const formId = $(this).attr('id');
            const categoryId = formId.split('_')[1];
            const categoryName = $(`#show_category_${categoryId}`).data('category-name');

            // Mettre à jour les champs requis avant la soumission
            handleRequiredFields();

            if (categoryName && categoryName.includes('Traiteur')) {
                if (menuItems.length === 0) {
                    e.preventDefault();
                    alert('Vous devez ajouter au moins un élément au menu pour un service de traiteur.');
                    $('#menu_warning').removeClass('d-none');
                    $('#menu_warning')[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
                    return;
                }
            }
        });

        // Prévisualisation des images pour chaque formulaire
        $('.images-input').change(function() {
            const formId = $(this).closest('form').attr('id');
            const categoryId = formId.split('_')[1];
            const previewContainer = $(`#image_previews_${categoryId}`);

            previewContainer.empty();

            for (let i = 0; i < this.files.length; i++) {
                const file = this.files[i];
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        $('<img>')
                            .attr('src', e.target.result)
                            .addClass('image-preview')
                            .appendTo(previewContainer);
                    };
                    reader.readAsDataURL(file);
                }
            }
        });

        // Attacher les gestionnaires d'événements pour les boutons du menu avec une approche différente
        console.log("Nombre de boutons 'Ajouter au menu' trouvés:", $('.save-menu-item-btn').length);

        // Utiliser setTimeout pour s'assurer que le DOM est complètement chargé
        setTimeout(function() {
            console.log("Attaching event handlers after timeout");
            console.log("Nombre de boutons 'Ajouter au menu' trouvés après timeout:", $('.save-menu-item-btn').length);

            // Attacher directement l'événement à tous les boutons avec la classe save-menu-item-btn
            $('.save-menu-item-btn').each(function() {
                console.log("Attaching click event to button:", $(this).attr('id'));
                $(this).on('click', function(e) {
                    e.preventDefault();
                    alert("Bouton Ajouter au menu cliqué (via jQuery direct)");
                    console.log("Bouton Ajouter au menu cliqué (via jQuery direct)");

                    // Trouver l'onglet actif
                    const activeTab = $(this).closest('.tab-pane');
                    console.log("Onglet actif trouvé:", activeTab.length > 0);

                    // Réinitialiser les messages d'erreur
                    activeTab.find('#menu-item-error').addClass('d-none');
                    activeTab.find('.is-invalid').removeClass('is-invalid');

                    // Récupérer les valeurs du formulaire
                    const name = activeTab.find('#menu_item_name').val().trim();
                    const type = activeTab.find('#menu_item_type').val();
                    const priceValue = activeTab.find('#menu_item_price').val();
                    const price = parseFloat(priceValue);
                    const description = activeTab.find('#menu_item_description').val().trim();

                    console.log("Valeurs du formulaire:", { name, type, priceValue, price, description });

                    // Valider les champs requis
                    let hasErrors = false;

                    if (!name) {
                        activeTab.find('#menu_item_name').addClass('is-invalid');
                        hasErrors = true;
                    }

                    if (!type) {
                        activeTab.find('#menu_item_type').addClass('is-invalid');
                        hasErrors = true;
                    }

                    if (!priceValue || price <= 0 || isNaN(price)) {
                        activeTab.find('#menu_item_price').addClass('is-invalid');
                        hasErrors = true;
                    }

                    if (hasErrors) {
                        activeTab.find('#menu-item-error').removeClass('d-none').text('Veuillez corriger les erreurs dans le formulaire.');
                        return;
                    }

                    if (window.editingIndex !== null && window.editingIndex !== undefined) {
                        // Mode édition - mettre à jour l'élément existant
                        window.updateMenuItem(window.editingIndex, {
                            name: name,
                            type: type,
                            price: price,
                            description: description
                        });

                        // Changer le texte du bouton pour revenir au mode ajout
                        $(this).html('<i class="fas fa-plus-circle me-1"></i> Ajouter au menu');

                        // Cacher le bouton d'annulation
                        activeTab.find('.cancel-edit-btn').addClass('d-none');

                        window.editingIndex = null;
                    } else {
                        // Mode ajout - ajouter un nouvel élément
                        window.addMenuItem({
                            name: name,
                            type: type,
                            price: price,
                            description: description
                        });
                    }

                    // Réinitialiser le formulaire
                    activeTab.find('#menu_item_name').val('');
                    activeTab.find('#menu_item_type').val('');
                    activeTab.find('#menu_item_price').val('');
                    activeTab.find('#menu_item_description').val('');
                });
            });

            // Attacher directement l'événement à tous les boutons avec la classe cancel-edit-btn
            $('.cancel-edit-btn').each(function() {
                console.log("Attaching click event to cancel button:", $(this).attr('id'));
                $(this).on('click', function(e) {
                    e.preventDefault();
                    console.log("Bouton Annuler cliqué (via jQuery direct)");

                    // Trouver l'onglet actif
                    const activeTab = $(this).closest('.tab-pane');
                    console.log("Onglet actif trouvé pour annulation:", activeTab.length > 0);

                    // Réinitialiser le formulaire
                    activeTab.find('#menu_item_name').val('');
                    activeTab.find('#menu_item_type').val('');
                    activeTab.find('#menu_item_price').val('');
                    activeTab.find('#menu_item_description').val('');

                    // Changer le texte du bouton pour revenir au mode ajout
                    activeTab.find('.save-menu-item-btn').html('<i class="fas fa-plus-circle me-1"></i> Ajouter au menu');

                    // Cacher le bouton d'annulation
                    $(this).addClass('d-none');

                    // Réinitialiser l'index d'édition
                    window.editingIndex = null;
                });
            });
        }, 1000); // Attendre 1 seconde pour s'assurer que le DOM est complètement chargé

        // Ajouter également un gestionnaire d'événement global pour les clics sur les boutons
        $(document).on('click', '.save-menu-item-btn', function(e) {
            console.log("Bouton Ajouter au menu cliqué (via délégation d'événements)");
            alert("Bouton Ajouter au menu cliqué (via délégation d'événements)");
        });

        $(document).on('click', '.cancel-edit-btn', function(e) {
            console.log("Bouton Annuler cliqué (via délégation d'événements)");
        });
    });
</script>
@endsection
