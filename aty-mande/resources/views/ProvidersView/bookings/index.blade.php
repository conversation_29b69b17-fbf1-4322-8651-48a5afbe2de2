@extends('ProvidersView.layouts.dashboard')

@section('title', 'Réservations')
@section('header-title', 'Gestion des réservations')

@section('content')
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar-alt text-primary me-2"></i>
                        Réservations
                    </h5>
                    <div class="d-flex">
                        <div class="input-group me-2">
                            <input type="text" class="form-control" placeholder="Rechercher..." id="search-input">
                            <button class="btn btn-outline-secondary" type="button" id="search-button">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                        <div class="dropdown">
                            <button class="btn btn-outline-primary dropdown-toggle" type="button" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                                <i class="fas fa-filter me-1"></i> Filtrer
                            </button>
                            <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="filterDropdown">
                                <li><a class="dropdown-item" href="#" data-filter="all">Toutes les réservations</a></li>
                                <li><a class="dropdown-item" href="#" data-filter="upcoming">Réservations à venir</a></li>
                                <li><a class="dropdown-item" href="#" data-filter="past">Réservations passées</a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" data-filter="pending">En attente</a></li>
                                <li><a class="dropdown-item" href="#" data-filter="confirmed">Confirmées</a></li>
                                <li><a class="dropdown-item" href="#" data-filter="completed">Terminées</a></li>
                                <li><a class="dropdown-item" href="#" data-filter="cancelled">Annulées</a></li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="mb-4">
                    <ul class="nav nav-tabs" id="bookingTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all" type="button" role="tab" aria-controls="all" aria-selected="true">
                                Toutes <span class="badge bg-secondary ms-1">{{ count($bookings) }}</span>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending" type="button" role="tab" aria-controls="pending" aria-selected="false">
                                En attente <span class="badge bg-warning ms-1">{{ $bookings->where('status', 'pending')->count() }}</span>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="confirmed-tab" data-bs-toggle="tab" data-bs-target="#confirmed" type="button" role="tab" aria-controls="confirmed" aria-selected="false">
                                Confirmées <span class="badge bg-info ms-1">{{ $bookings->where('status', 'confirmed')->count() }}</span>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="completed-tab" data-bs-toggle="tab" data-bs-target="#completed" type="button" role="tab" aria-controls="completed" aria-selected="false">
                                Terminées <span class="badge bg-success ms-1">{{ $bookings->where('status', 'completed')->count() }}</span>
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="cancelled-tab" data-bs-toggle="tab" data-bs-target="#cancelled" type="button" role="tab" aria-controls="cancelled" aria-selected="false">
                                Annulées <span class="badge bg-danger ms-1">{{ $bookings->where('status', 'cancelled')->count() }}</span>
                            </button>
                        </li>
                    </ul>
                </div>

                <div class="tab-content" id="bookingTabsContent">
                    <div class="tab-pane fade show active" id="all" role="tabpanel" aria-labelledby="all-tab">
                        @if(count($bookings) > 0)
                            <div class="table-responsive">
                                <table class="table table-hover align-middle">
                                    <thead>
                                        <tr>
                                            <th>Référence</th>
                                            <th>Client</th>
                                            <th>Service</th>
                                            <th>Date de l'événement</th>
                                            <th>Statut</th>
                                            <th>Montant</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach($bookings as $booking)
                                            <tr class="booking-row" 
                                                data-status="{{ $booking->status }}" 
                                                data-date="{{ $booking->event_date->format('Y-m-d') }}">
                                                <td>
                                                    <a href="{{ route('provider.bookings.show', $booking->id) }}" class="fw-bold text-decoration-none">
                                                        {{ $booking->booking_number }}
                                                    </a>
                                                </td>
                                                <td>
                                                    <div class="d-flex align-items-center">
                                                        <div class="avatar-sm me-2 bg-primary text-white rounded-circle">
                                                            {{ substr($booking->client->user->name, 0, 1) }}
                                                        </div>
                                                        <div>{{ $booking->client->user->name }}</div>
                                                    </div>
                                                </td>
                                                <td>{{ $booking->providerService->title }}</td>
                                                <td>
                                                    <div>{{ $booking->event_date->format('d/m/Y') }}</div>
                                                    @if($booking->event_start_time)
                                                        <small class="text-muted">{{ $booking->event_start_time->format('H:i') }}</small>
                                                    @endif
                                                </td>
                                                <td>
                                                    <span class="badge bg-{{ 
                                                        $booking->status === 'pending' ? 'warning' : 
                                                        ($booking->status === 'confirmed' ? 'info' : 
                                                        ($booking->status === 'completed' ? 'success' : 
                                                        ($booking->status === 'cancelled' ? 'danger' : 'secondary'))) 
                                                    }}">
                                                        {{ $booking->status === 'pending' ? 'En attente' : 
                                                           ($booking->status === 'confirmed' ? 'Confirmée' : 
                                                           ($booking->status === 'completed' ? 'Terminée' : 
                                                           ($booking->status === 'cancelled' ? 'Annulée' : $booking->status))) 
                                                        }}
                                                    </span>
                                                </td>
                                                <td>{{ number_format($booking->quote->total_amount, 2) }} €</td>
                                                <td>
                                                    <a href="{{ route('provider.bookings.show', $booking->id) }}" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                </td>
                                            </tr>
                                        @endforeach
                                    </tbody>
                                </table>
                            </div>
                        @else
                            <div class="text-center py-5">
                                <img src="https://cdn-icons-png.flaticon.com/512/6598/6598519.png" alt="No bookings" style="width: 100px; opacity: 0.5;" class="mb-3">
                                <h5>Aucune réservation</h5>
                                <p class="text-muted">Vous n'avez pas encore de réservations.</p>
                            </div>
                        @endif
                    </div>

                    <!-- Contenu des autres onglets (pending, confirmed, completed, cancelled) -->
                    <!-- Ces onglets auront un contenu similaire mais filtré par statut -->
                    <div class="tab-pane fade" id="pending" role="tabpanel" aria-labelledby="pending-tab">
                        <!-- Contenu similaire mais filtré pour les réservations en attente -->
                    </div>
                    <div class="tab-pane fade" id="confirmed" role="tabpanel" aria-labelledby="confirmed-tab">
                        <!-- Contenu similaire mais filtré pour les réservations confirmées -->
                    </div>
                    <div class="tab-pane fade" id="completed" role="tabpanel" aria-labelledby="completed-tab">
                        <!-- Contenu similaire mais filtré pour les réservations terminées -->
                    </div>
                    <div class="tab-pane fade" id="cancelled" role="tabpanel" aria-labelledby="cancelled-tab">
                        <!-- Contenu similaire mais filtré pour les réservations annulées -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Calendrier des réservations -->
<div class="row">
    <div class="col-md-12">
        <div class="card border-0 shadow-sm">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar text-primary me-2"></i>
                        Calendrier des réservations
                    </h5>
                </div>
                
                <div id="bookings-calendar" style="height: 500px;"></div>
            </div>
        </div>
    </div>
</div>
@endsection

@section('styles')
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.css">
<style>
    .fc-event {
        cursor: pointer;
    }
    .fc-event-title {
        font-weight: 500;
    }
    .fc-event-time {
        font-weight: 400;
    }
    .fc-daygrid-day.fc-day-today {
        background-color: rgba(99, 102, 241, 0.1);
    }
    .fc-button-primary {
        background-color: #6366f1 !important;
        border-color: #6366f1 !important;
    }
    .fc-button-primary:hover {
        background-color: #4f46e5 !important;
        border-color: #4f46e5 !important;
    }
    .fc-button-primary:disabled {
        background-color: #a5a6f6 !important;
        border-color: #a5a6f6 !important;
    }
    .booking-pending {
        background-color: #f59e0b;
        border-color: #f59e0b;
    }
    .booking-confirmed {
        background-color: #3b82f6;
        border-color: #3b82f6;
    }
    .booking-completed {
        background-color: #10b981;
        border-color: #10b981;
    }
    .booking-cancelled {
        background-color: #ef4444;
        border-color: #ef4444;
    }
</style>
@endsection

@section('scripts')
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/main.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.10.1/locales/fr.js"></script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Initialiser le calendrier
        const calendarEl = document.getElementById('bookings-calendar');
        const calendar = new FullCalendar.Calendar(calendarEl, {
            initialView: 'dayGridMonth',
            locale: 'fr',
            headerToolbar: {
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,listWeek'
            },
            buttonText: {
                today: "Aujourd'hui",
                month: 'Mois',
                week: 'Semaine',
                list: 'Liste'
            },
            events: [
                @foreach($bookings as $booking)
                {
                    id: '{{ $booking->id }}',
                    title: '{{ $booking->title }}',
                    start: '{{ $booking->event_date->format('Y-m-d') }}T{{ $booking->event_start_time ? $booking->event_start_time->format('H:i:s') : '09:00:00' }}',
                    end: '{{ $booking->event_date->format('Y-m-d') }}T{{ $booking->event_end_time ? $booking->event_end_time->format('H:i:s') : '18:00:00' }}',
                    className: 'booking-{{ $booking->status }}',
                    extendedProps: {
                        client: '{{ $booking->client->user->name }}',
                        service: '{{ $booking->providerService->title }}',
                        status: '{{ $booking->status }}',
                        bookingNumber: '{{ $booking->booking_number }}'
                    }
                },
                @endforeach
            ],
            eventClick: function(info) {
                window.location.href = '{{ route('provider.bookings.show', '') }}/' + info.event.id;
            }
        });
        
        calendar.render();
        
        // Recherche
        const searchInput = document.getElementById('search-input');
        const searchButton = document.getElementById('search-button');
        const tableRows = document.querySelectorAll('.booking-row');
        
        function performSearch() {
            const searchTerm = searchInput.value.toLowerCase();
            
            tableRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        searchButton.addEventListener('click', performSearch);
        searchInput.addEventListener('keyup', function(e) {
            if (e.key === 'Enter') {
                performSearch();
            }
        });
        
        // Filtrage
        const filterLinks = document.querySelectorAll('[data-filter]');
        const today = new Date().toISOString().split('T')[0];
        
        filterLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const filter = this.getAttribute('data-filter');
                
                tableRows.forEach(row => {
                    const status = row.getAttribute('data-status');
                    const date = row.getAttribute('data-date');
                    
                    if (filter === 'all') {
                        row.style.display = '';
                    } else if (filter === 'upcoming' && date >= today) {
                        row.style.display = '';
                    } else if (filter === 'past' && date < today) {
                        row.style.display = '';
                    } else if (filter === status) {
                        row.style.display = '';
                    } else {
                        row.style.display = 'none';
                    }
                });
            });
        });
    });
</script>
@endsection
