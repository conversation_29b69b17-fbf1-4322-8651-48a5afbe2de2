@extends('ProvidersView.layouts.dashboard')

@section('title', 'Détails de la réservation')
@section('header-title', 'Détails de la réservation')

@section('content')
<div class="row">
    <div class="col-md-8">
        <!-- Détails de la réservation -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white py-3">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-calendar-alt text-primary me-2"></i>
                        Réservation {{ $booking->booking_number }}
                    </h5>
                    <span class="badge bg-{{ 
                        $booking->status === 'pending' ? 'warning' : 
                        ($booking->status === 'confirmed' ? 'info' : 
                        ($booking->status === 'completed' ? 'success' : 
                        ($booking->status === 'cancelled' ? 'danger' : 'secondary'))) 
                    }} px-3 py-2">
                        {{ $booking->status === 'pending' ? 'En attente' : 
                           ($booking->status === 'confirmed' ? 'Confirmée' : 
                           ($booking->status === 'completed' ? 'Terminée' : 
                           ($booking->status === 'cancelled' ? 'Annulée' : $booking->status))) 
                        }}
                    </span>
                </div>
            </div>
            <div class="card-body">
                <h4 class="mb-3">{{ $booking->title }}</h4>
                
                <div class="mb-4">
                    <h6 class="text-muted mb-2">Description</h6>
                    <p>{{ $booking->description }}</p>
                </div>
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-muted mb-2">Détails de l'événement</h6>
                        <ul class="list-unstyled">
                            <li class="mb-2">
                                <i class="fas fa-calendar-day text-primary me-2"></i>
                                <strong>Date:</strong> {{ $booking->event_date->format('d/m/Y') }}
                            </li>
                            @if($booking->event_start_time)
                                <li class="mb-2">
                                    <i class="fas fa-clock text-primary me-2"></i>
                                    <strong>Heure de début:</strong> {{ $booking->event_start_time->format('H:i') }}
                                </li>
                            @endif
                            @if($booking->event_end_time)
                                <li class="mb-2">
                                    <i class="fas fa-hourglass-end text-primary me-2"></i>
                                    <strong>Heure de fin:</strong> {{ $booking->event_end_time->format('H:i') }}
                                </li>
                            @endif
                            @if($booking->event_location)
                                <li class="mb-2">
                                    <i class="fas fa-map-marker-alt text-primary me-2"></i>
                                    <strong>Lieu:</strong> {{ $booking->event_location }}
                                </li>
                            @endif
                            @if($booking->guest_count)
                                <li class="mb-2">
                                    <i class="fas fa-users text-primary me-2"></i>
                                    <strong>Nombre d'invités:</strong> {{ $booking->guest_count }}
                                </li>
                            @endif
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted mb-2">Service réservé</h6>
                        <div class="card bg-light">
                            <div class="card-body">
                                <h6 class="card-title">{{ $booking->providerService->title }}</h6>
                                <p class="card-text small">{{ Str::limit($booking->providerService->description, 100) }}</p>
                                <a href="{{ route('provider.services.show', $booking->providerService->id) }}" class="btn btn-sm btn-outline-primary">
                                    <i class="fas fa-eye me-1"></i> Voir le service
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mb-4">
                    <h6 class="text-muted mb-2">Informations du client</h6>
                    <div class="d-flex align-items-center">
                        <div class="avatar me-3 bg-primary text-white rounded-circle" style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center; font-size: 1.5rem;">
                            {{ substr($booking->client->user->name, 0, 1) }}
                        </div>
                        <div>
                            <h6 class="mb-1">{{ $booking->client->user->name }}</h6>
                            <p class="mb-0 text-muted">
                                <i class="fas fa-envelope me-1"></i> {{ $booking->client->user->email }}
                                @if($booking->client->user->phone)
                                    <span class="ms-3"><i class="fas fa-phone me-1"></i> {{ $booking->client->user->phone }}</span>
                                @endif
                            </p>
                        </div>
                    </div>
                </div>
                
                <div class="mb-4">
                    <h6 class="text-muted mb-2">Détails du devis accepté</h6>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <thead class="table-light">
                                <tr>
                                    <th>Description</th>
                                    <th class="text-end">Montant</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>{{ $booking->providerService->title }}</td>
                                    <td class="text-end">{{ number_format($booking->quote->base_amount, 2) }} €</td>
                                </tr>
                                @if($booking->quote->options)
                                    @foreach(json_decode($booking->quote->options, true) as $option)
                                        <tr>
                                            <td>{{ $option['name'] }}</td>
                                            <td class="text-end">{{ number_format($option['price'], 2) }} €</td>
                                        </tr>
                                    @endforeach
                                @endif
                                @if($booking->quote->discount_amount > 0)
                                    <tr>
                                        <td>Remise</td>
                                        <td class="text-end text-danger">-{{ number_format($booking->quote->discount_amount, 2) }} €</td>
                                    </tr>
                                @endif
                                <tr>
                                    <td>Sous-total</td>
                                    <td class="text-end">{{ number_format($booking->quote->subtotal_amount, 2) }} €</td>
                                </tr>
                                @if($booking->quote->tax_amount > 0)
                                    <tr>
                                        <td>TVA ({{ $booking->quote->tax_rate }}%)</td>
                                        <td class="text-end">{{ number_format($booking->quote->tax_amount, 2) }} €</td>
                                    </tr>
                                @endif
                                <tr class="table-active fw-bold">
                                    <td>Total</td>
                                    <td class="text-end">{{ number_format($booking->quote->total_amount, 2) }} €</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                @if($booking->quote->notes)
                    <div class="mb-4">
                        <h6 class="text-muted mb-2">Notes du devis</h6>
                        <div class="p-3 bg-light rounded">
                            {{ $booking->quote->notes }}
                        </div>
                    </div>
                @endif
            </div>
        </div>
        
        <!-- Conversation -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white py-3">
                <h5 class="card-title mb-0">
                    <i class="fas fa-comments text-primary me-2"></i>
                    Conversation
                </h5>
            </div>
            <div class="card-body">
                <div class="messages-container" style="max-height: 400px; overflow-y: auto;">
                    @if(count($messages) > 0)
                        @foreach($messages as $message)
                            <div class="message mb-3 {{ $message->sender_id === Auth::id() ? 'text-end' : '' }}">
                                <div class="message-content {{ $message->sender_id === Auth::id() ? 'bg-primary text-white' : 'bg-light' }} d-inline-block p-3 rounded" style="max-width: 80%;">
                                    <p class="mb-1">{{ $message->message }}</p>
                                    <small class="text-{{ $message->sender_id === Auth::id() ? 'light' : 'muted' }}">
                                        {{ $message->created_at->format('d/m/Y H:i') }}
                                    </small>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <p class="text-muted">Aucun message dans cette conversation.</p>
                        </div>
                    @endif
                </div>
                
                <form action="{{ route('provider.bookings.message', $booking->id) }}" method="POST" class="mt-4">
                    @csrf
                    <div class="input-group">
                        <input type="text" name="message" class="form-control" placeholder="Tapez votre message..." required>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane me-1"></i> Envoyer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Actions -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white py-3">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tasks text-primary me-2"></i>
                    Actions
                </h5>
            </div>
            <div class="card-body">
                @if($booking->status === 'pending')
                    <form action="{{ route('provider.bookings.confirm', $booking->id) }}" method="POST">
                        @csrf
                        <button type="submit" class="btn btn-success w-100 mb-3">
                            <i class="fas fa-check-circle me-1"></i> Confirmer la réservation
                        </button>
                    </form>
                    <button type="button" class="btn btn-danger w-100 mb-3" data-bs-toggle="modal" data-bs-target="#cancelBookingModal">
                        <i class="fas fa-times-circle me-1"></i> Annuler la réservation
                    </button>
                @endif
                
                @if($booking->status === 'confirmed')
                    <form action="{{ route('provider.bookings.complete', $booking->id) }}" method="POST">
                        @csrf
                        <button type="submit" class="btn btn-success w-100 mb-3">
                            <i class="fas fa-check-double me-1"></i> Marquer comme terminée
                        </button>
                    </form>
                    <button type="button" class="btn btn-danger w-100 mb-3" data-bs-toggle="modal" data-bs-target="#cancelBookingModal">
                        <i class="fas fa-times-circle me-1"></i> Annuler la réservation
                    </button>
                @endif
                
                @if($booking->status === 'completed')
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle me-2"></i>
                        Réservation terminée le {{ $booking->completed_at->format('d/m/Y') }}
                    </div>
                @endif
                
                @if($booking->status === 'cancelled')
                    <div class="alert alert-danger">
                        <i class="fas fa-times-circle me-2"></i>
                        Réservation annulée le {{ $booking->cancelled_at->format('d/m/Y') }}
                        @if($booking->cancellation_reason)
                            <p class="mt-2 mb-0"><strong>Raison:</strong> {{ $booking->cancellation_reason }}</p>
                        @endif
                    </div>
                @endif
                
                <a href="{{ route('provider.bookings') }}" class="btn btn-outline-secondary w-100">
                    <i class="fas fa-arrow-left me-1"></i> Retour à la liste
                </a>
            </div>
        </div>
        
        <!-- Historique -->
        <div class="card border-0 shadow-sm mb-4">
            <div class="card-header bg-white py-3">
                <h5 class="card-title mb-0">
                    <i class="fas fa-history text-primary me-2"></i>
                    Historique
                </h5>
            </div>
            <div class="card-body p-0">
                <ul class="list-group list-group-flush">
                    <li class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <i class="fas fa-file-alt text-muted me-2"></i>
                            Réservation créée
                        </div>
                        <small class="text-muted">{{ $booking->created_at->format('d/m/Y H:i') }}</small>
                    </li>
                    @if($booking->status !== 'pending')
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-check-circle text-info me-2"></i>
                                Réservation confirmée
                            </div>
                            <small class="text-muted">{{ $booking->confirmed_at ? $booking->confirmed_at->format('d/m/Y H:i') : 'N/A' }}</small>
                        </li>
                    @endif
                    @if($booking->status === 'completed')
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-check-double text-success me-2"></i>
                                Réservation terminée
                            </div>
                            <small class="text-muted">{{ $booking->completed_at->format('d/m/Y H:i') }}</small>
                        </li>
                    @endif
                    @if($booking->status === 'cancelled')
                        <li class="list-group-item d-flex justify-content-between align-items-center">
                            <div>
                                <i class="fas fa-times-circle text-danger me-2"></i>
                                Réservation annulée
                            </div>
                            <small class="text-muted">{{ $booking->cancelled_at->format('d/m/Y H:i') }}</small>
                        </li>
                    @endif
                </ul>
            </div>
        </div>
        
        <!-- Avis client -->
        @if($booking->review)
            <div class="card border-0 shadow-sm mb-4">
                <div class="card-header bg-white py-3">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-star text-warning me-2"></i>
                        Avis du client
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-2">
                        @for($i = 1; $i <= 5; $i++)
                            @if($i <= $booking->review->rating)
                                <i class="fas fa-star text-warning"></i>
                            @else
                                <i class="far fa-star text-warning"></i>
                            @endif
                        @endfor
                        <span class="ms-2 text-muted">{{ $booking->review->created_at->format('d/m/Y') }}</span>
                    </div>
                    <p>{{ $booking->review->review_text }}</p>
                    
                    @if($booking->review->provider_reply)
                        <div class="mt-3 p-3 bg-light rounded">
                            <h6 class="mb-2">Votre réponse:</h6>
                            <p class="mb-0">{{ $booking->review->provider_reply }}</p>
                        </div>
                    @else
                        <form action="{{ route('provider.reviews.reply', $booking->review->id) }}" method="POST" class="mt-3">
                            @csrf
                            <div class="mb-3">
                                <label for="provider_reply" class="form-label">Répondre à cet avis</label>
                                <textarea class="form-control" id="provider_reply" name="provider_reply" rows="3" required></textarea>
                            </div>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-reply me-1"></i> Publier une réponse
                            </button>
                        </form>
                    @endif
                </div>
            </div>
        @endif
    </div>
</div>

<!-- Modal pour annuler la réservation -->
<div class="modal fade" id="cancelBookingModal" tabindex="-1" aria-labelledby="cancelBookingModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="cancelBookingModalLabel">Annuler la réservation</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="{{ route('provider.bookings.cancel', $booking->id) }}" method="POST">
                @csrf
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        Attention: L'annulation d'une réservation est définitive et sera notifiée au client.
                    </div>
                    <div class="mb-3">
                        <label for="cancellation_reason" class="form-label">Raison de l'annulation</label>
                        <textarea class="form-control" id="cancellation_reason" name="cancellation_reason" rows="3" required></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    <button type="submit" class="btn btn-danger">Confirmer l'annulation</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endsection
