<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facture #{{ $invoice->invoice_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        .invoice-container {
            max-width: 800px;
            margin: 0 auto;
            border: 1px solid #ddd;
            padding: 30px;
        }
        .invoice-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 40px;
        }
        .invoice-title {
            font-size: 24px;
            font-weight: bold;
            color: #0d6efd;
        }
        .invoice-details {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .invoice-details-col {
            flex: 1;
        }
        .invoice-details-col:last-child {
            text-align: right;
        }
        .invoice-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        .invoice-table th {
            background-color: #f8f9fa;
            text-align: left;
            padding: 10px;
            border-bottom: 2px solid #ddd;
        }
        .invoice-table td {
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }
        .invoice-table .text-end {
            text-align: right;
        }
        .invoice-table .table-light {
            background-color: #f8f9fa;
        }
        .invoice-table .table-warning {
            background-color: #fff3cd;
        }
        .invoice-table .table-success {
            background-color: #d1e7dd;
        }
        .invoice-notes {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .invoice-footer {
            text-align: center;
            margin-top: 50px;
            font-size: 12px;
            color: #6c757d;
        }
        .qr-code {
            text-align: center;
            margin-top: 30px;
        }
        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 5px;
            font-weight: bold;
            font-size: 12px;
            text-transform: uppercase;
        }
        .status-paid {
            background-color: #d1e7dd;
            color: #0f5132;
        }
        .status-partially-paid {
            background-color: #cff4fc;
            color: #055160;
        }
        .status-pending {
            background-color: #fff3cd;
            color: #664d03;
        }
        .status-overdue {
            background-color: #f8d7da;
            color: #842029;
        }
        .provider-info {
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="invoice-container">
        <div class="invoice-header">
            <div>
                <div class="invoice-title">FACTURE</div>
                <div>#{{ $invoice->invoice_number }}</div>
            </div>
            <div>
                <div class="status-badge status-{{ $invoice->status }}">
                    {{ $invoice->status === 'paid' ? 'Payée' : 
                       ($invoice->status === 'partially_paid' ? 'Partiellement payée' : 
                       ($invoice->status === 'overdue' ? 'En retard' : 'En attente')) 
                    }}
                </div>
            </div>
        </div>

        <div class="provider-info">
            <h3>{{ auth()->user()->provider->business_name }}</h3>
            <p>{{ auth()->user()->provider->address ?? '' }}</p>
            <p>{{ auth()->user()->provider->city ?? '' }}, {{ auth()->user()->provider->country ?? '' }}</p>
            <p>Tél: {{ auth()->user()->provider->business_phone ?? auth()->user()->phone ?? 'Non spécifié' }}</p>
            <p>Email: {{ auth()->user()->provider->business_email ?? auth()->user()->email }}</p>
        </div>

        <div class="invoice-details">
            <div class="invoice-details-col">
                <h3>Facturé à</h3>
                <p>{{ $invoice->client->user->name }}</p>
                <p>{{ $invoice->client->user->email }}</p>
                <p>{{ $invoice->client->user->phone ?? 'Téléphone non spécifié' }}</p>
            </div>
            <div class="invoice-details-col">
                <h3>Détails</h3>
                <p><strong>Facture #:</strong> {{ $invoice->invoice_number }}</p>
                <p><strong>Date d'émission:</strong> {{ $invoice->created_at->format('d/m/Y') }}</p>
                <p><strong>Date d'échéance:</strong> {{ $invoice->due_date->format('d/m/Y') }}</p>
                <p><strong>Réservation:</strong> {{ $invoice->booking->booking_number }}</p>
            </div>
        </div>

        <h3>Détails de la facture</h3>
        <table class="invoice-table">
            <thead>
                <tr>
                    <th>Description</th>
                    <th class="text-end">Montant</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>{{ $invoice->booking->providerService->title }}</td>
                    <td class="text-end">{{ number_format($invoice->subtotal_amount, 0, ',', ' ') }} Ar</td>
                </tr>
                
                @if($invoice->tax_amount > 0)
                    <tr>
                        <td>TVA</td>
                        <td class="text-end">{{ number_format($invoice->tax_amount, 0, ',', ' ') }} Ar</td>
                    </tr>
                @endif
                
                @if($invoice->discount_amount > 0)
                    <tr>
                        <td>Remise</td>
                        <td class="text-end">-{{ number_format($invoice->discount_amount, 0, ',', ' ') }} Ar</td>
                    </tr>
                @endif
                
                <tr>
                    <td><strong>Sous-total</strong></td>
                    <td class="text-end"><strong>{{ number_format($invoice->subtotal_amount + $invoice->tax_amount - $invoice->discount_amount, 0, ',', ' ') }} Ar</strong></td>
                </tr>
                
                <tr class="table-light">
                    <td><strong>Total</strong></td>
                    <td class="text-end"><strong>{{ number_format($invoice->total_amount, 0, ',', ' ') }} Ar</strong></td>
                </tr>
                
                <tr>
                    <td>Montant payé</td>
                    <td class="text-end">{{ number_format($invoice->paid_amount, 0, ',', ' ') }} Ar</td>
                </tr>
                
                <tr class="{{ $invoice->due_amount > 0 ? 'table-warning' : 'table-success' }}">
                    <td><strong>Solde restant</strong></td>
                    <td class="text-end"><strong>{{ number_format($invoice->due_amount, 0, ',', ' ') }} Ar</strong></td>
                </tr>
            </tbody>
        </table>
        
        @if($invoice->notes)
            <div class="invoice-notes">
                <h3>Notes / Conditions</h3>
                <p>{{ $invoice->notes }}</p>
            </div>
        @endif

        <div class="qr-code">
            <img src="https://api.qrserver.com/v1/create-qr-code/?size=150x150&data={{ urlencode(json_encode([
                'invoice_number' => $invoice->invoice_number,
                'amount' => $invoice->total_amount,
                'client' => $invoice->client->user->name,
                'provider' => auth()->user()->provider->business_name,
                'date' => $invoice->created_at->format('Y-m-d'),
            ])) }}" alt="QR Code">
        </div>

        <div class="invoice-footer">
            <p>Cette facture a été générée par {{ auth()->user()->provider->business_name }} via Planifeo.</p>
            <p>Pour toute question concernant cette facture, veuillez nous contacter à {{ auth()->user()->provider->business_email ?? auth()->user()->email }}.</p>
        </div>
    </div>
</body>
</html>
