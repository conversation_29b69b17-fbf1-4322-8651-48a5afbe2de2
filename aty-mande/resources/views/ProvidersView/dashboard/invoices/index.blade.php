@extends('ProvidersView.layouts.dashboard')

@section('title', 'Gestion des factures')

@section('header-title', 'Gestion des factures')

@section('content')
    @if (session('status'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('status') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="dashboard-card mb-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h5 class="card-title mb-0">
                <i class="fas fa-file-invoice-dollar me-2 text-primary"></i>
                Toutes les factures
            </h5>
            <div class="d-flex">
                <div class="input-group me-2" style="width: 250px;">
                    <input type="text" class="form-control" id="search-input" placeholder="Rechercher...">
                    <button class="btn btn-outline-secondary" type="button" id="search-button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <div class="dropdown">
                    <button class="btn btn-outline-primary dropdown-toggle" type="button" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-filter me-1"></i>Filtrer
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="filterDropdown">
                        <li><a class="dropdown-item filter-item" href="#" data-filter="all">Toutes les factures</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item filter-item" href="#" data-filter="pending">En attente</a></li>
                        <li><a class="dropdown-item filter-item" href="#" data-filter="partially_paid">Partiellement payées</a></li>
                        <li><a class="dropdown-item filter-item" href="#" data-filter="paid">Payées</a></li>
                        <li><a class="dropdown-item filter-item" href="#" data-filter="overdue">En retard</a></li>
                    </ul>
                </div>
            </div>
        </div>

        @if(count($invoices) > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Facture</th>
                            <th>Client</th>
                            <th>Réservation</th>
                            <th>Date</th>
                            <th>Montant</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($invoices as $invoice)
                            <tr class="invoice-row" data-status="{{ $invoice->status }}">
                                <td>
                                    <div class="d-flex flex-column">
                                        <span class="fw-bold">{{ $invoice->invoice_number }}</span>
                                        <small class="text-muted">{{ $invoice->created_at->format('d/m/Y') }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px; font-size: 0.8rem;">
                                            {{ substr($invoice->client->user->name, 0, 1) }}
                                        </div>
                                        <div class="d-flex flex-column">
                                            <span>{{ $invoice->client->user->name }}</span>
                                            <small class="text-muted">{{ $invoice->client->user->email }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <span>{{ $invoice->booking->booking_number }}</span>
                                        <small class="text-muted">{{ Str::limit($invoice->booking->providerService->title, 20) }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <span>Émise le {{ $invoice->created_at->format('d/m/Y') }}</span>
                                        <small class="text-muted">Échéance le {{ $invoice->due_date->format('d/m/Y') }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <span class="fw-bold">{{ number_format($invoice->total_amount, 0, ',', ' ') }} Ar</span>
                                        @if($invoice->paid_amount > 0 && $invoice->paid_amount < $invoice->total_amount)
                                            <small class="text-muted">Payé: {{ number_format($invoice->paid_amount, 0, ',', ' ') }} Ar</small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 
                                        $invoice->status === 'paid' ? 'success' : 
                                        ($invoice->status === 'partially_paid' ? 'info' : 
                                        ($invoice->status === 'overdue' ? 'danger' : 'warning')) 
                                    }}">
                                        {{ $invoice->status === 'paid' ? 'Payée' : 
                                           ($invoice->status === 'partially_paid' ? 'Partiellement payée' : 
                                           ($invoice->status === 'overdue' ? 'En retard' : 'En attente')) 
                                        }}
                                    </span>
                                </td>
                                <td>
                                    <div class="d-flex">
                                        <a href="{{ route('provider.invoices.show', $invoice->id) }}" class="btn btn-sm btn-outline-primary me-2" title="Voir les détails">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="{{ route('provider.invoices.download', $invoice->id) }}" class="btn btn-sm btn-outline-secondary" title="Télécharger">
                                            <i class="fas fa-download"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <div class="d-flex justify-content-center mt-4">
                {{ $invoices->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <img src="https://cdn-icons-png.flaticon.com/512/6598/6598519.png" alt="No invoices" style="width: 100px; opacity: 0.5;" class="mb-3">
                <h5>Aucune facture trouvée</h5>
                <p class="text-muted">Vous n'avez pas encore créé de factures. Créez une facture pour une réservation existante.</p>
            </div>
        @endif
    </div>

    <div class="dashboard-card mb-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h5 class="card-title mb-0">
                <i class="fas fa-bookmark me-2 text-primary"></i>
                Réservations sans facture
            </h5>
        </div>

        @php
            $bookingsWithoutInvoice = \App\Models\Booking::where('provider_id', auth()->user()->provider->id)
                ->whereDoesntHave('invoices')
                ->with(['client', 'client.user', 'providerService'])
                ->orderBy('created_at', 'desc')
                ->take(5)
                ->get();
        @endphp

        @if(count($bookingsWithoutInvoice) > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-light">
                        <tr>
                            <th>Réservation</th>
                            <th>Client</th>
                            <th>Service</th>
                            <th>Date</th>
                            <th>Montant</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($bookingsWithoutInvoice as $booking)
                            <tr>
                                <td>
                                    <div class="d-flex flex-column">
                                        <span class="fw-bold">{{ $booking->booking_number }}</span>
                                        <small class="text-muted">{{ $booking->created_at->format('d/m/Y') }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px; font-size: 0.8rem;">
                                            {{ substr($booking->client->user->name, 0, 1) }}
                                        </div>
                                        <div class="d-flex flex-column">
                                            <span>{{ $booking->client->user->name }}</span>
                                            <small class="text-muted">{{ $booking->client->user->email }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <span>{{ Str::limit($booking->providerService->title, 30) }}</span>
                                        <small class="text-muted">{{ $booking->providerService->category->name }}</small>
                                    </div>
                                </td>
                                <td>
                                    <div class="d-flex flex-column">
                                        <span>{{ $booking->event_date ? $booking->event_date->format('d/m/Y') : 'Non spécifiée' }}</span>
                                        @if($booking->event_start_time)
                                            <small class="text-muted">
                                                {{ \Carbon\Carbon::parse($booking->event_start_time)->format('H:i') }}
                                                @if($booking->event_end_time)
                                                    - {{ \Carbon\Carbon::parse($booking->event_end_time)->format('H:i') }}
                                                @endif
                                            </small>
                                        @endif
                                    </div>
                                </td>
                                <td>
                                    <span class="fw-bold">{{ number_format($booking->total_amount, 0, ',', ' ') }} Ar</span>
                                </td>
                                <td>
                                    <a href="{{ route('provider.invoices.create', $booking->id) }}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-plus me-1"></i>Créer une facture
                                    </a>
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @else
            <div class="text-center py-4">
                <p class="text-muted">Toutes vos réservations ont des factures associées.</p>
            </div>
        @endif
    </div>
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Recherche
        const searchInput = document.getElementById('search-input');
        const searchButton = document.getElementById('search-button');
        const invoiceRows = document.querySelectorAll('.invoice-row');
        
        function performSearch() {
            const searchTerm = searchInput.value.toLowerCase();
            
            invoiceRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        if (searchButton) {
            searchButton.addEventListener('click', performSearch);
        }
        
        if (searchInput) {
            searchInput.addEventListener('keyup', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        }
        
        // Filtrage
        const filterItems = document.querySelectorAll('.filter-item');
        
        filterItems.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                
                const filter = this.dataset.filter;
                
                invoiceRows.forEach(row => {
                    if (filter === 'all') {
                        row.style.display = '';
                    } else {
                        if (row.dataset.status === filter) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    }
                });
            });
        });
    });
</script>
@endpush
