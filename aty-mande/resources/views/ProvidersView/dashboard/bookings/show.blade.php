@extends('ProvidersView.layouts.dashboard')

@section('title', 'Détails de la réservation')

@section('header-title', 'Détails de la réservation')

@section('content')
    @if (session('status'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('status') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="row">
        <div class="col-md-8">
            <!-- Booking Details -->
            <div class="dashboard-card mb-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-bookmark me-2 text-primary"></i>
                        Réservation #{{ $booking->booking_number }}
                    </h5>
                    <div>
                        <span class="badge bg-{{
                            $booking->status === 'pending' ? 'warning' :
                            ($booking->status === 'confirmed' ? 'success' :
                            ($booking->status === 'completed' ? 'info' :
                            ($booking->status === 'cancelled' ? 'danger' : 'secondary')))
                        }} fs-6">
                            {{ $booking->status === 'pending' ? 'En attente' :
                               ($booking->status === 'confirmed' ? 'Confirmée' :
                               ($booking->status === 'completed' ? 'Terminée' :
                               ($booking->status === 'cancelled' ? 'Annulée' : 'Inconnue')))
                            }}
                        </span>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Client</p>
                        <div class="d-flex align-items-center">
                            <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-2" style="width: 40px; height: 40px;">
                                {{ substr($booking->client->user->name, 0, 1) }}
                            </div>
                            <div>
                                <h6 class="mb-0">{{ $booking->client->user->name }}</h6>
                                <p class="small text-muted mb-0">{{ $booking->client->user->email }}</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Service réservé</p>
                        <h6>{{ $booking->providerService->title }}</h6>
                        <p class="small text-muted">
                            <i class="fas fa-tag me-1"></i>
                            {{ $booking->providerService->category->name }}
                        </p>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Date de l'événement</p>
                        <h6>{{ $booking->event_date ? $booking->event_date->format('d/m/Y') : 'Non spécifiée' }}</h6>
                        @if($booking->event_start_time)
                            <p class="small text-muted">
                                <i class="fas fa-clock me-1"></i>
                                {{ \Carbon\Carbon::parse($booking->event_start_time)->format('H:i') }}
                                @if($booking->event_end_time)
                                    - {{ \Carbon\Carbon::parse($booking->event_end_time)->format('H:i') }}
                                @endif
                            </p>
                        @endif
                    </div>
                    <div class="col-md-6">
                        <p class="text-muted mb-1">Lieu de l'événement</p>
                        <h6>{{ $booking->event_location ?: 'Non spécifié' }}</h6>
                    </div>
                </div>

                <div class="mb-4">
                    <p class="text-muted mb-1">Description</p>
                    <div class="p-3 bg-light rounded">
                        {{ $booking->description ?: 'Aucune description fournie.' }}
                    </div>
                </div>

                <div class="mb-4">
                    <h6 class="fw-bold mb-3">Détails du paiement</h6>
                    <div class="table-responsive">
                        <table class="table table-bordered">
                            <tbody>
                                <tr>
                                    <td>Montant total</td>
                                    <td class="text-end fw-bold">{{ number_format($booking->total_amount, 0, ',', ' ') }} Ar</td>
                                </tr>
                                <tr>
                                    <td>Statut du paiement</td>
                                    <td class="text-end">
                                        <span class="badge bg-{{ $booking->payment_status === 'paid' ? 'success' : 'warning' }}">
                                            {{ $booking->payment_status === 'paid' ? 'Payé' : 'En attente' }}
                                        </span>
                                    </td>
                                </tr>
                                @if($booking->payment_date)
                                    <tr>
                                        <td>Date de paiement</td>
                                        <td class="text-end">{{ $booking->payment_date->format('d/m/Y') }}</td>
                                    </tr>
                                @endif
                            </tbody>
                        </table>
                    </div>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="{{ route('provider.bookings') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Retour à la liste
                    </a>

                    <div>
                        @if($booking->invoices()->exists())
                            <a href="{{ route('provider.invoices.show', $booking->invoices()->latest()->first()->id) }}" class="btn btn-outline-primary me-2">
                                <i class="fas fa-file-invoice me-2"></i>Voir la facture
                            </a>
                        @else
                            <a href="{{ route('provider.invoices.create', $booking->id) }}" class="btn btn-primary me-2">
                                <i class="fas fa-file-invoice-dollar me-2"></i>Créer une facture
                            </a>
                        @endif

                        @if($booking->status === 'pending')
                            <button type="button" class="btn btn-success me-2" data-bs-toggle="modal" data-bs-target="#confirmModal">
                                <i class="fas fa-check me-2"></i>Confirmer
                            </button>
                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#cancelModal">
                                <i class="fas fa-times me-2"></i>Annuler
                            </button>
                        @elseif($booking->status === 'confirmed')
                            <button type="button" class="btn btn-info me-2" data-bs-toggle="modal" data-bs-target="#completeModal">
                                <i class="fas fa-check-double me-2"></i>Marquer comme terminée
                            </button>
                            <button type="button" class="btn btn-danger" data-bs-toggle="modal" data-bs-target="#cancelModal">
                                <i class="fas fa-times me-2"></i>Annuler
                            </button>
                        @endif
                    </div>
                </div>
            </div>

            <!-- Messages -->
            <div class="dashboard-card mb-4">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-comments me-2 text-primary"></i>
                        Messages
                    </h5>
                </div>

                <div class="messages-container mb-4" style="max-height: 400px; overflow-y: auto;">
                    @if(isset($messages) && count($messages) > 0)
                        @foreach($messages as $message)
                            <div class="message-item d-flex mb-3 {{ $message->sender_id === Auth::id() ? 'justify-content-end' : '' }}">
                                <div class="message-content {{ $message->sender_id === Auth::id() ? 'bg-primary text-white' : 'bg-light' }}" style="max-width: 80%; border-radius: 10px; padding: 10px 15px;">
                                    <div class="message-text">
                                        {{ $message->message }}
                                    </div>
                                    <div class="message-meta d-flex justify-content-between align-items-center mt-2">
                                        <small class="{{ $message->sender_id === Auth::id() ? 'text-white-50' : 'text-muted' }}">
                                            {{ $message->sender_id === Auth::id() ? 'Vous' : $booking->client->user->name }}
                                        </small>
                                        <small class="{{ $message->sender_id === Auth::id() ? 'text-white-50' : 'text-muted' }}">
                                            {{ $message->created_at->format('d/m/Y H:i') }}
                                        </small>
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    @else
                        <div class="text-center py-4">
                            <img src="https://cdn-icons-png.flaticon.com/512/6598/6598519.png" alt="No messages" style="width: 80px; opacity: 0.5;" class="mb-3">
                            <p class="text-muted">Aucun message pour le moment</p>
                        </div>
                    @endif
                </div>

                @if(in_array($booking->status, ['pending', 'confirmed', 'completed']))
                    <form action="{{ route('provider.bookings.message', $booking->id) }}" method="POST">
                        @csrf
                        <div class="input-group">
                            <input type="text" class="form-control" name="message" placeholder="Tapez votre message..." required>
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </form>
                @endif
            </div>
        </div>

        <div class="col-md-4">
            <!-- Client Info -->
            <div class="dashboard-card mb-4">
                <h5 class="card-title mb-3">
                    <i class="fas fa-user me-2 text-primary"></i>
                    Informations du client
                </h5>

                <div class="text-center mb-4">
                    <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 80px; height: 80px; font-size: 2rem;">
                        {{ substr($booking->client->user->name, 0, 1) }}
                    </div>
                    <h5>{{ $booking->client->user->name }}</h5>
                </div>

                <div class="mb-3">
                    <div class="d-flex align-items-center mb-2">
                        <div class="bg-light rounded-circle p-2 me-3">
                            <i class="fas fa-envelope text-primary"></i>
                        </div>
                        <div>{{ $booking->client->user->email }}</div>
                    </div>
                    @if($booking->client->user->phone)
                        <div class="d-flex align-items-center mb-2">
                            <div class="bg-light rounded-circle p-2 me-3">
                                <i class="fas fa-phone text-primary"></i>
                            </div>
                            <div>{{ $booking->client->user->phone }}</div>
                        </div>
                    @endif
                </div>
            </div>

            <!-- Service Info -->
            <div class="dashboard-card mb-4">
                <h5 class="card-title mb-3">
                    <i class="fas fa-tag me-2 text-primary"></i>
                    Détails du service
                </h5>

                @if($booking->providerService->image)
                    <img src="{{ asset('storage/' . $booking->providerService->image) }}" alt="{{ $booking->providerService->title }}" class="img-fluid rounded mb-3" style="width: 100%; height: 150px; object-fit: cover;">
                @endif

                <h6>{{ $booking->providerService->title }}</h6>
                <p class="text-muted small mb-3">{{ $booking->providerService->short_description }}</p>

                <div class="d-flex justify-content-between mb-2">
                    <span class="text-muted">Catégorie:</span>
                    <span>{{ $booking->providerService->category->name }}</span>
                </div>

                <div class="d-flex justify-content-between">
                    <span class="text-muted">Prix de base:</span>
                    <span class="fw-bold">{{ number_format($booking->providerService->base_price, 0, ',', ' ') }} Ar</span>
                </div>

                <hr>

                <div class="d-grid">
                    <a href="{{ route('provider.services.show', $booking->providerService->id) }}" class="btn btn-sm btn-outline-primary">
                        <i class="fas fa-eye me-2"></i>Voir les détails du service
                    </a>
                </div>
            </div>

            <!-- Timeline -->
            <div class="dashboard-card mb-4">
                <h5 class="card-title mb-3">
                    <i class="fas fa-history me-2 text-primary"></i>
                    Historique
                </h5>

                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="mb-0">Réservation créée</h6>
                            <p class="small text-muted mb-0">{{ $booking->created_at->format('d/m/Y H:i') }}</p>
                        </div>
                    </div>

                    @if($booking->status === 'confirmed' || $booking->status === 'completed' || $booking->status === 'cancelled')
                        <div class="timeline-item">
                            <div class="timeline-marker {{ $booking->status === 'cancelled' ? 'bg-danger' : 'bg-success' }}"></div>
                            <div class="timeline-content">
                                <h6 class="mb-0">{{ $booking->status === 'cancelled' ? 'Réservation annulée' : 'Réservation confirmée' }}</h6>
                                <p class="small text-muted mb-0">{{ $booking->confirmed_at ? $booking->confirmed_at->format('d/m/Y H:i') : ($booking->cancelled_at ? $booking->cancelled_at->format('d/m/Y H:i') : $booking->updated_at->format('d/m/Y H:i')) }}</p>
                            </div>
                        </div>
                    @endif

                    @if($booking->status === 'completed')
                        <div class="timeline-item">
                            <div class="timeline-marker bg-info"></div>
                            <div class="timeline-content">
                                <h6 class="mb-0">Service terminé</h6>
                                <p class="small text-muted mb-0">{{ $booking->completed_at ? $booking->completed_at->format('d/m/Y H:i') : $booking->updated_at->format('d/m/Y H:i') }}</p>
                            </div>
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>

    <!-- Confirm Modal -->
    @if($booking->status === 'pending')
        <div class="modal fade" id="confirmModal" tabindex="-1" aria-labelledby="confirmModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-success text-white">
                        <h5 class="modal-title" id="confirmModalLabel">
                            <i class="fas fa-check-circle me-2"></i>Confirmer la réservation
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Êtes-vous sûr de vouloir confirmer cette réservation ?</p>
                        <p class="text-muted small">En confirmant cette réservation, vous vous engagez à fournir les services demandés à la date spécifiée.</p>

                        <div class="card bg-light mb-3">
                            <div class="card-body">
                                <h6 class="card-title">{{ $booking->providerService->title }}</h6>
                                <p class="card-text small mb-2">Réservation #{{ $booking->booking_number }}</p>
                                <div class="d-flex justify-content-between">
                                    <span><strong>Client:</strong> {{ $booking->client->user->name }}</span>
                                    <span><strong>Date:</strong> {{ $booking->event_date ? $booking->event_date->format('d/m/Y') : 'Non spécifiée' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <form action="{{ route('provider.bookings.confirm', $booking->id) }}" method="POST">
                            @csrf
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-check me-2"></i>Confirmer la réservation
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Complete Modal -->
    @if($booking->status === 'confirmed')
        <div class="modal fade" id="completeModal" tabindex="-1" aria-labelledby="completeModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-info text-white">
                        <h5 class="modal-title" id="completeModalLabel">
                            <i class="fas fa-check-double me-2"></i>Marquer comme terminée
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Êtes-vous sûr de vouloir marquer cette réservation comme terminée ?</p>
                        <p class="text-muted small">Cette action indique que vous avez fourni tous les services demandés.</p>

                        <div class="card bg-light mb-3">
                            <div class="card-body">
                                <h6 class="card-title">{{ $booking->providerService->title }}</h6>
                                <p class="card-text small mb-2">Réservation #{{ $booking->booking_number }}</p>
                                <div class="d-flex justify-content-between">
                                    <span><strong>Client:</strong> {{ $booking->client->user->name }}</span>
                                    <span><strong>Date:</strong> {{ $booking->event_date ? $booking->event_date->format('d/m/Y') : 'Non spécifiée' }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <form action="{{ route('provider.bookings.complete', $booking->id) }}" method="POST">
                            @csrf
                            <button type="submit" class="btn btn-info">
                                <i class="fas fa-check-double me-2"></i>Marquer comme terminée
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    @endif

    <!-- Cancel Modal -->
    @if(in_array($booking->status, ['pending', 'confirmed']))
        <div class="modal fade" id="cancelModal" tabindex="-1" aria-labelledby="cancelModalLabel" aria-hidden="true">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header bg-danger text-white">
                        <h5 class="modal-title" id="cancelModalLabel">
                            <i class="fas fa-times-circle me-2"></i>Annuler la réservation
                        </h5>
                        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                    </div>
                    <form action="{{ route('provider.bookings.cancel', $booking->id) }}" method="POST">
                        @csrf
                        <div class="modal-body">
                            <div class="alert alert-warning">
                                <div class="d-flex">
                                    <div class="me-3">
                                        <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                                    </div>
                                    <div>
                                        <h5 class="alert-heading">Confirmation d'annulation</h5>
                                        <p>Êtes-vous sûr de vouloir annuler cette réservation ? Cette action est irréversible.</p>
                                    </div>
                                </div>
                            </div>

                            <div class="card bg-light mb-4">
                                <div class="card-body">
                                    <h6 class="card-title">{{ $booking->providerService->title }}</h6>
                                    <p class="card-text small mb-2">Réservation #{{ $booking->booking_number }}</p>
                                    <div class="d-flex justify-content-between">
                                        <span><strong>Client:</strong> {{ $booking->client->user->name }}</span>
                                        <span><strong>Date:</strong> {{ $booking->event_date ? $booking->event_date->format('d/m/Y') : 'Non spécifiée' }}</span>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="cancellation_reason" class="form-label">Raison de l'annulation</label>
                                <textarea class="form-control" id="cancellation_reason" name="cancellation_reason" rows="3" placeholder="Expliquez pourquoi vous annulez cette réservation..." required></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-times me-2"></i>Confirmer l'annulation
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    @endif
@endsection

@push('styles')
<style>
    .timeline {
        position: relative;
        padding-left: 30px;
    }

    .timeline:before {
        content: '';
        position: absolute;
        left: 10px;
        top: 0;
        bottom: 0;
        width: 2px;
        background: #e9ecef;
    }

    .timeline-item {
        position: relative;
        margin-bottom: 20px;
    }

    .timeline-marker {
        position: absolute;
        left: -30px;
        width: 20px;
        height: 20px;
        border-radius: 50%;
        background: #6c757d;
        border: 3px solid #fff;
        box-shadow: 0 0 0 2px #e9ecef;
    }

    .timeline-content {
        padding-bottom: 10px;
        border-bottom: 1px solid #e9ecef;
    }
</style>
@endpush
