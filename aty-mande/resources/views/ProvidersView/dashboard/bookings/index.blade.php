@extends('ProvidersView.layouts.dashboard')

@section('title', 'Réservations')

@section('header-title', 'Gestion des réservations')

@section('content')
    @if (session('status'))
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            {{ session('status') }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
    @endif

    <div class="dashboard-card mb-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h5 class="card-title mb-0">
                <i class="fas fa-bookmark me-2 text-primary"></i>
                Toutes les réservations
            </h5>
            <div class="d-flex">
                <div class="input-group me-2" style="width: 250px;">
                    <input type="text" class="form-control" id="search-input" placeholder="Rechercher...">
                    <button class="btn btn-outline-secondary" type="button" id="search-button">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                <div class="dropdown">
                    <button class="btn btn-outline-primary dropdown-toggle" type="button" id="filterDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-filter me-1"></i>Filtrer
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="filterDropdown">
                        <li><a class="dropdown-item filter-item" href="#" data-filter="all">Toutes les réservations</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item filter-item" href="#" data-filter="pending">En attente</a></li>
                        <li><a class="dropdown-item filter-item" href="#" data-filter="confirmed">Confirmées</a></li>
                        <li><a class="dropdown-item filter-item" href="#" data-filter="completed">Terminées</a></li>
                        <li><a class="dropdown-item filter-item" href="#" data-filter="cancelled">Annulées</a></li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="mb-4">
            <ul class="nav nav-tabs" id="bookingTabs" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="all-tab" data-bs-toggle="tab" data-bs-target="#all-bookings" type="button" role="tab" aria-controls="all-bookings" aria-selected="true">
                        Toutes <span class="badge bg-secondary ms-1">{{ $bookings->total() }}</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="pending-tab" data-bs-toggle="tab" data-bs-target="#pending-bookings" type="button" role="tab" aria-controls="pending-bookings" aria-selected="false">
                        En attente <span class="badge bg-warning ms-1">{{ $bookings->where('status', 'pending')->count() }}</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="confirmed-tab" data-bs-toggle="tab" data-bs-target="#confirmed-bookings" type="button" role="tab" aria-controls="confirmed-bookings" aria-selected="false">
                        Confirmées <span class="badge bg-success ms-1">{{ $bookings->where('status', 'confirmed')->count() }}</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="completed-tab" data-bs-toggle="tab" data-bs-target="#completed-bookings" type="button" role="tab" aria-controls="completed-bookings" aria-selected="false">
                        Terminées <span class="badge bg-info ms-1">{{ $bookings->where('status', 'completed')->count() }}</span>
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="cancelled-tab" data-bs-toggle="tab" data-bs-target="#cancelled-bookings" type="button" role="tab" aria-controls="cancelled-bookings" aria-selected="false">
                        Annulées <span class="badge bg-danger ms-1">{{ $bookings->where('status', 'cancelled')->count() }}</span>
                    </button>
                </li>
            </ul>
        </div>

        <div class="tab-content" id="bookingTabsContent">
            <div class="tab-pane fade show active" id="all-bookings" role="tabpanel" aria-labelledby="all-tab">
                @if(count($bookings) > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>Réservation</th>
                                    <th>Client</th>
                                    <th>Service</th>
                                    <th>Date</th>
                                    <th>Montant</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($bookings as $booking)
                                    <tr class="booking-row" data-status="{{ $booking->status }}">
                                        <td>
                                            <div class="d-flex flex-column">
                                                <span class="fw-bold">{{ $booking->booking_number }}</span>
                                                <small class="text-muted">{{ $booking->created_at->format('d/m/Y') }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="rounded-circle bg-primary text-white d-flex align-items-center justify-content-center me-2" style="width: 30px; height: 30px; font-size: 0.8rem;">
                                                    {{ substr($booking->client->user->name, 0, 1) }}
                                                </div>
                                                <div class="d-flex flex-column">
                                                    <span>{{ $booking->client->user->name }}</span>
                                                    <small class="text-muted">{{ $booking->client->user->email }}</small>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <span>{{ Str::limit($booking->providerService->title, 30) }}</span>
                                                <small class="text-muted">{{ $booking->providerService->category->name }}</small>
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <span>{{ $booking->event_date ? $booking->event_date->format('d/m/Y') : 'Non spécifiée' }}</span>
                                                @if($booking->event_start_time)
                                                    <small class="text-muted">
                                                        {{ \Carbon\Carbon::parse($booking->event_start_time)->format('H:i') }}
                                                        @if($booking->event_end_time)
                                                            - {{ \Carbon\Carbon::parse($booking->event_end_time)->format('H:i') }}
                                                        @endif
                                                    </small>
                                                @endif
                                            </div>
                                        </td>
                                        <td>
                                            <span class="fw-bold">{{ number_format($booking->total_amount, 0, ',', ' ') }} Ar</span>
                                        </td>
                                        <td>
                                            <span class="badge bg-{{ 
                                                $booking->status === 'pending' ? 'warning' : 
                                                ($booking->status === 'confirmed' ? 'success' : 
                                                ($booking->status === 'completed' ? 'info' : 
                                                ($booking->status === 'cancelled' ? 'danger' : 'secondary'))) 
                                            }}">
                                                {{ $booking->status === 'pending' ? 'En attente' : 
                                                ($booking->status === 'confirmed' ? 'Confirmée' : 
                                                ($booking->status === 'completed' ? 'Terminée' : 
                                                ($booking->status === 'cancelled' ? 'Annulée' : 'Inconnue'))) 
                                                }}
                                            </span>
                                        </td>
                                        <td>
                                            <div class="d-flex">
                                                <a href="{{ route('provider.bookings.show', $booking->id) }}" class="btn btn-sm btn-outline-primary me-2" title="Voir les détails">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                @if($booking->status === 'pending')
                                                    <button type="button" class="btn btn-sm btn-outline-success me-2" title="Confirmer" data-bs-toggle="modal" data-bs-target="#confirmModal{{ $booking->id }}">
                                                        <i class="fas fa-check"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" title="Annuler" data-bs-toggle="modal" data-bs-target="#cancelModal{{ $booking->id }}">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                @elseif($booking->status === 'confirmed')
                                                    <button type="button" class="btn btn-sm btn-outline-info me-2" title="Marquer comme terminée" data-bs-toggle="modal" data-bs-target="#completeModal{{ $booking->id }}">
                                                        <i class="fas fa-check-double"></i>
                                                    </button>
                                                    <button type="button" class="btn btn-sm btn-outline-danger" title="Annuler" data-bs-toggle="modal" data-bs-target="#cancelModal{{ $booking->id }}">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    <div class="d-flex justify-content-center mt-4">
                        {{ $bookings->links() }}
                    </div>
                @else
                    <div class="text-center py-5">
                        <img src="https://cdn-icons-png.flaticon.com/512/6598/6598519.png" alt="No bookings" style="width: 100px; opacity: 0.5;" class="mb-3">
                        <h5>Aucune réservation trouvée</h5>
                        <p class="text-muted">Vous n'avez pas encore de réservations.</p>
                    </div>
                @endif
            </div>
            
            <div class="tab-pane fade" id="pending-bookings" role="tabpanel" aria-labelledby="pending-tab">
                <!-- Contenu filtré automatiquement par JavaScript -->
            </div>
            
            <div class="tab-pane fade" id="confirmed-bookings" role="tabpanel" aria-labelledby="confirmed-tab">
                <!-- Contenu filtré automatiquement par JavaScript -->
            </div>
            
            <div class="tab-pane fade" id="completed-bookings" role="tabpanel" aria-labelledby="completed-tab">
                <!-- Contenu filtré automatiquement par JavaScript -->
            </div>
            
            <div class="tab-pane fade" id="cancelled-bookings" role="tabpanel" aria-labelledby="cancelled-tab">
                <!-- Contenu filtré automatiquement par JavaScript -->
            </div>
        </div>
    </div>

    <!-- Modals for actions -->
    @foreach($bookings as $booking)
        @if($booking->status === 'pending')
            <!-- Confirm Modal -->
            <div class="modal fade" id="confirmModal{{ $booking->id }}" tabindex="-1" aria-labelledby="confirmModalLabel{{ $booking->id }}" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header bg-success text-white">
                            <h5 class="modal-title" id="confirmModalLabel{{ $booking->id }}">
                                <i class="fas fa-check-circle me-2"></i>Confirmer la réservation
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p>Êtes-vous sûr de vouloir confirmer cette réservation ?</p>
                            <p class="text-muted small">En confirmant cette réservation, vous vous engagez à fournir les services demandés à la date spécifiée.</p>
                            
                            <div class="card bg-light mb-3">
                                <div class="card-body">
                                    <h6 class="card-title">{{ $booking->providerService->title }}</h6>
                                    <p class="card-text small mb-2">Réservation #{{ $booking->booking_number }}</p>
                                    <div class="d-flex justify-content-between">
                                        <span><strong>Client:</strong> {{ $booking->client->user->name }}</span>
                                        <span><strong>Date:</strong> {{ $booking->event_date ? $booking->event_date->format('d/m/Y') : 'Non spécifiée' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <form action="{{ route('provider.bookings.confirm', $booking->id) }}" method="POST">
                                @csrf
                                <button type="submit" class="btn btn-success">
                                    <i class="fas fa-check me-2"></i>Confirmer la réservation
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        @if($booking->status === 'confirmed')
            <!-- Complete Modal -->
            <div class="modal fade" id="completeModal{{ $booking->id }}" tabindex="-1" aria-labelledby="completeModalLabel{{ $booking->id }}" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header bg-info text-white">
                            <h5 class="modal-title" id="completeModalLabel{{ $booking->id }}">
                                <i class="fas fa-check-double me-2"></i>Marquer comme terminée
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p>Êtes-vous sûr de vouloir marquer cette réservation comme terminée ?</p>
                            <p class="text-muted small">Cette action indique que vous avez fourni tous les services demandés.</p>
                            
                            <div class="card bg-light mb-3">
                                <div class="card-body">
                                    <h6 class="card-title">{{ $booking->providerService->title }}</h6>
                                    <p class="card-text small mb-2">Réservation #{{ $booking->booking_number }}</p>
                                    <div class="d-flex justify-content-between">
                                        <span><strong>Client:</strong> {{ $booking->client->user->name }}</span>
                                        <span><strong>Date:</strong> {{ $booking->event_date ? $booking->event_date->format('d/m/Y') : 'Non spécifiée' }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <form action="{{ route('provider.bookings.complete', $booking->id) }}" method="POST">
                                @csrf
                                <button type="submit" class="btn btn-info">
                                    <i class="fas fa-check-double me-2"></i>Marquer comme terminée
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        @endif

        @if(in_array($booking->status, ['pending', 'confirmed']))
            <!-- Cancel Modal -->
            <div class="modal fade" id="cancelModal{{ $booking->id }}" tabindex="-1" aria-labelledby="cancelModalLabel{{ $booking->id }}" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header bg-danger text-white">
                            <h5 class="modal-title" id="cancelModalLabel{{ $booking->id }}">
                                <i class="fas fa-times-circle me-2"></i>Annuler la réservation
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <form action="{{ route('provider.bookings.cancel', $booking->id) }}" method="POST">
                            @csrf
                            <div class="modal-body">
                                <div class="alert alert-warning">
                                    <div class="d-flex">
                                        <div class="me-3">
                                            <i class="fas fa-exclamation-triangle fa-2x text-warning"></i>
                                        </div>
                                        <div>
                                            <h5 class="alert-heading">Confirmation d'annulation</h5>
                                            <p>Êtes-vous sûr de vouloir annuler cette réservation ? Cette action est irréversible.</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="card bg-light mb-4">
                                    <div class="card-body">
                                        <h6 class="card-title">{{ $booking->providerService->title }}</h6>
                                        <p class="card-text small mb-2">Réservation #{{ $booking->booking_number }}</p>
                                        <div class="d-flex justify-content-between">
                                            <span><strong>Client:</strong> {{ $booking->client->user->name }}</span>
                                            <span><strong>Date:</strong> {{ $booking->event_date ? $booking->event_date->format('d/m/Y') : 'Non spécifiée' }}</span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mb-3">
                                    <label for="cancellation_reason" class="form-label">Raison de l'annulation</label>
                                    <textarea class="form-control" id="cancellation_reason" name="cancellation_reason" rows="3" placeholder="Expliquez pourquoi vous annulez cette réservation..." required></textarea>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-times me-2"></i>Confirmer l'annulation
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        @endif
    @endforeach
@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Filtrage par onglets
        const bookingTabs = document.querySelectorAll('#bookingTabs .nav-link');
        const bookingRows = document.querySelectorAll('.booking-row');
        
        bookingTabs.forEach(tab => {
            tab.addEventListener('shown.bs.tab', function(e) {
                const targetId = e.target.id;
                const status = targetId.replace('-tab', '');
                
                if (status === 'all') {
                    bookingRows.forEach(row => {
                        row.style.display = '';
                    });
                } else {
                    bookingRows.forEach(row => {
                        if (row.dataset.status === status) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    });
                }
            });
        });
        
        // Recherche
        const searchInput = document.getElementById('search-input');
        const searchButton = document.getElementById('search-button');
        
        function performSearch() {
            const searchTerm = searchInput.value.toLowerCase();
            
            bookingRows.forEach(row => {
                const text = row.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    row.style.display = '';
                } else {
                    row.style.display = 'none';
                }
            });
        }
        
        if (searchButton) {
            searchButton.addEventListener('click', performSearch);
        }
        
        if (searchInput) {
            searchInput.addEventListener('keyup', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });
        }
        
        // Filtrage
        const filterItems = document.querySelectorAll('.filter-item');
        
        filterItems.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                
                const filter = this.dataset.filter;
                
                bookingRows.forEach(row => {
                    if (filter === 'all') {
                        row.style.display = '';
                    } else {
                        if (row.dataset.status === filter) {
                            row.style.display = '';
                        } else {
                            row.style.display = 'none';
                        }
                    }
                });
            });
        });
    });
</script>
@endpush
