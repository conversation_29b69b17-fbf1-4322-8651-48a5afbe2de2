<?php

use Illuminate\Support\Facades\Route;
use App\Tsiky;
use App\Experience;
use App\Certification;
use App\Skill;
use App\Education;


/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {

    $myselves = Tsiky::all();
    $myexperiences = Experience::all();
    $mycertifications = Certification::all();
    $myskills = Skill::all();
    $myeducations = Education::all();

    return view('welcome', compact('myselves', 'myexperiences', 'mycertifications', 'myskills', 'myeducations'));
});


Route::group(['prefix' => 'admin'], function () {
    Voyager::routes();
});
