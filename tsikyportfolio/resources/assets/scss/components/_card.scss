.card {
    margin-bottom: 30px;


    .card-header {
        background: transparent;
        border: none;

        &.has-icon {

        	i {
        		background: $white;
        		position: absolute;
		        top: -28px;
		        border: 7px solid white;
		        overflow: hidden !important;
		        font-size: 35px;
        	}
        }
    }
    .card-body {
        font-size: 14px;
        color: $gray-600;
    }
}

.price-card {
	padding: 30px;
	border: 1px solid $border-color;

	.price-card-title {
		font-size: 30px;
		margin-bottom: 20px;
		opacity: .8;
	}

	.price-card-cost {
		position: relative;
		margin-bottom: 30px;
		opacity: .8;

		sup {
			font-size: 18px;
			position: relative;
			top: -35px;
		}

		.num {
			font-size: 55px;
			font-weight: bold;
			margin: 0 -2px 0 -7px;
		}

		.date {
			font-size: 12px;

			&:before {
				content: '/';
				padding-right: 2px;
			}
		}
	}

	.list {
		list-style-type: none;
		padding-left: 0;
		margin-bottom: 50px;

		.list-item {
			margin: 12px 0;
		}
	}


	&.price-card-requried {
		position: relative;
		overflow: hidden;
		padding: 50px 30px;

		&:after {
			content: 'New';
			position: absolute;
			right: -25px;
			top: 10px;
			padding: 6px 10px;
			width: 100px;
			background: $primary;
			color: $white;
			font-weight: 400;
			transform: rotate(45deg);
			font-size: 12px;

		}

		.num {
			color: $primary;
		}
	}
}



.portfolio {

    .filters {       
        border-bottom: 2px solid $border-color;
        border-radius: 0;
        padding-bottom: 4px;
        margin: 0 auto 20px;
        max-width: 400px;
        display: flex;
        justify-content: space-between;

        a {
			flex-basis: 0;
			display: block;
        	color: $body-color;
        	font-size: 13px;
        	font-weight: 500;
        	opacity: .7;
        	padding: 4px 10px;
        	@include custom-transition(.3);

        	&:first-child {
        		margin-left: 0;
        	}

        	&:last-child {
        		margin-right: 0;
        	}

        	&:hover {
        		opacity: .9;
        	}

        	&.active {
        		opacity: 1;
        		background: $primary;
        		color: $white;
        	}
        }

    }

    .portfolio-container {

        .portfolio-item { 
        	position: relative;
			padding-left: 0;
			padding-right: 0;
			margin: 10px -5px;
			overflow: hidden;
			border-radius: 2px;
			backface-visibility: hidden;

			.content-holder {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				width: 100%;
				height: 100%;
				background: rgba($primary,.8);
				display: flex;
				align-items: center;
				justify-content: center;
				text-align: center;
				padding: 0 20px;
				color: $white;
				opacity: 0;
				visibility: hidden;
				@include custom-transition(.5);


				.title {
					font-size: 22px;
					font-weight: 600;

				}

				.subtitle {
					font-weight: 500;
					opacity: 1;
					color: $white;
				}

			}

			&:hover {

				.content-holder {
					opacity: 1;
					visibility: visible;
				}
			}
        }
    }
}

/* blog wrapper */
.blog-card {
	display: flex;
	text-align: left;
	border: 1px solid $border-color;
	margin-bottom: 30px;
	overflow: hidden;
	margin: 0 15px 30px;


	.img-holder {
		min-width: 350px;
		max-width: 350px;

		img {
			width: 100%;
			height: 100%;
			object-fit: cover;
		}
	}

	.content-holder {
		padding: 30px 20px 30px 30px;

		.title {
			font-weight: 500;
			font-size: 25px;
			opacity: .8;
		}

		.post-details {
			margin: 15px 0 15px !important;

			i {
				display: inline-block;
				width: 15px;
			}

			a {
				margin-right: 10px;;
				color: #444;
				text-decoration: none;
				display: inline-block;
				font-weight: 600;
				opacity: .7;
				font-size: 13px;
			}
		}

		.read-more {
			text-decoration: none;
			margin-top: 20px;
			display: block;
			font-size: 13px;
			i {
				position: relative;
				top: 1px;				
			}
		}
			
	}
}
@include media-breakpoint-down (md) {
	.blog-card {
		flex-direction: column;		

		.img-holder {
			width: 100%;
			max-width: 100%;
			min-width: 100%;
		}
	}
}

.custom-map {
	height: 400px;
}
