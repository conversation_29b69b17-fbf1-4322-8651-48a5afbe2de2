[2024-05-27 09:29:03] production.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 09:29:50] production.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 09:30:40] production.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:03:12] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:08:23] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:08:24] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:09:11] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:09:11] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#19 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#20 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(740): Illuminate\\Routing\\Router->runRoute()
#21 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#22 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#23 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#24 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#26 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#27 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#29 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#30 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#32 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#34 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#36 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#38 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#40 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#41 /var/www/tsikyportfolio/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#42 {main}
"} 
[2024-05-27 11:09:11] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(239): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(203): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware()
#18 /var/www/tsikyportfolio/public/index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate()
#19 {main}
"} 
[2024-05-27 11:09:12] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:09:14] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:09:15] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:09:15] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#19 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#20 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(740): Illuminate\\Routing\\Router->runRoute()
#21 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#22 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#23 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#24 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#26 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#27 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#29 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#30 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#32 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#34 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#36 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#38 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#40 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#41 /var/www/tsikyportfolio/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#42 {main}
"} 
[2024-05-27 11:09:15] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(239): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(203): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware()
#18 /var/www/tsikyportfolio/public/index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate()
#19 {main}
"} 
[2024-05-27 11:09:15] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:09:20] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:09:25] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:09:25] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#19 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#20 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(740): Illuminate\\Routing\\Router->runRoute()
#21 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#22 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#23 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#24 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#26 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#27 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#29 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#30 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#32 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#34 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#36 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#38 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#40 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#41 /var/www/tsikyportfolio/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#42 {main}
"} 
[2024-05-27 11:09:25] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(239): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(203): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware()
#18 /var/www/tsikyportfolio/public/index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate()
#19 {main}
"} 
[2024-05-27 11:09:25] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:09:28] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:09:29] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:09:29] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#19 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#20 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(740): Illuminate\\Routing\\Router->runRoute()
#21 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#22 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#23 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#24 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#26 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#27 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#29 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#30 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#32 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#34 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#36 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#38 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#40 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#41 /var/www/tsikyportfolio/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#42 {main}
"} 
[2024-05-27 11:09:29] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(239): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(203): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware()
#18 /var/www/tsikyportfolio/public/index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate()
#19 {main}
"} 
[2024-05-27 11:09:29] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:09:48] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:09:49] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#19 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#20 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(740): Illuminate\\Routing\\Router->runRoute()
#21 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#22 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#23 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#24 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#26 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#27 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#29 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#30 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#32 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#34 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#36 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#38 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#40 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#41 /var/www/tsikyportfolio/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#42 {main}
"} 
[2024-05-27 11:09:49] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(239): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(203): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware()
#18 /var/www/tsikyportfolio/public/index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate()
#19 {main}
"} 
[2024-05-27 11:09:49] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:09:51] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:09:54] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:09:54] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#19 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#20 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(740): Illuminate\\Routing\\Router->runRoute()
#21 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#22 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#23 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#24 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#26 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#27 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#29 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#30 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#32 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#34 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#36 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#38 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#40 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#41 /var/www/tsikyportfolio/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#42 {main}
"} 
[2024-05-27 11:09:55] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(239): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(203): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware()
#18 /var/www/tsikyportfolio/public/index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate()
#19 {main}
"} 
[2024-05-27 11:09:55] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:10:01] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:10:02] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:10:02] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#19 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#20 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(740): Illuminate\\Routing\\Router->runRoute()
#21 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#22 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#23 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#24 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#26 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#27 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#29 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#30 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#32 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#34 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#36 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#38 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#40 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#41 /var/www/tsikyportfolio/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#42 {main}
"} 
[2024-05-27 11:10:02] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(239): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(203): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware()
#18 /var/www/tsikyportfolio/public/index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate()
#19 {main}
"} 
[2024-05-27 11:10:03] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:10:30] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:10:30] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#19 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#20 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(740): Illuminate\\Routing\\Router->runRoute()
#21 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#22 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#23 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#24 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#26 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#27 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#29 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#30 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#32 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#34 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#36 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#38 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#40 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#41 /var/www/tsikyportfolio/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#42 {main}
"} 
[2024-05-27 11:10:30] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(239): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(203): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware()
#18 /var/www/tsikyportfolio/public/index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate()
#19 {main}
"} 
[2024-05-27 11:10:31] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:10:32] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:10:33] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:10:33] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#19 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#20 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(740): Illuminate\\Routing\\Router->runRoute()
#21 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#22 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#23 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#24 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#26 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#27 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#29 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#30 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#32 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#34 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#36 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#38 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#40 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#41 /var/www/tsikyportfolio/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#42 {main}
"} 
[2024-05-27 11:10:33] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(239): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(203): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware()
#18 /var/www/tsikyportfolio/public/index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate()
#19 {main}
"} 
[2024-05-27 11:10:33] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:10:38] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:10:39] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:10:39] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#19 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#20 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(740): Illuminate\\Routing\\Router->runRoute()
#21 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#22 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#23 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#24 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#26 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#27 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#29 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#30 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#32 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#34 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#36 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#38 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#40 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#41 /var/www/tsikyportfolio/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#42 {main}
"} 
[2024-05-27 11:10:39] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(239): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(203): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware()
#18 /var/www/tsikyportfolio/public/index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate()
#19 {main}
"} 
[2024-05-27 11:10:39] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:11:30] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:11:30] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#19 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#20 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(740): Illuminate\\Routing\\Router->runRoute()
#21 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#22 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#23 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#24 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#26 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#27 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#29 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#30 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#32 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#34 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#36 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#38 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#40 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#41 /var/www/tsikyportfolio/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#42 {main}
"} 
[2024-05-27 11:11:30] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(239): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(203): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware()
#18 /var/www/tsikyportfolio/public/index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate()
#19 {main}
"} 
[2024-05-27 11:11:31] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:11:32] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:11:33] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:11:33] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#19 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#20 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(740): Illuminate\\Routing\\Router->runRoute()
#21 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#22 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#23 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#24 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#26 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#27 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#29 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#30 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#32 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#34 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#36 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#38 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#40 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#41 /var/www/tsikyportfolio/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#42 {main}
"} 
[2024-05-27 11:11:33] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(239): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(203): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware()
#18 /var/www/tsikyportfolio/public/index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate()
#19 {main}
"} 
[2024-05-27 11:11:34] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:11:37] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:12:48] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:12:48] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(169): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#19 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#20 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(740): Illuminate\\Routing\\Router->runRoute()
#21 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#22 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#23 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#24 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#26 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#27 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#29 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#30 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#32 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#34 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#36 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#38 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#40 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#41 /var/www/tsikyportfolio/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#42 {main}
"} 
[2024-05-27 11:12:48] local.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php:79)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/helpers.php(306): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(81): tap()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Encryption/EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(885): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(1043): Illuminate\\Foundation\\Application->make()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(959): Illuminate\\Container\\Container->resolveClass()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(920): Illuminate\\Container\\Container->resolveDependencies()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(770): Illuminate\\Container\\Container->build()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(856): Illuminate\\Container\\Container->resolve()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Container/Container.php(706): Illuminate\\Foundation\\Application->resolve()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(841): Illuminate\\Container\\Container->make()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(239): Illuminate\\Foundation\\Application->make()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(203): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware()
#18 /var/www/tsikyportfolio/public/index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate()
#19 {main}
"} 
[2024-05-27 11:12:49] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:12:51] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:14:04] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:14:15] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:14:15] local.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'tsiky'@'localhost' (using password: YES) (SQL: select * from `tsikys`) {"exception":"[object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'tsiky'@'localhost' (using password: YES) (SQL: select * from `tsikys`) at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connection.php:760)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connection.php(422): Illuminate\\Database\\Connection->run()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2706): Illuminate\\Database\\Connection->select()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(674): Illuminate\\Database\\Eloquent\\Builder->get()
#9 /var/www/tsikyportfolio/routes/web.php(24): Illuminate\\Database\\Eloquent\\Model::all()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php(40): Illuminate\\Routing\\RouteFileRegistrar->{closure}()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Route.php(237): Illuminate\\Routing\\CallableDispatcher->dispatch()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Route.php(208): Illuminate\\Routing\\Route->runCallable()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(798): Illuminate\\Routing\\Route->run()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#19 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#21 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#23 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle()
#24 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#26 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#28 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#30 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#31 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(740): Illuminate\\Routing\\Router->runRoute()
#32 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#33 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#34 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#35 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#38 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#41 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#43 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#45 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#47 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#49 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#51 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#52 /var/www/tsikyportfolio/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#53 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'tsiky'@'localhost' (using password: YES) at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:70)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(70): PDO->__construct()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(46): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1181): call_user_func()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1217): Illuminate\\Database\\Connection->getPdo()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connection.php(486): Illuminate\\Database\\Connection->getReadPdo()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->getPdoForSelect()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connection.php(422): Illuminate\\Database\\Connection->run()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2706): Illuminate\\Database\\Connection->select()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#18 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Model.php(674): Illuminate\\Database\\Eloquent\\Builder->get()
#19 /var/www/tsikyportfolio/routes/web.php(24): Illuminate\\Database\\Eloquent\\Model::all()
#20 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/CallableDispatcher.php(40): Illuminate\\Routing\\RouteFileRegistrar->{closure}()
#21 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Route.php(237): Illuminate\\Routing\\CallableDispatcher->dispatch()
#22 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Route.php(208): Illuminate\\Routing\\Route->runCallable()
#23 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(798): Illuminate\\Routing\\Route->run()
#24 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#25 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#26 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#27 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#28 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#29 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#30 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#31 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#32 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#33 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle()
#34 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#36 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#38 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#40 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#41 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(740): Illuminate\\Routing\\Router->runRoute()
#42 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#43 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#44 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#45 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#47 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#48 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#50 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#51 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#53 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#55 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#57 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#58 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#59 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#60 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#61 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#62 /var/www/tsikyportfolio/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#63 {main}
"} 
[2024-05-27 11:14:43] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:14:43] local.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2024-05-27 11:14:43] local.ERROR: SQLSTATE[HY000] [1045] Access denied for user 'tsiky'@'localhost' (using password: YES) (SQL: select * from `settings` order by `order` asc) {"view":{"view":"/var/www/tsikyportfolio/vendor/tcg/voyager/resources/views/auth/master.blade.php","data":[]},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): SQLSTATE[HY000] [1045] Access denied for user 'tsiky'@'localhost' (using password: YES) (SQL: select * from `settings` order by `order` asc) at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connection.php:760)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connection.php(422): Illuminate\\Database\\Connection->run()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2706): Illuminate\\Database\\Connection->select()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 /var/www/tsikyportfolio/vendor/tcg/voyager/src/Voyager.php(242): Illuminate\\Database\\Eloquent\\Builder->get()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(338): TCG\\Voyager\\Voyager->setting()
#10 /var/www/tsikyportfolio/vendor/tcg/voyager/resources/views/auth/master.blade.php(9): Illuminate\\Support\\Facades\\Facade::__callStatic()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(109): require('...')
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(178): Illuminate\\View\\View->getContents()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(147): Illuminate\\View\\View->renderContents()
#18 /var/www/tsikyportfolio/vendor/tcg/voyager/resources/views/login.blade.php(86): Illuminate\\View\\View->render()
#19 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(109): require('...')
#20 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#21 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#22 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#23 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get()
#24 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(178): Illuminate\\View\\View->getContents()
#25 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(147): Illuminate\\View\\View->renderContents()
#26 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#27 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent()
#28 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(906): Illuminate\\Http\\Response->__construct()
#29 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(875): Illuminate\\Routing\\Router::toResponse()
#30 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(798): Illuminate\\Routing\\Router->prepareResponse()
#31 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#32 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#34 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#36 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#38 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#40 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle()
#41 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#43 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#45 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#47 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#48 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(740): Illuminate\\Routing\\Router->runRoute()
#49 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#50 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#51 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#52 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#54 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#55 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#57 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#58 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#60 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#62 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#63 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#64 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#65 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#66 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#67 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#68 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#69 /var/www/tsikyportfolio/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#70 {main}

[previous exception] [object] (Illuminate\\Database\\QueryException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'tsiky'@'localhost' (using password: YES) (SQL: select * from `settings` order by `order` asc) at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connection.php:760)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connection.php(422): Illuminate\\Database\\Connection->run()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2706): Illuminate\\Database\\Connection->select()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#8 /var/www/tsikyportfolio/vendor/tcg/voyager/src/Voyager.php(242): Illuminate\\Database\\Eloquent\\Builder->get()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(338): TCG\\Voyager\\Voyager->setting()
#10 /var/www/tsikyportfolio/storage/framework/views/f3ab99542ba791e34ff0ac10544d65aa29186611.php(9): Illuminate\\Support\\Facades\\Facade::__callStatic()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(109): require('...')
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(178): Illuminate\\View\\View->getContents()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(147): Illuminate\\View\\View->renderContents()
#18 /var/www/tsikyportfolio/storage/framework/views/7be212437dead1a17b85e590aad42d86528057a3.php(87): Illuminate\\View\\View->render()
#19 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(109): require('...')
#20 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#21 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#22 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#23 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get()
#24 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(178): Illuminate\\View\\View->getContents()
#25 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(147): Illuminate\\View\\View->renderContents()
#26 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#27 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent()
#28 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(906): Illuminate\\Http\\Response->__construct()
#29 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(875): Illuminate\\Routing\\Router::toResponse()
#30 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(798): Illuminate\\Routing\\Router->prepareResponse()
#31 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#32 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#34 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#35 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#36 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#37 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#38 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#40 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle()
#41 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#43 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#45 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#47 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#48 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(740): Illuminate\\Routing\\Router->runRoute()
#49 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#50 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#51 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#52 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#53 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#54 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#55 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#57 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#58 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#59 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#60 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#61 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#62 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#63 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#64 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#65 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#66 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#67 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#68 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#69 /var/www/tsikyportfolio/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#70 {main}

[previous exception] [object] (PDOException(code: 1045): SQLSTATE[HY000] [1045] Access denied for user 'tsiky'@'localhost' (using password: YES) at /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php:70)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(70): PDO->__construct()
#1 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connectors/Connector.php(46): Illuminate\\Database\\Connectors\\Connector->createPdoConnection()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connectors/MySqlConnector.php(24): Illuminate\\Database\\Connectors\\Connector->createConnection()
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connectors/ConnectionFactory.php(184): Illuminate\\Database\\Connectors\\MySqlConnector->connect()
#4 [internal function]: Illuminate\\Database\\Connectors\\ConnectionFactory->Illuminate\\Database\\Connectors\\{closure}()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1181): call_user_func()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connection.php(1217): Illuminate\\Database\\Connection->getPdo()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connection.php(486): Illuminate\\Database\\Connection->getReadPdo()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connection.php(414): Illuminate\\Database\\Connection->getPdoForSelect()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connection.php(753): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connection.php(720): Illuminate\\Database\\Connection->runQueryCallback()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Connection.php(422): Illuminate\\Database\\Connection->run()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2706): Illuminate\\Database\\Connection->select()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2694): Illuminate\\Database\\Query\\Builder->runSelect()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(3230): Illuminate\\Database\\Query\\Builder->Illuminate\\Database\\Query\\{closure}()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Query/Builder.php(2695): Illuminate\\Database\\Query\\Builder->onceWithColumns()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(710): Illuminate\\Database\\Query\\Builder->get()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Builder.php(694): Illuminate\\Database\\Eloquent\\Builder->getModels()
#18 /var/www/tsikyportfolio/vendor/tcg/voyager/src/Voyager.php(242): Illuminate\\Database\\Eloquent\\Builder->get()
#19 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Support/Facades/Facade.php(338): TCG\\Voyager\\Voyager->setting()
#20 /var/www/tsikyportfolio/storage/framework/views/f3ab99542ba791e34ff0ac10544d65aa29186611.php(9): Illuminate\\Support\\Facades\\Facade::__callStatic()
#21 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(109): require('...')
#22 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#23 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#24 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#25 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get()
#26 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(178): Illuminate\\View\\View->getContents()
#27 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(147): Illuminate\\View\\View->renderContents()
#28 /var/www/tsikyportfolio/storage/framework/views/7be212437dead1a17b85e590aad42d86528057a3.php(87): Illuminate\\View\\View->render()
#29 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(109): require('...')
#30 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#31 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#32 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#33 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get()
#34 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(178): Illuminate\\View\\View->getContents()
#35 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(147): Illuminate\\View\\View->renderContents()
#36 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#37 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent()
#38 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(906): Illuminate\\Http\\Response->__construct()
#39 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(875): Illuminate\\Routing\\Router::toResponse()
#40 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(798): Illuminate\\Routing\\Router->prepareResponse()
#41 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#42 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#43 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#44 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#45 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#46 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#47 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#48 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#49 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#50 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle()
#51 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#52 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#53 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#54 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#55 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#56 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#57 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#58 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(740): Illuminate\\Routing\\Router->runRoute()
#59 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#60 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#61 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#62 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#63 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#64 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#65 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#66 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#67 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#68 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#69 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#70 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#71 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#72 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#73 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#74 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#75 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#76 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#77 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#78 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#79 /var/www/tsikyportfolio/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#80 {main}
"} 
[2024-05-27 11:21:20] local.ERROR: Undefined variable $myself {"view":{"view":"/var/www/tsikyportfolio/resources/views/welcome.blade.php","data":[]},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Undefined variable $myself at /var/www/tsikyportfolio/resources/views/welcome.blade.php:94)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php(270): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 /var/www/tsikyportfolio/resources/views/welcome.blade.php(94): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(109): require('...')
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(178): Illuminate\\View\\View->getContents()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(147): Illuminate\\View\\View->renderContents()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(906): Illuminate\\Http\\Response->__construct()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(875): Illuminate\\Routing\\Router::toResponse()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(798): Illuminate\\Routing\\Router->prepareResponse()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#19 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#21 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#23 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle()
#24 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#26 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#28 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#30 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#31 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(740): Illuminate\\Routing\\Router->runRoute()
#32 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#33 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#34 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#35 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#38 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#41 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#43 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#45 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#47 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#49 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#51 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#52 /var/www/tsikyportfolio/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#53 {main}

[previous exception] [object] (ErrorException(code: 0): Undefined variable $myself at /var/www/tsikyportfolio/storage/framework/views/43af8e5e06b758e94a91bf1bebb94fbe387ffdd2.php:95)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php(270): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 /var/www/tsikyportfolio/storage/framework/views/43af8e5e06b758e94a91bf1bebb94fbe387ffdd2.php(95): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(109): require('...')
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(178): Illuminate\\View\\View->getContents()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(147): Illuminate\\View\\View->renderContents()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(906): Illuminate\\Http\\Response->__construct()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(875): Illuminate\\Routing\\Router::toResponse()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(798): Illuminate\\Routing\\Router->prepareResponse()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#19 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#21 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#23 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle()
#24 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#26 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#28 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#30 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#31 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(740): Illuminate\\Routing\\Router->runRoute()
#32 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#33 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#34 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#35 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#38 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#41 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#43 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#45 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#47 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#49 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#51 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#52 /var/www/tsikyportfolio/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#53 {main}
"} 
[2024-05-27 11:21:31] local.ERROR: Undefined variable $myself {"view":{"view":"/var/www/tsikyportfolio/resources/views/welcome.blade.php","data":[]},"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Undefined variable $myself at /var/www/tsikyportfolio/resources/views/welcome.blade.php:94)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php(270): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 /var/www/tsikyportfolio/resources/views/welcome.blade.php(94): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(109): require('...')
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(178): Illuminate\\View\\View->getContents()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(147): Illuminate\\View\\View->renderContents()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(906): Illuminate\\Http\\Response->__construct()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(875): Illuminate\\Routing\\Router::toResponse()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(798): Illuminate\\Routing\\Router->prepareResponse()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#19 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#21 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#23 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle()
#24 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#26 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#28 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#30 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#31 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(740): Illuminate\\Routing\\Router->runRoute()
#32 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#33 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#34 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#35 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#38 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#41 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#43 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#45 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#47 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#49 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#51 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#52 /var/www/tsikyportfolio/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#53 {main}

[previous exception] [object] (ErrorException(code: 0): Undefined variable $myself at /var/www/tsikyportfolio/storage/framework/views/43af8e5e06b758e94a91bf1bebb94fbe387ffdd2.php:95)
[stacktrace]
#0 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php(270): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 /var/www/tsikyportfolio/storage/framework/views/43af8e5e06b758e94a91bf1bebb94fbe387ffdd2.php(95): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}()
#2 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(109): require('...')
#3 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php(110): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire()
#5 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php(70): Illuminate\\View\\Engines\\PhpEngine->evaluatePath()
#6 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(195): Illuminate\\View\\Engines\\CompilerEngine->get()
#7 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(178): Illuminate\\View\\View->getContents()
#8 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/View.php(147): Illuminate\\View\\View->renderContents()
#9 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Response.php(69): Illuminate\\View\\View->render()
#10 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Response.php(35): Illuminate\\Http\\Response->setContent()
#11 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(906): Illuminate\\Http\\Response->__construct()
#12 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(875): Illuminate\\Routing\\Router::toResponse()
#13 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(798): Illuminate\\Routing\\Router->prepareResponse()
#14 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#15 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#16 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#17 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#18 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle()
#19 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle()
#21 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#22 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest()
#23 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle()
#24 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle()
#26 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle()
#28 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(799): Illuminate\\Pipeline\\Pipeline->then()
#30 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(776): Illuminate\\Routing\\Router->runRouteWithinStack()
#31 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(740): Illuminate\\Routing\\Router->runRoute()
#32 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Routing/Router.php(729): Illuminate\\Routing\\Router->dispatchToRoute()
#33 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(190): Illuminate\\Routing\\Router->dispatch()
#34 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#35 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#36 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#37 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#38 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#39 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#40 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#41 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#42 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle()
#43 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#44 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#45 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#46 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle()
#47 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#48 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#49 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#50 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(165): Illuminate\\Pipeline\\Pipeline->then()
#51 /var/www/tsikyportfolio/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(134): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#52 /var/www/tsikyportfolio/public/index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle()
#53 {main}
"} 
[2025-06-12 07:47:28] production.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
[2025-06-12 07:47:33] production.INFO: No database connection yet in VoyagerServiceProvider loadAuth(). No worries, this is not a problem!  
