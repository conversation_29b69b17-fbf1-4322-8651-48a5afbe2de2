1719400999O:23:"TCG\Voyager\Models\Menu":30:{s:8:" * table";s:5:"menus";s:10:" * guarded";a:0:{}s:13:" * connection";s:5:"mysql";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:4:{s:2:"id";i:1;s:4:"name";s:5:"admin";s:10:"created_at";s:19:"2023-12-14 15:38:00";s:10:"updated_at";s:19:"2023-12-14 15:38:00";}s:11:" * original";a:4:{s:2:"id";i:1;s:4:"name";s:5:"admin";s:10:"created_at";s:19:"2023-12-14 15:38:00";s:10:"updated_at";s:19:"2023-12-14 15:38:00";}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:12:"parent_items";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:14:{i:0;O:27:"TCG\Voyager\Models\MenuItem":32:{s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:8:" * table";s:10:"menu_items";s:10:" * guarded";a:0:{}s:15:" * translatable";a:1:{i:0;s:5:"title";}s:13:" * connection";s:5:"mysql";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:1;s:7:"menu_id";i:1;s:5:"title";s:9:"Dashboard";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-boat";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:1;s:10:"created_at";s:19:"2023-12-14 15:38:00";s:10:"updated_at";s:19:"2023-12-14 15:38:00";s:5:"route";s:17:"voyager.dashboard";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:1;s:7:"menu_id";i:1;s:5:"title";s:9:"Dashboard";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-boat";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:1;s:10:"created_at";s:19:"2023-12-14 15:38:00";s:10:"updated_at";s:19:"2023-12-14 15:38:00";s:5:"route";s:17:"voyager.dashboard";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}}i:1;O:27:"TCG\Voyager\Models\MenuItem":32:{s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:8:" * table";s:10:"menu_items";s:10:" * guarded";a:0:{}s:15:" * translatable";a:1:{i:0;s:5:"title";}s:13:" * connection";s:5:"mysql";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:2;s:7:"menu_id";i:1;s:5:"title";s:5:"Media";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-images";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:5;s:10:"created_at";s:19:"2023-12-14 15:38:00";s:10:"updated_at";s:19:"2023-12-14 15:38:00";s:5:"route";s:19:"voyager.media.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:2;s:7:"menu_id";i:1;s:5:"title";s:5:"Media";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-images";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:5;s:10:"created_at";s:19:"2023-12-14 15:38:00";s:10:"updated_at";s:19:"2023-12-14 15:38:00";s:5:"route";s:19:"voyager.media.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}}i:2;O:27:"TCG\Voyager\Models\MenuItem":32:{s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:8:" * table";s:10:"menu_items";s:10:" * guarded";a:0:{}s:15:" * translatable";a:1:{i:0;s:5:"title";}s:13:" * connection";s:5:"mysql";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:3;s:7:"menu_id";i:1;s:5:"title";s:5:"Users";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-person";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:3;s:10:"created_at";s:19:"2023-12-14 15:38:00";s:10:"updated_at";s:19:"2023-12-14 15:38:00";s:5:"route";s:19:"voyager.users.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:3;s:7:"menu_id";i:1;s:5:"title";s:5:"Users";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:14:"voyager-person";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:3;s:10:"created_at";s:19:"2023-12-14 15:38:00";s:10:"updated_at";s:19:"2023-12-14 15:38:00";s:5:"route";s:19:"voyager.users.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}}i:3;O:27:"TCG\Voyager\Models\MenuItem":32:{s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:8:" * table";s:10:"menu_items";s:10:" * guarded";a:0:{}s:15:" * translatable";a:1:{i:0;s:5:"title";}s:13:" * connection";s:5:"mysql";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:4;s:7:"menu_id";i:1;s:5:"title";s:5:"Roles";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-lock";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:2;s:10:"created_at";s:19:"2023-12-14 15:38:00";s:10:"updated_at";s:19:"2023-12-14 15:38:00";s:5:"route";s:19:"voyager.roles.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:4;s:7:"menu_id";i:1;s:5:"title";s:5:"Roles";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-lock";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:2;s:10:"created_at";s:19:"2023-12-14 15:38:00";s:10:"updated_at";s:19:"2023-12-14 15:38:00";s:5:"route";s:19:"voyager.roles.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}}i:4;O:27:"TCG\Voyager\Models\MenuItem":32:{s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:8:" * table";s:10:"menu_items";s:10:" * guarded";a:0:{}s:15:" * translatable";a:1:{i:0;s:5:"title";}s:13:" * connection";s:5:"mysql";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:5;s:7:"menu_id";i:1;s:5:"title";s:5:"Tools";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:13:"voyager-tools";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:9;s:10:"created_at";s:19:"2023-12-14 15:38:00";s:10:"updated_at";s:19:"2023-12-14 15:38:00";s:5:"route";N;s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:5;s:7:"menu_id";i:1;s:5:"title";s:5:"Tools";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:13:"voyager-tools";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:9;s:10:"created_at";s:19:"2023-12-14 15:38:00";s:10:"updated_at";s:19:"2023-12-14 15:38:00";s:5:"route";N;s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:4:{i:0;O:27:"TCG\Voyager\Models\MenuItem":32:{s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:8:" * table";s:10:"menu_items";s:10:" * guarded";a:0:{}s:15:" * translatable";a:1:{i:0;s:5:"title";}s:13:" * connection";s:5:"mysql";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:6;s:7:"menu_id";i:1;s:5:"title";s:12:"Menu Builder";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-list";s:5:"color";N;s:9:"parent_id";i:5;s:5:"order";i:10;s:10:"created_at";s:19:"2023-12-14 15:38:00";s:10:"updated_at";s:19:"2023-12-14 15:38:00";s:5:"route";s:19:"voyager.menus.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:6;s:7:"menu_id";i:1;s:5:"title";s:12:"Menu Builder";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-list";s:5:"color";N;s:9:"parent_id";i:5;s:5:"order";i:10;s:10:"created_at";s:19:"2023-12-14 15:38:00";s:10:"updated_at";s:19:"2023-12-14 15:38:00";s:5:"route";s:19:"voyager.menus.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}}i:1;O:27:"TCG\Voyager\Models\MenuItem":32:{s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:8:" * table";s:10:"menu_items";s:10:" * guarded";a:0:{}s:15:" * translatable";a:1:{i:0;s:5:"title";}s:13:" * connection";s:5:"mysql";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:7;s:7:"menu_id";i:1;s:5:"title";s:8:"Database";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-data";s:5:"color";N;s:9:"parent_id";i:5;s:5:"order";i:11;s:10:"created_at";s:19:"2023-12-14 15:38:00";s:10:"updated_at";s:19:"2023-12-14 15:38:00";s:5:"route";s:22:"voyager.database.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:7;s:7:"menu_id";i:1;s:5:"title";s:8:"Database";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-data";s:5:"color";N;s:9:"parent_id";i:5;s:5:"order";i:11;s:10:"created_at";s:19:"2023-12-14 15:38:00";s:10:"updated_at";s:19:"2023-12-14 15:38:00";s:5:"route";s:22:"voyager.database.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}}i:2;O:27:"TCG\Voyager\Models\MenuItem":32:{s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:8:" * table";s:10:"menu_items";s:10:" * guarded";a:0:{}s:15:" * translatable";a:1:{i:0;s:5:"title";}s:13:" * connection";s:5:"mysql";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:8;s:7:"menu_id";i:1;s:5:"title";s:7:"Compass";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:15:"voyager-compass";s:5:"color";N;s:9:"parent_id";i:5;s:5:"order";i:12;s:10:"created_at";s:19:"2023-12-14 15:38:00";s:10:"updated_at";s:19:"2023-12-14 15:38:00";s:5:"route";s:21:"voyager.compass.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:8;s:7:"menu_id";i:1;s:5:"title";s:7:"Compass";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:15:"voyager-compass";s:5:"color";N;s:9:"parent_id";i:5;s:5:"order";i:12;s:10:"created_at";s:19:"2023-12-14 15:38:00";s:10:"updated_at";s:19:"2023-12-14 15:38:00";s:5:"route";s:21:"voyager.compass.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}}i:3;O:27:"TCG\Voyager\Models\MenuItem":32:{s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:8:" * table";s:10:"menu_items";s:10:" * guarded";a:0:{}s:15:" * translatable";a:1:{i:0;s:5:"title";}s:13:" * connection";s:5:"mysql";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:9;s:7:"menu_id";i:1;s:5:"title";s:5:"BREAD";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:13:"voyager-bread";s:5:"color";N;s:9:"parent_id";i:5;s:5:"order";i:13;s:10:"created_at";s:19:"2023-12-14 15:38:00";s:10:"updated_at";s:19:"2023-12-14 15:38:00";s:5:"route";s:19:"voyager.bread.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:9;s:7:"menu_id";i:1;s:5:"title";s:5:"BREAD";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:13:"voyager-bread";s:5:"color";N;s:9:"parent_id";i:5;s:5:"order";i:13;s:10:"created_at";s:19:"2023-12-14 15:38:00";s:10:"updated_at";s:19:"2023-12-14 15:38:00";s:5:"route";s:19:"voyager.bread.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}}i:5;O:27:"TCG\Voyager\Models\MenuItem":32:{s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:8:" * table";s:10:"menu_items";s:10:" * guarded";a:0:{}s:15:" * translatable";a:1:{i:0;s:5:"title";}s:13:" * connection";s:5:"mysql";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:10;s:7:"menu_id";i:1;s:5:"title";s:8:"Settings";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:16:"voyager-settings";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:14;s:10:"created_at";s:19:"2023-12-14 15:38:00";s:10:"updated_at";s:19:"2023-12-14 15:38:00";s:5:"route";s:22:"voyager.settings.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:10;s:7:"menu_id";i:1;s:5:"title";s:8:"Settings";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:16:"voyager-settings";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:14;s:10:"created_at";s:19:"2023-12-14 15:38:00";s:10:"updated_at";s:19:"2023-12-14 15:38:00";s:5:"route";s:22:"voyager.settings.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}}i:6;O:27:"TCG\Voyager\Models\MenuItem":32:{s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:8:" * table";s:10:"menu_items";s:10:" * guarded";a:0:{}s:15:" * translatable";a:1:{i:0;s:5:"title";}s:13:" * connection";s:5:"mysql";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:11;s:7:"menu_id";i:1;s:5:"title";s:10:"Categories";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:18:"voyager-categories";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:8;s:10:"created_at";s:19:"2023-12-14 15:38:03";s:10:"updated_at";s:19:"2023-12-14 15:38:03";s:5:"route";s:24:"voyager.categories.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:11;s:7:"menu_id";i:1;s:5:"title";s:10:"Categories";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:18:"voyager-categories";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:8;s:10:"created_at";s:19:"2023-12-14 15:38:03";s:10:"updated_at";s:19:"2023-12-14 15:38:03";s:5:"route";s:24:"voyager.categories.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}}i:7;O:27:"TCG\Voyager\Models\MenuItem":32:{s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:8:" * table";s:10:"menu_items";s:10:" * guarded";a:0:{}s:15:" * translatable";a:1:{i:0;s:5:"title";}s:13:" * connection";s:5:"mysql";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:12;s:7:"menu_id";i:1;s:5:"title";s:5:"Posts";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-news";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:6;s:10:"created_at";s:19:"2023-12-14 15:38:04";s:10:"updated_at";s:19:"2023-12-14 15:38:04";s:5:"route";s:19:"voyager.posts.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:12;s:7:"menu_id";i:1;s:5:"title";s:5:"Posts";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:12:"voyager-news";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:6;s:10:"created_at";s:19:"2023-12-14 15:38:04";s:10:"updated_at";s:19:"2023-12-14 15:38:04";s:5:"route";s:19:"voyager.posts.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}}i:8;O:27:"TCG\Voyager\Models\MenuItem":32:{s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:8:" * table";s:10:"menu_items";s:10:" * guarded";a:0:{}s:15:" * translatable";a:1:{i:0;s:5:"title";}s:13:" * connection";s:5:"mysql";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:13;s:7:"menu_id";i:1;s:5:"title";s:5:"Pages";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:17:"voyager-file-text";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:7;s:10:"created_at";s:19:"2023-12-14 15:38:05";s:10:"updated_at";s:19:"2023-12-14 15:38:05";s:5:"route";s:19:"voyager.pages.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:13;s:7:"menu_id";i:1;s:5:"title";s:5:"Pages";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";s:17:"voyager-file-text";s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:7;s:10:"created_at";s:19:"2023-12-14 15:38:05";s:10:"updated_at";s:19:"2023-12-14 15:38:05";s:5:"route";s:19:"voyager.pages.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}}i:9;O:27:"TCG\Voyager\Models\MenuItem":32:{s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:8:" * table";s:10:"menu_items";s:10:" * guarded";a:0:{}s:15:" * translatable";a:1:{i:0;s:5:"title";}s:13:" * connection";s:5:"mysql";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:14;s:7:"menu_id";i:1;s:5:"title";s:14:"Certifications";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";N;s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:15;s:10:"created_at";s:19:"2023-12-14 17:21:19";s:10:"updated_at";s:19:"2023-12-14 17:21:19";s:5:"route";s:28:"voyager.certifications.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:14;s:7:"menu_id";i:1;s:5:"title";s:14:"Certifications";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";N;s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:15;s:10:"created_at";s:19:"2023-12-14 17:21:19";s:10:"updated_at";s:19:"2023-12-14 17:21:19";s:5:"route";s:28:"voyager.certifications.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}}i:10;O:27:"TCG\Voyager\Models\MenuItem":32:{s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:8:" * table";s:10:"menu_items";s:10:" * guarded";a:0:{}s:15:" * translatable";a:1:{i:0;s:5:"title";}s:13:" * connection";s:5:"mysql";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:15;s:7:"menu_id";i:1;s:5:"title";s:9:"Education";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";N;s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:16;s:10:"created_at";s:19:"2023-12-14 17:22:26";s:10:"updated_at";s:19:"2023-12-14 17:22:26";s:5:"route";s:24:"voyager.educations.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:15;s:7:"menu_id";i:1;s:5:"title";s:9:"Education";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";N;s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:16;s:10:"created_at";s:19:"2023-12-14 17:22:26";s:10:"updated_at";s:19:"2023-12-14 17:22:26";s:5:"route";s:24:"voyager.educations.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}}i:11;O:27:"TCG\Voyager\Models\MenuItem":32:{s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:8:" * table";s:10:"menu_items";s:10:" * guarded";a:0:{}s:15:" * translatable";a:1:{i:0;s:5:"title";}s:13:" * connection";s:5:"mysql";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:16;s:7:"menu_id";i:1;s:5:"title";s:11:"Experiences";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";N;s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:17;s:10:"created_at";s:19:"2023-12-14 17:22:42";s:10:"updated_at";s:19:"2023-12-14 17:22:42";s:5:"route";s:25:"voyager.experiences.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:16;s:7:"menu_id";i:1;s:5:"title";s:11:"Experiences";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";N;s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:17;s:10:"created_at";s:19:"2023-12-14 17:22:42";s:10:"updated_at";s:19:"2023-12-14 17:22:42";s:5:"route";s:25:"voyager.experiences.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}}i:12;O:27:"TCG\Voyager\Models\MenuItem":32:{s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:8:" * table";s:10:"menu_items";s:10:" * guarded";a:0:{}s:15:" * translatable";a:1:{i:0;s:5:"title";}s:13:" * connection";s:5:"mysql";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:18;s:7:"menu_id";i:1;s:5:"title";s:6:"Skills";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";N;s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:19;s:10:"created_at";s:19:"2023-12-14 17:23:36";s:10:"updated_at";s:19:"2023-12-14 17:23:36";s:5:"route";s:20:"voyager.skills.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:18;s:7:"menu_id";i:1;s:5:"title";s:6:"Skills";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";N;s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:19;s:10:"created_at";s:19:"2023-12-14 17:23:36";s:10:"updated_at";s:19:"2023-12-14 17:23:36";s:5:"route";s:20:"voyager.skills.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}}i:13;O:27:"TCG\Voyager\Models\MenuItem":32:{s:20:" * translatorMethods";a:1:{s:4:"link";s:14:"translatorLink";}s:8:" * table";s:10:"menu_items";s:10:" * guarded";a:0:{}s:15:" * translatable";a:1:{i:0;s:5:"title";}s:13:" * connection";s:5:"mysql";s:13:" * primaryKey";s:2:"id";s:10:" * keyType";s:3:"int";s:12:"incrementing";b:1;s:7:" * with";a:0:{}s:12:" * withCount";a:0:{}s:19:"preventsLazyLoading";b:0;s:10:" * perPage";i:15;s:6:"exists";b:1;s:18:"wasRecentlyCreated";b:0;s:28:" * escapeWhenCastingToString";b:0;s:13:" * attributes";a:13:{s:2:"id";i:19;s:7:"menu_id";i:1;s:5:"title";s:7:"Tsikies";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";N;s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:20;s:10:"created_at";s:19:"2023-12-14 17:33:30";s:10:"updated_at";s:19:"2023-12-14 17:33:30";s:5:"route";s:20:"voyager.tsikys.index";s:10:"parameters";N;}s:11:" * original";a:13:{s:2:"id";i:19;s:7:"menu_id";i:1;s:5:"title";s:7:"Tsikies";s:3:"url";s:0:"";s:6:"target";s:5:"_self";s:10:"icon_class";N;s:5:"color";N;s:9:"parent_id";N;s:5:"order";i:20;s:10:"created_at";s:19:"2023-12-14 17:33:30";s:10:"updated_at";s:19:"2023-12-14 17:33:30";s:5:"route";s:20:"voyager.tsikys.index";s:10:"parameters";N;}s:10:" * changes";a:0:{}s:8:" * casts";a:0:{}s:17:" * classCastCache";a:0:{}s:21:" * attributeCastCache";a:0:{}s:8:" * dates";a:0:{}s:13:" * dateFormat";N;s:10:" * appends";a:0:{}s:19:" * dispatchesEvents";a:0:{}s:14:" * observables";a:0:{}s:12:" * relations";a:1:{s:8:"children";O:39:"Illuminate\Database\Eloquent\Collection":2:{s:8:" * items";a:0:{}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}}}s:28:" * escapeWhenCastingToString";b:0;}}s:10:" * touches";a:0:{}s:10:"timestamps";b:1;s:9:" * hidden";a:0:{}s:10:" * visible";a:0:{}s:11:" * fillable";a:0:{}}